<?php
class B2bGxptPurchaseOrderErpServiceDeliveryPurchaseOrderRequest
{


	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jingdong.b2b.gxpt.purchaseOrderErpService.deliveryPurchaseOrder";
	}
	
	public function getApiParas(){
	    if(empty($this->apiParas)){
            return "{}";
        }
        return json_encode($this->apiParas);
	}
	
	public function check(){
		
	}
	
	public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
                                                        		                                    	                        	                   			private $shipmentId;
    	                        
	public function setShipmentId($shipmentId){
		$this->shipmentId = $shipmentId;
         $this->apiParas["shipmentId"] = $shipmentId;
	}

	public function getShipmentId(){
	  return $this->shipmentId;
	}

                        	                   			private $venderId;
    	                        
	public function setVenderId($venderId){
		$this->venderId = $venderId;
         $this->apiParas["venderId"] = $venderId;
	}

	public function getVenderId(){
	  return $this->venderId;
	}

                        	                   			private $shipmentNo;
    	                        
	public function setShipmentNo($shipmentNo){
		$this->shipmentNo = $shipmentNo;
         $this->apiParas["shipmentNo"] = $shipmentNo;
	}

	public function getShipmentNo(){
	  return $this->shipmentNo;
	}

                        	                   			private $erpOrderId;
    	                        
	public function setErpOrderId($erpOrderId){
		$this->erpOrderId = $erpOrderId;
         $this->apiParas["erpOrderId"] = $erpOrderId;
	}

	public function getErpOrderId(){
	  return $this->erpOrderId;
	}

                        	                   			private $shipmentName;
    	                        
	public function setShipmentName($shipmentName){
		$this->shipmentName = $shipmentName;
         $this->apiParas["shipmentName"] = $shipmentName;
	}

	public function getShipmentName(){
	  return $this->shipmentName;
	}

                        	                   			private $distributionMode;
    	                        
	public function setDistributionMode($distributionMode){
		$this->distributionMode = $distributionMode;
         $this->apiParas["distributionMode"] = $distributionMode;
	}

	public function getDistributionMode(){
	  return $this->distributionMode;
	}

                            }





        
 

