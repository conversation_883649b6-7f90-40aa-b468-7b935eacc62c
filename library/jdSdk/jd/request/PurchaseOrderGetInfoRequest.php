<?php
class PurchaseOrderGetInfoRequest
{


	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jingdong.purchase.order.get.info";
	}
	
	public function getApiParas(){
	    if(empty($this->apiParas)){
            return "{}";
        }
        return json_encode($this->apiParas);
	}
	
	public function check(){
		
	}
	
	public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
                                    	                        	                   			private $purchaseId;
    	                        
	public function setPurchaseId($purchaseId){
		$this->purchaseId = $purchaseId;
         $this->apiParas["purchaseId"] = $purchaseId;
	}

	public function getPurchaseId(){
	  return $this->purchaseId;
	}

}





        
 

