<?php
class SmsJosCreateIsvSmsModelServiceRequest
{


	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jingdong.sms.jos.createIsvSmsModelService";
	}
	
	public function getApiParas(){
	    if(empty($this->apiParas)){
            return "{}";
        }
        return json_encode($this->apiParas);
	}
	
	public function check(){
		
	}
	
	public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
                                                        		                                    	                   			private $detail;
    	                        
	public function setDetail($detail){
		$this->detail = $detail;
         $this->apiParas["detail"] = $detail;
	}

	public function getDetail(){
	  return $this->detail;
	}

                        	                   			private $name;
    	                        
	public function setName($name){
		$this->name = $name;
         $this->apiParas["name"] = $name;
	}

	public function getName(){
	  return $this->name;
	}

                        	                   			private $isvAppKey;
    	                        
	public function setIsvAppKey($isvAppKey){
		$this->isvAppKey = $isvAppKey;
         $this->apiParas["isvAppKey"] = $isvAppKey;
	}

	public function getIsvAppKey(){
	  return $this->isvAppKey;
	}

                        	                   			private $modelTypeId;
    	                        
	public function setModelTypeId($modelTypeId){
		$this->modelTypeId = $modelTypeId;
         $this->apiParas["modelTypeId"] = $modelTypeId;
	}

	public function getModelTypeId(){
	  return $this->modelTypeId;
	}

                        	                   			private $operators;
    	                        
	public function setOperators($operators){
		$this->operators = $operators;
         $this->apiParas["operators"] = $operators;
	}

	public function getOperators(){
	  return $this->operators;
	}

                            }





        
 

