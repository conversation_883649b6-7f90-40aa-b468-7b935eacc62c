<?php
class FwbSpWorkorderGetRequest
{


	private $apiParas = array();
	
	public function getApiMethodName(){
	  return "jingdong.fwb.sp.workorder.get";
	}
	
	public function getApiParas(){
	    if(empty($this->apiParas)){
            return "{}";
        }
        return json_encode($this->apiParas);
	}
	
	public function check(){
		
	}
	
	public function putOtherTextParam($key, $value){
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}

    private $version;

    public function setVersion($version){
        $this->version = $version;
    }

    public function getVersion(){
        return $this->version;
    }
                                    	                        	                   			private $workOrderId;
    	                        
	public function setWorkOrderId($workOrderId){
		$this->workOrderId = $workOrderId;
         $this->apiParas["workOrderId"] = $workOrderId;
	}

	public function getWorkOrderId(){
	  return $this->workOrderId;
	}

}





        
 

