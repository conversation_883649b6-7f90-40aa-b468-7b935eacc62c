<?php
/**
 * TOP API: taobao.trade.invoice.amount.get request
 *
 * <AUTHOR> create
 * @since 1.0, 2022.08.29
 */
class TradeInvoiceAmountGetRequest
{
	/**
	 * 业务订单ID
	 **/
	private $tid;

	private $apiParas = array();

	public function setTid($tid)
	{
		$this->tid = $tid;
		$this->apiParas["tid"] = $tid;
	}

	public function getTid()
	{
		return $this->tid;
	}

	public function getApiMethodName()
	{
		return "taobao.trade.invoice.amount.get";
	}

	public function getApiParas()
	{
		return $this->apiParas;
	}

	public function check()
	{

		RequestCheckUtil::checkNotNull($this->tid,"tid");
	}

	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
