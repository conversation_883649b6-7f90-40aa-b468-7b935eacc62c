<?php
/**
 * TOP API: taobao.traderates.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2020.10.26
 */
class TraderatesGetRequest
{
	/** 
	 * 评价结束时间。如果只输入结束时间，那么全部返回所有评价数据。
	 **/
	private $endDate;
	
	/** 
	 * 需返回的字段列表。可选值：TradeRate 结构中的所有字段，多个字段之间用“,”分隔
	 **/
	private $fields;
	
	/** 
	 * 商品的数字ID
	 **/
	private $numIid;
	
	/** 
	 * 页码。取值范围:大于零的整数最大限制为200; 默认值:1
	 **/
	private $pageNo;
	
	/** 
	 * 每页获取条数。默认值40，最小值1，最大值150。
	 **/
	private $pageSize;
	
	/** 
	 * 评价对方昵称
	 **/
	private $peerNick;
	
	/** 
	 * 评价类型。可选值:get(得到),give(给出)
	 **/
	private $rateType;
	
	/** 
	 * 评价结果。可选值:good(好评),neutral(中评),bad(差评)
	 **/
	private $result;
	
	/** 
	 * 评价者角色即评价的发起方。可选值:seller(卖家),buyer(买家)。 当 give buyer 以买家身份给卖家的评价； 当 get seller 以买家身份得到卖家给的评价； 当 give seller 以卖家身份给买家的评价； 当 get buyer 以卖家身份得到买家给的评价。
	 **/
	private $role;
	
	/** 
	 * 评价开始时。如果只输入开始时间，那么能返回开始时间之后的评价数据。
	 **/
	private $startDate;
	
	/** 
	 * 交易订单id，可以是父订单id号，也可以是子订单id号
	 **/
	private $tid;
	
	/** 
	 * 是否启用has_next的分页方式，如果指定true,则返回的结果中不包含总记录数，但是会新增一个是否存在下一页的的字段，通过此种方式获取评价信息，效率在原有的基础上有80%的提升。
	 **/
	private $useHasNext;
	
	private $apiParas = array();
	
	public function setEndDate($endDate)
	{
		$this->endDate = $endDate;
		$this->apiParas["end_date"] = $endDate;
	}

	public function getEndDate()
	{
		return $this->endDate;
	}

	public function setFields($fields)
	{
		$this->fields = $fields;
		$this->apiParas["fields"] = $fields;
	}

	public function getFields()
	{
		return $this->fields;
	}

	public function setNumIid($numIid)
	{
		$this->numIid = $numIid;
		$this->apiParas["num_iid"] = $numIid;
	}

	public function getNumIid()
	{
		return $this->numIid;
	}

	public function setPageNo($pageNo)
	{
		$this->pageNo = $pageNo;
		$this->apiParas["page_no"] = $pageNo;
	}

	public function getPageNo()
	{
		return $this->pageNo;
	}

	public function setPageSize($pageSize)
	{
		$this->pageSize = $pageSize;
		$this->apiParas["page_size"] = $pageSize;
	}

	public function getPageSize()
	{
		return $this->pageSize;
	}

	public function setPeerNick($peerNick)
	{
		$this->peerNick = $peerNick;
		$this->apiParas["peer_nick"] = $peerNick;
	}

	public function getPeerNick()
	{
		return $this->peerNick;
	}

	public function setRateType($rateType)
	{
		$this->rateType = $rateType;
		$this->apiParas["rate_type"] = $rateType;
	}

	public function getRateType()
	{
		return $this->rateType;
	}

	public function setResult($result)
	{
		$this->result = $result;
		$this->apiParas["result"] = $result;
	}

	public function getResult()
	{
		return $this->result;
	}

	public function setRole($role)
	{
		$this->role = $role;
		$this->apiParas["role"] = $role;
	}

	public function getRole()
	{
		return $this->role;
	}

	public function setStartDate($startDate)
	{
		$this->startDate = $startDate;
		$this->apiParas["start_date"] = $startDate;
	}

	public function getStartDate()
	{
		return $this->startDate;
	}

	public function setTid($tid)
	{
		$this->tid = $tid;
		$this->apiParas["tid"] = $tid;
	}

	public function getTid()
	{
		return $this->tid;
	}

	public function setUseHasNext($useHasNext)
	{
		$this->useHasNext = $useHasNext;
		$this->apiParas["use_has_next"] = $useHasNext;
	}

	public function getUseHasNext()
	{
		return $this->useHasNext;
	}

	public function getApiMethodName()
	{
		return "taobao.traderates.get";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->fields,"fields");
		RequestCheckUtil::checkMaxListSize($this->fields,20,"fields");
		RequestCheckUtil::checkMaxValue($this->pageSize,150,"pageSize");
		RequestCheckUtil::checkMinValue($this->pageSize,1,"pageSize");
		RequestCheckUtil::checkMaxLength($this->peerNick,32,"peerNick");
		RequestCheckUtil::checkNotNull($this->rateType,"rateType");
		RequestCheckUtil::checkNotNull($this->role,"role");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
