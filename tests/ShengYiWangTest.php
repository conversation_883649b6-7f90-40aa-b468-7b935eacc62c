<?php

use App\Services\ShengYiWang\ShengYiWangClient;

class ShengYiWangTest extends TestCase
{
    public function testTaobaoWaybill()
    {

//渠道   配货编码
//PDD    B00422
//JD     B00420
//KUAISHOU B00419
//REDBOOK B00423
//TIKTOK B00424
//TAOBAO B00425-2

        $shengYiWangClient = new ShengYiWangClient();
        $params = [
//            'takeCode' => "B00416-11",
        ];
        $shengYiWangClient->setToken('e5af35c69d7a8ca2fa34f339587be5a2b13915c2503790971bfca16fc95b7b35d7078027f244d5ec51b3287cadfefca7');
        $shengYiWangClient->setUserId('4b89bd5f2f9e067f67da9aabf351799f');
        $response = $shengYiWangClient->execute('GET', '/third/print/shopInfo/getInfoList', $params);
        $shengYiWangClient->handleResponse($response);
        dd($response);
    }

    public function testaa()
    {
        $shengYiWangClient = new ShengYiWangClient();
        $params = [
            'shopOrderNos' => ["6940788636339738544"],
        ];
        $shengYiWangClient->setToken('3acfc94e2d66fedd8820e09e1faa930ec64f32c1929d91b626378cd357776b89c0a00cca4ca629d12f01cdde25b71b54');
        $shengYiWangClient->setUserId('6d21a74c825fb06397d070d59c552a44');
        $response = $shengYiWangClient->execute('POST', '/third/print/order/listDetail', $params);
        $shengYiWangClient->handleResponse($response);
        dd($response);
    }


}
