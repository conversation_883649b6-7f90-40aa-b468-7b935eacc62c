<?php

/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/25
 * Time: 22:38
 */
class DemoV3
{
//    private $host = 'http://127.0.0.1:8082';
    private $host = 'http://**************:8000';
    private $urlPrefix = '/api';
    private $appId = '16474131866217';
    private $appKey = 'b8365784396c28c4a0b9cfd79ea4a655';


    function curlPost($site,$urlPath, array $params)
    {
        $traceId = uniqid();
        $timestamp = time();

        $curl = curl_init();
        $urlParams = [
            'appId' => $this->appId,
            'traceId' => $traceId,
            'timestamp' => $timestamp,
            'sign' => '',
        ];
        $allParams = array_merge($urlParams, $params);
        $sign = $this->sign($allParams, $this->appKey);
        $urlParams['sign'] = $sign;
        $prefix=$site??'';
        $url = $this->host .$prefix. $this->urlPrefix . $urlPath . '?' . http_build_query($urlParams);
        echo 'url:' . $url . PHP_EOL;
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($params, JSON_UNESCAPED_UNICODE),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        echo $response;
        if (curl_errno($curl)) {
            echo 'Curl error: ' . curl_error($curl);
        }
        curl_close($curl);
        return $response;
    }

    /**
     * 签名
     * @param $params
     * @param $secret
     * @return string
     */
    function sign($params, $secret)
    {
        $clientSign = $params['sign'];
        unset($params['sign']);
        ksort($params);
        $params = array_map(function ($item) {
            if (is_array($item)) {
                $item = json_encode($item, JSON_UNESCAPED_UNICODE);
            }
            return $item;
        }, $params);
        $text = urldecode(http_build_query($params));
        $reServerSign = md5($text . '@' . $secret);

        return $reServerSign;
    }
}

$demo = new DemoV3();
$response = $demo->curlPost('/dy','/v3/check_sign', ['aaa' => 'a1']);
//$response = $demo->curlPost(null,'/v3/gen_oauth_state', ['aaa' => 'a1']);
var_dump(json_decode($response,true));
