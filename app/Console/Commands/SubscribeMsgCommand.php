<?php


namespace App\Console\Commands;

use App\Exceptions\OrderException;
use App\Jobs\Orders\SyncSaveSubscribeMsg;
use App\Services\Order\OrderServiceManager;
use Illuminate\Console\Command;

/**
 * 消息订阅接口版
 * Class SubscribeMsgCommand
 * @package App\Console\Commands
 */
class SubscribeMsgCommand extends Command
{
    protected $name = 'command:subscribe-msg';

    protected $description = '消息订阅接口版';


    /**
     * Execute the console command.
     *
     * @return mixed
     * @throws OrderException
     */
    public function handle()
    {
        $next = true;
        $orderService = OrderServiceManager::create(config('app.platform'));
        while ($next) {
            $items = $orderService->consumeSubscribeMsg();
            if (!empty($items)) {
                foreach ($items as $index => $item) {
                    dispatch((new SyncSaveSubscribeMsg($item)));
                }
//                sleep(2);
            } else {
                $next = false;
            }
        }
//        $items = $orderService->consumeSubscribeMsg();
//        if (empty($items)){ // 没消息停1秒
//            sleep(1);
//        }else{
//            foreach ($items as $index => $item) {
//                dispatch((new SyncSaveSubscribeMsg($item)));
//            }
//
//        }
    }
}
