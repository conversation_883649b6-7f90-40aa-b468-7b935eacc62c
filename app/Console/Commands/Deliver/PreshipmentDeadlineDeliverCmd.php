<?php

namespace App\Console\Commands\Deliver;

use App\Jobs\Deliver\PreshipmentPackagesDeliveryJob;
use App\Models\DeliverSetting;
use App\Models\Package;
use App\Services\Shop\ShopService;
use App\Utils\StrUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * 剩余发货时间发货任务
 *
 *
 */
class PreshipmentDeadlineDeliverCmd extends Command
{

    const PRESHIPMENT_LOGISTIC_DEADLINE_TIMER_DELIVER = 'preshipment:logistic-deadline-timer-deliver';
    protected $signature = self::PRESHIPMENT_LOGISTIC_DEADLINE_TIMER_DELIVER;
    protected $description = '剩余发货时间';



    /**
     * @var ShopService $shopService ;
     */
    private $shopService;

    public function __construct()
    {
        parent::__construct();
        $this->shopService = new ShopService();
    }

    public function handle()
    {

        try {
            $logisticDeliverSettings = $this->shopService->getLogisticDeliverSettings();
            $taskName = "[剩余发货时间]";
            Log::info("{$taskName}任务开始执行");

            /**
             * @var DeliverSetting $logisticDeliverSetting
             */
            foreach ($logisticDeliverSettings as $logisticDeliverSetting) {
                if (empty($logisticDeliverSetting)) {
                    continue;
                }
                $shopId = $logisticDeliverSetting->shopId;

                $isEnable = $logisticDeliverSetting->isEnable;
                $timeoutDeliverEnable = $logisticDeliverSetting->timeoutDeliverEnable;
                $deliverTimeout=$logisticDeliverSetting->deliverTimeout;
                $passed = $isEnable && $timeoutDeliverEnable;
                Log::info(StrUtil::format("租户:{} 是否开启选项{} 是否开启剩余发货时间发货{} 检查结果是{}",
                    $shopId, $isEnable, $timeoutDeliverEnable, $passed));
                if (!$passed) {
                    Log::info(StrUtil::format("租户:{} 不满足条件，不执行{}", $shopId, $taskName));
                    continue;
                }
                $wheres = [];
                $wheres[] = ['operation_shop_id' => $shopId];
                $wheres[] = ['pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_YES];
                $wheres[] = ['promise_ship_time' => DB::raw("date_add(promise_ship_time, interval -$deliverTimeout hour) < now()")];
                $wheres[] = ['source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY];
                $shopPackagesDeliverJob = new PreshipmentPackagesDeliveryJob($taskName,$shopId, $wheres);
                dispatch($shopPackagesDeliverJob);
            };

        } catch (Throwable $e) {
            Log::error("定时发货任务执行失败", [$e, $e->getTraceAsString()]);
        }


    }

}
