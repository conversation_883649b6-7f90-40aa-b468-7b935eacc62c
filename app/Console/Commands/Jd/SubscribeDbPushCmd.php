<?php

namespace App\Console\Commands\Jd;

use App\Models\Shop;
use App\Utils\DateTimeUtil;
use App\Utils\LogUtil;
use Illuminate\Console\Command;
use Jdcloud\Credentials\CredentialProvider;
use Jdcloud\Credentials\Credentials;
use Jdcloud\Yundingdatapush\YundingdatapushClient;

/**
 * 补偿一下数据库订阅
 */
class SubscribeDbPushCmd  extends Command
{

    protected $signature = 'jd:subscribe_db_push';
    protected $description = '补偿一下数据库订阅';
    public function handle(){
        $logger=LogUtil::jdDbSync();
        $appKey=config('socialite.jd.client_id');
        $secretAccessKey =config("jd_cloud.secretKey");
        $accessKeyId = config("jd_cloud.accessKey");
        $ydRdsInstanceId=config('jd_cloud.dbPush.rdsInstanceId');
        $yundingdatapushClient=new YundingdatapushClient([
            'credentials'  => new Credentials($accessKeyId, $secretAccessKey),
            'version' => 'latest',
            'scheme' => 'https'
        ]);
        $logger->info("开始补偿订阅",["accessKeyId"=>$accessKeyId,"secretAccessKey"=>$secretAccessKey]);
        $strNow=DateTimeUtil::strNow();
        Shop::query()->whereDate('expire_at' ,'>',$strNow)->chunk(100, function ($shops) use ($yundingdatapushClient,$appKey,$ydRdsInstanceId,$logger) {
            foreach ($shops as $shop) {
                try {
                    $param = ["datapushVender" => [
                        "appkey" => $appKey,
                        "ydRdsInstanceId" => $ydRdsInstanceId,
                        'venderId' => $shop->service_id
                    ]];
                    $result = $yundingdatapushClient->addDatapushVender($param);

                    $logger->info("订阅补偿", ["shopId"=>$shop->id,"param"=>  $param,"result"=>$result->toArray()]);
                } catch (\Throwable $e) {
                    $logger->error("订阅补偿", [$e->getTraceAsString()]);

                }

            }
        });
    }

}
