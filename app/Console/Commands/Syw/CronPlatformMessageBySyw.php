<?php

namespace App\Console\Commands\Syw;

use App\Services\ShengYiWang\SyncPlatformMessageBySywService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CronPlatformMessageBySyw extends Command
{
    protected $signature   = 'command:sync-platform-message-by-syw {--start=} {--end=} {--status=}}';
    protected $description = '同步平台信息';

    /**
     * @var SyncPlatformMessageBySywService $syncPlatformMessageBySywService
     */
    private $syncPlatformMessageBySywService;
    public function __construct(SyncPlatformMessageBySywService $syncPlatformMessageBySywService)
    {
        parent::__construct();
        $this->syncPlatformMessageBySywService = $syncPlatformMessageBySywService;
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try {
            $start = $this->option('start');
            if (!$start) {
                //这个地方从redis里面取上一次同步的最后时间
//            $start = Carbon::now()->subDays(1)->format('Y-m-d 00:00:00');
            }
            $end = $this->option('end');
            if (!$end) {
                $end = Carbon::now()->subSeconds(10)->format('Y-m-d 00:00:00');    //取当前时间的10秒钟
            }
            $status = $this->option('status') ?? 0;
            $this->syncPlatformMessageBySywService->sync($start, $end, $status);
        }catch (\Throwable $ex){
            Log::error("同步平台信息异常",["msg"=>$ex->getMessage(),"trace"=>$ex->getTraceAsString()]);
        }
    }
}
