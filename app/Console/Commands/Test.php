<?php

namespace App\Console\Commands;

use App\Jobs\Orders\SyncSaveOrders;
use App\Jobs\Shop\ShopTokenPushJob;
use App\Models\Fix\Shop;
use App\Services\Order\Impl\TaobaoOrderImpl;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use JdClient;
use TopClient\request\TmcUserGetRequest;

class Test extends Command
{
    protected $name = 'command:test';
    protected $description = '测试脚本';

    public function handle()
    {
        $shop = Shop::query()->first();
        dispatch(new ShopTokenPushJob($shop));


    }
}
