<?php

namespace App\Console\Commands\Xhs;

use App\Models\Goods;
use App\Models\Shop;
use App\Services\Goods\impl\XhsGoodsServiceImpl;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class XhsGetGoodsBySkuIdCmd extends Command
{

    protected $signature = 'xhs:get-goods-by-skuid --shop_id={shop_id} --sku_id={sku_id}';
    protected $description = '测试命令';

    public function handle()
    {
        $xhsGoodsService = new XhsGoodsServiceImpl();
        $shopId = $this->argument('shop_id');
        $shop=Shop::find($shopId);
        $skuId = $this->argument('sku_id');

        $xhsGoodsService->setShop($shop);
        $xhsGoodsService->setAccessToken($shop->access_token);
        $data = $xhsGoodsService->getGoodsBySkuIds([$skuId]);
        Log::info('测试命令执行成功',[ $skuId, $data]);
        $goods=$xhsGoodsService->formatToGoods($data);
        Goods::batchSave($goods, $shop->user_id, $shop->id);
        Log::info('测试命令执行成功',$data);
    }
}
