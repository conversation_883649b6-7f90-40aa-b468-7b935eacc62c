<?php
namespace App\Console\Commands;

use App\Jobs\Orders\DeleteOrderJob;
use App\Models\Order;
use App\Models\Shop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * Class ClearInvalidOrderDataCommand
 * @deprecated
 * @package App\Console\Commands
 */
class ClearInvalidOrderDataCommand extends Command
{
	protected $signature = 'command:clear-invalid-order-data {--shop_id=}';

	protected $description = '订单数据定期清理';

	public function handle()
	{
		$query        = Shop::query();
		$shopId       = null;
		if ($this->option('shop_id')) {
			$shopId = $this->option('shop_id');
		}
		if (!is_null($shopId)) {
			$query->where('id', $shopId);
		}

		$query->chunk(100, function ($shops) {
			foreach ($shops as $shop) {
				try {
					$this->clearData($shop);
				} catch (\Exception $e) {
					Log::warn('订单数据清理失败', [$shop, $e->getMessage()]);
					continue;
				}
			}
		});
	}

	protected function clearData($shop)
	{
		$conditions = [];
//		$conditions[] = ['user_id', $shop->user_id];
		$conditions[] = ['shop_id', $shop->id];
		$conditions[] = ['order_status', Order::ORDER_STATUS_DELIVERED];

		//累计打单数
		$totalPrintCount = $shop->totalStatistic;
		if ($shop->totalStatistic && $totalPrintCount['print_count'] > 0) {
			$conditions[] = ['send_at', '<', date('Y-m-d H:i:s', strtotime('-15 day'))];
		}

		$this->info($shop->id . ' - ' .Order::query()->where($conditions)->count());

		Order::query()->where($conditions)->chunkById(500, function ($orders){
			dispatch(new DeleteOrderJob($orders));
		}, 'pay_at');
	}
}
