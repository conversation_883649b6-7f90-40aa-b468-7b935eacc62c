<?php

namespace App\Console\Commands;

use App\Jobs\Orders\FixHistoryOrderStatusJob;
use App\Models\Order;
use Illuminate\Console\Command;

class FixHistoryOrderStatusCommand extends Command
{
	protected $signature   = 'command:fixHistoryOrderStatus {--shop_id=}';
	protected $description = '历史订单状态未同步问题修复';

	public function handle()
	{
		$shopId = null;
		if ($this->option('shop_id')) {
			$shopId = $this->option('shop_id');
		}

		$query = Order::query()->where('order_status', Order::ORDER_STATUS_PAYMENT);

		if (!is_null($shopId)) {
			$query = $query->where('shop_id', $shopId);
		}

		$query->chunk(100, function ($orders) {
			foreach ($orders as $order) {
				dispatch(new FixHistoryOrderStatusJob($order));
			}
		});
	}
}