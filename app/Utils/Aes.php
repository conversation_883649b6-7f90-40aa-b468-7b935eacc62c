<?php

namespace App\Utils;

class Aes
{
	/**
	 * var string $method 加解密方法，可通过openssl_get_cipher_methods()获得
	 */
	protected $method;

	/**
	 * var string $secret_key 加解密的密钥
	 */
	protected $secret_key;

	/**
	 * var string $iv 加解密的向量，有些方法需要设置比如CBC
	 */
	protected $iv;

	/**
	 * var string $options （不知道怎么解释，目前设置为0没什么问题）
	 */
	protected $options;

	/**
	 * 构造函数
	 *
	 * @param string $key 密钥
	 * @param string $method 加密方式
	 * @param string $iv iv向量
	 * @param mixed $options 还不是很清楚
	 *
	 */
	public function __construct($key, $method = 'AES-128-CBC', $iv = '', $options = 0)
	{
		// key是必须要设置的
		$this->secret_key = isset($key) ? $key : exit('key为必须项');

		$this->method = $method;

		$this->iv = $iv;

		$this->options = $options;
	}

	/**
	 * 加密方法，对数据进行加密，返回加密后的数据
	 *
	 * @param string $data 要加密的数据
	 *
	 * @return string
	 *
	 */
	public function encrypt($data)
	{
		$en = openssl_encrypt($data, $this->method, $this->secret_key, $this->options, $this->iv);
		//$en = $this->String2Hex($en);
		return $en;
	}

	public function stripPKSC7Padding($source)
	{
		$source = trim($source);
		$char   = substr($source, -1);
		$num    = ord($char);

		if ($num == 125) {
			return $source;
		}
		$source = substr($source, 0, -($num + 1));
		return $source;
	}

	public function user_mcrypt_decrypt($content_encrypted, $key, $cipher = MCRYPT_RIJNDAEL_128, $mode = MCRYPT_MODE_CBC, $pkcs7 = true, $base64 = true)
	{
		//AES, 128 模式加密数据 CBC
		$content_encrypted = $base64 ? base64_decode($content_encrypted) : $content_encrypted;
//		$content           = @mcrypt_decrypt($cipher, $key, $content_encrypted, $mode, $this->iv);  //mcrypt  方式

		$content           = openssl_decrypt($content_encrypted, 'AES-256-CBC', $key, 1, $this->iv); //openssl 方式
		// 解密后的内容 要根据填充算法来相应的移除填充数
		$content = $pkcs7 ? $this->stripPKSC7Padding($content) : rtrim($content, "\0");

		return $content;
	}

	/**
	 * 解密方法，对数据进行解密，返回解密后的数据
	 *
	 * @param string $data 要解密的数据
	 *
	 * @return string
	 *
	 */
	public function decrypt($data)
	{
		//		$data = $this->Hex2String($data);
		//		$de = openssl_decrypt(base64_encode($data), $this->method, $this->secret_key, OPENSSL_RAW_DATA, $this->iv);

		//		$de = openssl_decrypt($data, "AES-128-CBC", $this->secret_key, 0, $this->iv);

		$mcrypt_cipher = defined('MCRYPT_RIJNDAEL_128') ? MCRYPT_RIJNDAEL_128 : 'rijndael-128';
		$mcrypt_mode   = defined('MCRYPT_MODE_CBC') ? MCRYPT_MODE_CBC : 'cbc';

		$de = $this->user_mcrypt_decrypt($data, $this->secret_key, $mcrypt_cipher, $mcrypt_mode, true, true);
		return $de;
	}

	public function String2Hex($string)
	{
		$hex = '';
		for ($i = 0; $i < strlen($string); $i++) {
			$hex .= dechex(ord($string[$i]));
		}
		$hex = bin2hex($string);
		return $hex;
	}

	public function Hex2String($hex)
	{
		$string = '';
		for ($i = 0; $i < strlen($hex) - 1; $i += 2) {
			$string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
		}

		return $string;
	}
}