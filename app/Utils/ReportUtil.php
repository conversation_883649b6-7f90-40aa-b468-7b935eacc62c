<?php

namespace App\Utils;
use Illuminate\Support\Facades\Log;

/**
 * 订单相关的Util
 */
class ReportUtil
{

    public static function buildSkuTitle($orderItems): string
    {

        if (empty($orderItems)) {
            return "";
        }

        $orderItemsSkuGroup = collect($orderItems)->groupBy("sku_id");
        $result = [];
        foreach ($orderItemsSkuGroup as $skuId => $groupOrderItems) {
            $skuValue = $groupOrderItems[0]->sku_value;
            $num = 0;
            foreach ($groupOrderItems as $orderItem) {
                $num += intval($orderItem->goods_num);
            }
            $result[] = $skuValue . " [" . strval($num) . "]";
        }
//        Log::info("buildSkuTitle", ["orderItem"=>$orderItems,"result"=>$result]);
        return implode(PHP_EOL, $result);
    }

    public static function buildGoodsNum($orderItems): string
    {
        if (empty($orderItems)) {
            return strval(0);
        }
        $num = 0;
        foreach ($orderItems as $orderItem) {
            $num += intval($orderItem->goods_num ?? 0);
        }
        return strval($num);
    }

    public static function buildSkuOutId($orderItems){
        if (empty($orderItems)) {
            return "";
        }
        $skuOutIdArr = [];
        foreach ($orderItems as $value) {
            $skuOutIdArr[] = $value['outer_iid'] . "|" . $value['outer_sku_iid'];
        }
        return implode('|', $skuOutIdArr);
    }

    public static function buildBuyerMessages($orderItems): string
    {
        return isset($orderItems) ? self::implodeNotEmpty("|", array_pluck($orderItems, 'buyer_message')) :'';
    }
    public static function buildSellerMemos($orderItems): string
    {
        return isset($orderItems) ? self::implodeNotEmpty("|", array_pluck($orderItems, 'seller_memo')) :'';
    }

    public static function  implodeNotEmpty($separator="",$arr): string
    {
        if(empty($arr)){
            return "";
        }else{
          return   implode($separator,array_filter($arr,function($item){
                return !empty($item);
            }));
        }
    }
    public static function handleNumberNotFormat(array $list): array
    {
        // 避免数字转成科学记数法
        $list = array_map(function ($item) {
            // 有的订单号 是通过逗号链接，excel 会有问题。如： 111111,222222
            $arr = explode(',', $item);
            if ((is_numeric($item) && strlen($item) >= 10) ||
                (!empty($arr[0]) && is_numeric($arr[0]) && strlen($arr[0]) >= 10)) {
                return $item . "\t";
            }
            return $item;
        }, $list);
        return $list;
    }
}
