<?php

namespace App\Utils;

use App\Http\Request\PageParam;
use Illuminate\Http\Request;

class RequestUtil
{
    public static function ofPageParam(Request $request): PageParam
    {
        $pageParam=new PageParam();
        $pageParam->page=$request->input('page', 1);
        $pageParam->pageSize=$request->input('pageSize', 10);
        $sort = $request->input('sort', '');
        //多个排序之间用:分隔，一个排序你们用,分隔，字段和升降序
        $sorts=explode(':', $sort    );
        foreach ($sorts as $sort){
            $sort=explode(',', $sort);
            if(count($sort)==2){
                $pageParam->addSort($sort[0], $sort[1]);
            }
        }

        return $pageParam;
    }
}
