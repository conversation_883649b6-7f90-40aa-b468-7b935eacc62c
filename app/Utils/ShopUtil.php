<?php

namespace App\Utils;

use App\Constants\RedisKeyConst;
use App\Models\Shop;
use Illuminate\Support\Facades\Cache;

/**
 * 店铺相关的Util
 */
class ShopUtil
{
    /**
     * 获取通过ID 获取Shop，支持缓存
     * @param string $id
     * @return Shop|null
     */
    public static function firstByIdWithCache(string $id): ?Shop
    {
        return Cache::remember(sprintf(RedisKeyConst::SHOP_INFO_BY_ID, $id), 0.3, function () use ($id) {
//         缓存 12 秒
            return Shop::where('id', $id)->first();
        });
    }

    /**
     * 通过店铺标识符获取Shop，支持缓存
     * @param string $identifier
     * @return Shop|null
     */
    public static function firstByIdentifierWithCache(string $identifier): ?Shop{
        return Cache::remember(sprintf(RedisKeyConst::SHOP_INFO_BY_INDENTIFIER, $identifier), 0.3, function () use ($identifier) {
            //         缓存 12 秒
            return Shop::where('identifier', $identifier)->first();
        });
    }
}
