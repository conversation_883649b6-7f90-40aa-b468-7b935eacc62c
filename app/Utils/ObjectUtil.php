<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/5/23
 * Time: 16:26
 */

namespace App\Utils;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use InvalidArgumentException;
use stdClass;

class ObjectUtil
{
    /**
     * 把 map 转成 对象
     * @param array $data
     * @param string|object $class
     * @param bool $toCamel 字段转小驼峰
     * @return mixed
     * <AUTHOR>
     */
    public static function mapToObject(array $data, $class, bool $toCamel = false)
    {
        if(is_string($class)){
            $class = new $class();
        }
        foreach ($data as $key => $value) {
            $toCamel && $key = Str::camel($key);
            if (property_exists($class, $key)) {
                $class->$key = $value;
            }
        }
        return $class;
    }

    /**
     * 批量把 map 转成对象
     * <AUTHOR>
     * @param array $listData
     * @param string|object $class
     * @return array
     */
    public static function batchMapToObject(array $listData, $class): array
    {
        $resultList = [];
        foreach ($listData as $item) {
            $resultList[] = self::mapToObject($item, $class);
        }
        return $resultList;
    }

    /**
     * 获取对象属性值,支持.获取嵌套对象的属性
     * @param $object
     * @param $propertyString
     * @param  null  $default
     * @return mixed
     * @throws Exception
     */
    public static function getProperty($object, $propertyString, $default = null) {
        if (!is_object($object)) {
            return $default;
        }

        $properties = explode('.', $propertyString);
        $value = $object;

        foreach ($properties as $property) {
            Log::info("getProperty",[$value]);
            if (!is_object($value) || !isset($value->$property)) {
                return $default;
            }
            $value = $value->$property;
        }
        return $value;
    }

    /**
     * 把数组转换成 std
     * @param array $arr
     * @return stdClass
     */
    public static function toStd(array $arr): stdClass
    {
        $std = new stdClass();
        foreach ($arr as $key => $value) {
            if (is_array($value)) {
                $std->$key = self::toStd($value);
            } else {
                $std->$key = $value;
            }
        }
        return $std;
    }



}
