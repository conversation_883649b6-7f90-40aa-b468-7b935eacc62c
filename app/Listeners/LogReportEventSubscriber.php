<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/7/27
 * Time: 16:26
 */

namespace App\Listeners;


use App\Constants\OperationLogTypeConst;
use App\Constants\PlatformConst;
use App\Events\Orders\OrderCreateEvent;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderDeliveryEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Services\Order\Impl\KsOrderImpl;
use App\Services\Order\OrderServiceManager;
use Carbon\Carbon;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Log;

class LogReportEventSubscriber extends QueueEvent
{

    /**
     * 为订阅者注册监听器。
     *
     * @param Dispatcher $events
     */
    public function subscribe(Dispatcher $events)
    {
        if (in_array(config('app.platform'), [PlatformConst::KS, PlatformConst::JD, PlatformConst::TAOBAO])) {
            $events->listen(
                UserLoginEvent::class,
                'App\Listeners\LogReportEventSubscriber@onUserLogin'
            );
            $events->listen(
                OrderQueryEvent::class,
                'App\Listeners\LogReportEventSubscriber@onOrderQuery'
            );
//            $events->listen(
//                OrderDeliveryEvent::class,
//                'App\Listeners\LogReportEventSubscriber@onOrderDelivery'
//            );
            $events->listen(
                OrderPrintEvent::class,
                'App\Listeners\LogReportEventSubscriber@onOrderPrint'
            );
            $events->listen(
                SqlLogEvent::class,
                'App\Listeners\LogReportEventSubscriber@onSqlLog'
            );
            $events->listen(
                OrderDecryptEvent::class,
                'App\Listeners\LogReportEventSubscriber@onOrderDecrypt'
            );
            $events->listen(
                OrderUpdateEvent::class,
                'App\Listeners\LogReportEventSubscriber@onOrderUpdate'
            );
        }elseif (in_array(config('app.platform'), [PlatformConst::DY])) {
            $events->listen(
                OrderCreateEvent::class,
                'App\Listeners\LogReportEventSubscriber@onRawEvent'
            );
            $events->listen(
                OrderPrintEvent::class,
                'App\Listeners\LogReportEventSubscriber@onRawEvent'
            );
        }

    }
    public function onRawEvent($event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }
    /**
     * @param UserLoginEvent $event
     * <AUTHOR>
     */
    public function onUserLogin(UserLoginEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }

    /**
     * 处理订单查询事件。
     * @param OrderQueryEvent $event
     */
    public function onOrderQuery(OrderQueryEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }


    /**
     * @param OrderPrintEvent $event
     * <AUTHOR>
     */
    public function onOrderPrint(OrderPrintEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }

    /**
     * @param SqlLogEvent $event
     * <AUTHOR>
     */
    public function onSqlLog(SqlLogEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }

    /**
     * @param OrderDecryptEvent $event
     * <AUTHOR>
     */
    public function onOrderDecrypt(OrderDecryptEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }

    public function onOrderUpdate(OrderUpdateEvent $event)
    {
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setShop($event->shop);
        $orderService->reportBatchLogByEvent($event);
    }


}
