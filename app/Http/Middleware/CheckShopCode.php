<?php

namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuthShop;
use App\Models\Shop;
use App\Models\UserExtra;
use Closure;

class CheckShopCode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 校验 shopCode
        $shopCode = $request->input('shopCode');
        if (empty($shopCode)) {
            return throw_error_code_exception(StatusCode::SHOP_CODE_MISS);
        }

        // 店铺信息
        $shopInfo = Shop::firstByShopCode($shopCode);
        if (empty($shopInfo)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        //店铺授权是否正常
        if (!empty($shopInfo->access_token) && $shopInfo->auth_status != Shop::AUTH_STATUS_SUCCESS) {
            \Log::info("店铺授权不正常",[$shopInfo]);
//            return throw_error_code_exception(StatusCode::SHOP_AUTH_ERROR);
        }

//        // 是否过期
//        $userExtraInfo = UserExtra::query()->where('identifier', $shopInfo->shop_identifier)
//            ->orderBy('created_at', 'desc')->first();
//        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
//            \Log::info("店铺过期",[$userExtraInfo,$shopInfo]);
//            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
//        }

//        $request->user_id = $shopInfo->user_id;
        $request->shop_id = $shopInfo->id;
        $request->shop = $shopInfo;

        return $next($request);
    }
}
