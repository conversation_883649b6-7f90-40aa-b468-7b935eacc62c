<?php

namespace App\Http\Middleware;

use Closure;

class HandleReqId
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $req_id = \request()->header('req-id');
        empty($req_id) && $req_id = \request('traceId');

        // 过滤非法字符
        $match = preg_match('/^[-_a-zA-Z0-9]+$/', $req_id);
        !$match && $req_id = '';
        empty($req_id) && $req_id = session_create_id();
        // 最多36个字符
        $req_id = substr($req_id, 0,36);
        !defined('REQ_ID') && define('REQ_ID', $req_id);

        $response = $next($request);

        return $response;
    }
}
