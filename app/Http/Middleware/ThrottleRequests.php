<?php
/**
 * User: Sean
 * Date: 2019/5/30
 * Time: 17:21
 */

namespace App\Http\Middleware;

use Closure;

class ThrottleRequests
{
    public function handle($request, Closure $next)
    {
	    $request->hidePrivacy = false;
	    $hidePrivacyArr = [
	        '/order',
	        '/order/info/batch',
		    '/print_record',
		    '/waybill_history',
	    ];

        if (env('HIDE_PRIVACY_SWITCH', false) && in_array($request->getPathInfo(), $hidePrivacyArr)) {
	        $request->hidePrivacy = true;
        }

        return $next($request);
    }
}
