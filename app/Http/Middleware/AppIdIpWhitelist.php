<?php

namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use Closure;
use Illuminate\Http\Request;

class AppIdIpWhitelist
{
    public function handle(Request $request, Closure $next)
    {
        $appId = $request->input('appId');
        if (empty($appId)) {
            return $next($request);
        }
        $ipWhitelist = \Cache::remember('appidIpWhitelist:' . $appId, 60 * 1, function () use ($appId) {
            $info = ApiAuth::query()->where('app_id', $appId)->first();
            if (empty($info)) {
                return null;
            }
            return $info->ip_whitelist;
        });
        if (empty($ipWhitelist)) {
            return $next($request);
        }
        $ip = getRealIp();
        if (!in_array($ip, explode(',', $ipWhitelist))) {
            return throw_error_code_exception(StatusCode::IP_NOT_IN_WHITELIST, null, 'IP:' . $ip);
        }
        return $next($request);
    }
}
