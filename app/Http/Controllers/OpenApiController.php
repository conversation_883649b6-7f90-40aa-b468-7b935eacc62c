<?php
namespace App\Http\Controllers;

use App\Constants\PlatformConst;
use App\Models\ApiAuth;
use App\Models\ApiAuthShop;
use App\Models\ApiShopBind;
use App\Models\Company;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\Shop;
use App\Models\User;
use App\Models\UserExtra;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Client\DyClient;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\WaybillServiceManager;
use Illuminate\Http\Request;
use App\Http\StatusCode\StatusCode;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class OpenApiController extends Controller
{

    // 创建关系表
    public function getShopInfo(Request $request) {
        $this->validate($request, [
            'invite_code' => 'required|string'
        ]);
        // 换绑定授权
        $inviteCode = $request->input('invite_code');
        $shop = Shop::query()->where('shop_code', $inviteCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::INVIRE_CODE_ERROR);
        }

        // 店铺是否订购或过期
        $userExtraInfo = UserExtra::query()->where('identifier', $shop->identifier)->orderBy('created_at', 'desc')->first();
        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
        }
        // 生成配置信息
        $authShopInfo = ApiAuthShop::query()->where('shop_identifier', $shop->identifier)->first();
        if (empty($authShopInfo)) {
            // 生成token
            $key = mt_rand();
            $hash = md5($key . mt_rand() . time());
            $token = str_replace('=', '', base64_encode($hash));

            $authShopInfo = ApiAuthShop::query()->create([
                'shop_identifier' => $shop->identifier,
                'shop_expires_at' => $userExtraInfo->expire_at ?? date('Y-m-d H:i:s', time()),
                'shop_token'      => $token,
                'token_expires_at' => $userExtraInfo->expire_at ?? date('Y-m-d H:i:s', time())
            ]);
        }
        $authShopInfo->shop_expires_at = $userExtraInfo->expire_at;
        $authShopInfo->token_expires_at = $userExtraInfo->expire_at;
        $authShopInfo->save();
        $result = [
            'shopId'          => $shop->identifier,
            'shopName'        => $shop->shop_name,
            'shopCode'        => $shop->shop_code??'',
            'shopToken'       => $authShopInfo->shop_token, //店铺token
            'shopExpireTime'  => $userExtraInfo->expire_at, // 订购到期时间
            'tokenExpireTime' => $userExtraInfo->expire_at, // token到期时间
        ];

        return $this->successForOpenApi($result);
    }

    // 创建关系表
    public function getShopInfoV2(Request $request) {
        $this->validate($request, [
            'shop_name' => 'required|string'
        ]);
        $shopName = $request->input('shop_name');
        $appId    = $request->input('app_id');
        // 查询店铺信息
        $shop = Shop::query()->where(['shop_name' => $shopName])->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_NOT_FOUND);
        }
        // 店铺是否订购或过期
        $userExtraInfo = UserExtra::query()->where('identifier', $shop->identifier)->orderBy('created_at', 'desc')->first();
        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
        }
        // 生成配置信息
        $authShopInfo = ApiAuthShop::query()->where('shop_identifier', $shop->identifier)->first();
        if (empty($authShopInfo)) {
            // 生成token
            $key = mt_rand();
            $hash = md5($key . mt_rand() . time());
            $token = str_replace('=', '', base64_encode($hash));

            $authShopInfo = ApiAuthShop::query()->create([
                'shop_identifier' => $shop->identifier,
                'shop_expires_at' => $userExtraInfo->expire_at ?? date('Y-m-d H:i:s', time()),
                'shop_token'      => $token,
                'token_expires_at' => $userExtraInfo->expire_at ?? date('Y-m-d H:i:s', time())
            ]);
        }
        $authShopInfo->shop_expires_at = $userExtraInfo->expire_at;
        $authShopInfo->token_expires_at = $userExtraInfo->expire_at;
        $authShopInfo->save();
        ApiShopBind::updateOrCreateByAppIdShopId($appId, $shop->id);
        $result = [
            'shopId'          => $shop->identifier,
            'shopName'        => $shop->shop_name,
            'shopCode'        => $shop->shop_code??'',
            'shopToken'       => $authShopInfo->shop_token, //店铺token
            'shopExpireTime'  => $userExtraInfo->expire_at, // 订购到期时间
            'tokenExpireTime' => $userExtraInfo->expire_at, // token到期时间
        ];

        return $this->successForOpenApi($result);
    }

    public function refreshToken(Request $request) {
        $this->validate($request, [
            'invite_code' => 'required|string'
        ]);
        // 换绑定授权
        $inviteCode = $request->input('invite_code');
        $shop = Shop::query()->where('shop_code', $inviteCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::INVIRE_CODE_ERROR);
        }

        // 店铺是否订购或过期
        $userExtraInfo = UserExtra::query()->where('identifier', $shop->identifier)->orderBy('created_at', 'desc')->first();
        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
        }
        // 查询配置信息
        $authShopInfo = ApiAuthShop::query()->where('shop_identifier', $shop->identifier)->first();
        if (empty($authShopInfo)) {
            return throw_error_code_exception(StatusCode::SHOP_NO_CONFIG);
        }
        //生成token
        $key = mt_rand();
        $hash = md5($key . mt_rand() . time());
        $shopToken = str_replace('=', '', base64_encode($hash));

        ApiAuthShop::query()->updateOrCreate(['shop_identifier'=>$shop->identifier],[
            'shop_token' => $shopToken,
            'token_expires_at' => $userExtraInfo->expire_at,
            'updated_at' => Carbon::now()
        ]);

        $result = [
            'shopId'          => $authShopInfo->shop_identifier,
            'shopName'        => $shop->shop_name,
            'shopToken'       => $shopToken, //店铺token
            'shopExpireTime'  => $userExtraInfo->expire_at, // 订购到期时间
            'tokenExpireTime' => $userExtraInfo->expire_at, // 订购到期时间
        ];

        return $this->successForOpenApi($result);
    }

    public function refreshTokenV2(Request $request) {
        $this->validate($request, [
            'shop_name' => 'required|string'
        ]);
        // 查询店铺信息
        $shopName = $request->input('shop_name');
        $shop = Shop::query()->where(['shop_name' => $shopName])->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        // 店铺是否订购或过期
        $userExtraInfo = UserExtra::query()->where('identifier', $shop->identifier)->orderBy('created_at', 'desc')->first();
        if (empty($userExtraInfo) || strtotime($userExtraInfo->expire_at) < time()) {
            return throw_error_code_exception(StatusCode::SHOP_NO_BUY);
        }
        // 查询配置信息
        $authShopInfo = ApiAuthShop::query()->where('shop_identifier', $shop->identifier)->first();
        if (empty($authShopInfo)) {
            return throw_error_code_exception(StatusCode::SHOP_NO_CONFIG);
        }
        //生成token
        $key = mt_rand();
        $hash = md5($key . mt_rand() . time());
        $shopToken = str_replace('=', '', base64_encode($hash));

        ApiAuthShop::query()->updateOrCreate(['shop_identifier'=>$shop->identifier],[
            'shop_token' => $shopToken,
            'token_expires_at' => $userExtraInfo->expire_at,
            'updated_at' => Carbon::now()
        ]);

        $result = [
            'shopId'          => $authShopInfo->shop_identifier,
            'shopName'        => $shop->shop_name,
            'shopToken'       => $shopToken, //店铺token
            'shopExpireTime'  => $userExtraInfo->expire_at, // 订购到期时间
            'tokenExpireTime' => $userExtraInfo->expire_at, // 订购到期时间
        ];

        return $this->successForOpenApi($result);
    }
    /**
     * 获取订单列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getOrderList(Request $request)
    {
        $this->validate($request, [
            'start_time' => 'required|string',
            'end_time'   => 'required|string'
        ]);

        $startTime = strtotime($request->input('start_time'));
        $endTime   = strtotime($request->input('end_time'));
        $page      = $request->input('page', 1);
        $pageSize  = $request->input('page_size', 100);
        $flag      = $request->input('flag');
        $remark    = $request->input('remark', '');
        $orderStatus = $request->input('order_status', 1);
        // 一页最多1000条
        if ($pageSize > 1000) {
            $pageSize = 1000;
        }

        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($request->user_id);
        $orderService->setShop($request->shop);

        /*  请求平台拉取订单信息 begin 暂时先走数据库
        $orderService->setPage($page);
        $result = $orderService->getTradesOrderForOpenApi($startTime, $endTime);
        /*请求平台拉取订单信息 end 暂时先走数据库 */

        $result = $orderService->getOrderListForOpenApi($startTime, $endTime, $flag, $remark, $page, $pageSize, $orderStatus);

        return $this->successForOpenApi($result);
    }


    /**
     * 获取订单详情
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getOrderInfo(Request $request)
    {
        $this->validate($request, [
            'orderSns' => 'required|string'
        ]);

        $orderSns = $request->input('orderSns');
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderSnsArr = explode(',', $orderSns);
        if (count($orderSnsArr) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        /*  请求平台拉取订单信息 begin 暂时先走数据库
        $orderInfo = [];
        $orderService->setShop($shop);
        foreach ($orderSnsArr as $key => $tid) {
            $orderInfo[] = $orderService->getOrderInfoForOpenApi($tid);
        }
        /*  请求平台拉取订单信息 end 暂时先走数据库 */

        $orderService->setShop($request->shop);
        $request = $orderService->getOrdersInfoForOpenApi($orderSnsArr);

        return $this->successForOpenApi($request);
    }

    /**
     * 网点查询
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getWpBranchList(Request $request)
    {
        $this->validate($request, [
            'platform'   => 'required|string',
            'phone'   => 'required|string',
        ]);

        $phone = $request->input('phone');
        $platform = $request->input('platform');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = Shop::query()->where('user_id', $user->id)->first();
            if (empty($waybill->access_token)) {
                return throw_error_code_exception(StatusCode::SHOP_ERROR);
            }
        } else {
            $ownerId     = $request->input('owner_id', '');
            $where = ['user_id'=>$user->id, 'auth_source'=>$platform];
            if (!empty($ownerId)) {
                $where['owner_id'] = $ownerId;
            }
            $waybill = Waybill::query()->where($where)->first();
        }

        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }

        // 查询网店信息
        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $serviceId      = config('app.platform') == 'jd' ? $waybill->service_id : '';
        $waybillData    = $waybillService->waybillSubscriptionQuery('', $serviceId);

        $companyList = [];
        foreach ($waybillData as $key=>$item) {
            foreach ($item['branch_account_cols'] as $k => $v) {
                if ($platform == Waybill::OPEN_API_DEFAULT) {
                    foreach ($v['shipp_addresses'] as $val) {
                        $addrArr = [
                            'province' => $val['province'],
                            'city'     => $val['city'],
                            'district' => $val['district'],
                            'detail'   => $val['detail']
                        ];
                        $companyList[] = array_merge([
                            'wp_code'       => $item['wp_code'],
                            'branch_code'   => $v['branch_code'],
                            'quantity'      => $v['quantity'],
                            'cancel_quantity'         => $v['cancel_quantity'],
                            'recycled_quantity'       => $v['recycled_quantity'],
                            'allocated_quantity'      => $v['allocated_quantity'],
                        ], $addrArr);
                    }
                } else {
                    $shipp = $v['shipp_addresses'];
                    foreach ($shipp as $address) {
                        $addrArr = [
                            'province' => is_object($address) ? $address->province : $address['province'],
                            'city'     => is_object($address) ? $address->city : $address['city'],
                            'district' => is_object($address) ? $address->district : $address['district'],
                            'detail'   => is_object($address) ? $address->detail : $address['detail']
                        ];

                        $companyList[] = array_merge([
                            'wp_code'       => $item['wp_code'],
                            'branch_code'   => $v['branch_code'],
                            'quantity'      => $v['quantity'],
                            'cancel_quantity'         => $v['cancel_quantity'],
                            'recycled_quantity'       => $v['recycled_quantity'],
                            'allocated_quantity'      => $v['allocated_quantity'],
                        ], $addrArr);
                    }
                }
            }
        }

        return $this->successForOpenApi($companyList);
    }

    public function getAccountList(Request $request)
    {
        $this->validate($request, [
            'phone'   => 'required|string',
        ]);

        $phone = $request->input('phone');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        $waybill = Waybill::query()->where(['user_id'=>$user->id])->get();
        $result = [];
        if (!empty($waybill)) {
            foreach ($waybill as $item) {
                $result[] = [
                    'platform' => $item['auth_source'],
                    'owner_id' => $item['owner_id'],
                    'owner_name' => $item['owner_name']
                ];
            }
        }

        return $this->successForOpenApi($result);
    }

    /**
     * 获取面单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getWayBillCode(Request $request)
    {
        $this->validate($request, [
            'sender_name'   => 'required|string',
            'sender_mobile' => 'required|string',
            'wp_code'       => 'required|string',
            'waybill_type'  => 'required|string',
            'platform'      => 'required|string',
            'order_sn_list' => 'required|string',
            'branch_code'   => 'required|string',
            'phone'   => 'required|string',
        ]);

        Log::info('[对外接口-获取面单号] 机构获取面单号', [$request->input()]);
        $platform    = $request->input('platform');
        $branchCode  = $request->input('branch_code');
        $senderName  = $request->input('sender_name');
        $sendMobile  = $request->input('sender_mobile');
        $orderSnList = $request->input('order_sn_list');
        $waybillType = $request->input('waybill_type');
        $packageNum  = $request->input('package_num', 1);
        $batchNo     = $request->input('batch_no', time() . rand(00, 99));
        $wpCode      = $request->input('wp_code');
        $ownerId     = $request->input('owner_id', '');
        $appId       = $request->input('app_id');

        if (config('app.platform') == 'dy' && $platform != Waybill::OPEN_API_DEFAULT) {
            return throw_error_code_exception(StatusCode::WAYBILL_ERROR);
        }

        if ($platform == Waybill::OPEN_API_DEFAULT) {
            return throw_error_code_exception(StatusCode::API_VERSION_ERROR);
        }

        $phone = $request->input('phone');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = Shop::query()->where('user_id', $user->id)->first();
        } else {
            $params = ['user_id'=>$user->id, 'auth_source'=>$platform];
            if (!empty($ownerId)) {
                $params = ['user_id'=>$user->id, 'auth_source'=>$platform, 'owner_id'=>$ownerId];
            }
            $waybill = Waybill::query()->where($params)->first();
        }

        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }

        $orderSnList = explode(',', $orderSnList);
        if (count($orderSnList) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        // 查询网点信息
        $authSource = $platform == Waybill::OPEN_API_DEFAULT ? Waybill::AUTH_SOURCE_DY : $waybill->auth_source;
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $waybillData    = $waybillService->waybillSubscriptionQuery();

        $userId = $request->user_id;
        $shopId = $request->shop_id;
        $printData = Order::getPrintDataAndWaybillForOpenApi($userId, $shopId, $wpCode, $platform, $branchCode, $senderName, $sendMobile, $orderSnList, $waybillType, $batchNo, $packageNum, $waybillData, $waybill, $appId);

        return $this->successForOpenApi($printData);
    }

    /**
     * 获取面单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getWayBillCodeV2(Request $request)
    {
        $this->validate($request, [
            'sender_name'   => 'required|string',
            'sender_mobile' => 'required|string',
            'wp_code'       => 'required|string',
            'waybill_type'  => 'required|string',
            'platform'      => 'required|string',
            'order_sn_list' => 'required|string',
            'branch_code'   => 'required|string',
            'invite_code'   => 'required|string',
            //'phone'   => 'required|string',
        ]);

        Log::info('[对外接口-获取面单号] 机构获取面单号', [$request->input()]);
        $platform    = $request->input('platform');
        $branchCode  = $request->input('branch_code');
        $senderName  = $request->input('sender_name');
        $sendMobile  = $request->input('sender_mobile');
        $orderSnList = $request->input('order_sn_list');
        $waybillType = $request->input('waybill_type');
        $packageNum  = $request->input('package_num', 1);
        $batchNo     = $request->input('batch_no', time() . rand(00, 99));
        $wpCode      = $request->input('wp_code');
        $ownerId     = $request->input('owner_id', '');
        $inviteCode  = $request->input('invite_code', '');
        $appId       = $request->input('app_id');

        $shop = Shop::query()->where('shop_code', $inviteCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::KS, PlatformConst::JD]) && $platform != Waybill::OPEN_API_DEFAULT) {
            return throw_error_code_exception(StatusCode::WAYBILL_ERROR);
        }

        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = $shop;
            if (empty($waybill) || $waybill->auth_status != Shop::AUTH_STATUS_SUCCESS) {
                return throw_error_code_exception(StatusCode::SHOP_WAYBILL_ERROR);
            }
        } else {
            $params = ['shop_id'=>$shop->id, 'auth_source'=>$platform];
            if (!empty($ownerId)) {
                $params = ['shop_id'=>$shop->id, 'auth_source'=>$platform, 'owner_id'=>$ownerId];
            }
            $waybill = Waybill::query()->where($params)->first();
        }

        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }

        $orderSnList = explode(',', $orderSnList);
        if (count($orderSnList) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        // 查询网点信息
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            switch (config('app.platform')){
                case 'dy':
                    $authSource = Waybill::AUTH_SOURCE_DY;
                    break;
                case 'jd':
                    $authSource = Waybill::AUTH_SOURCE_JD;
                    break;
                default:
                    $authSource = Waybill::AUTH_SOURCE_DY;
                    break;
            }
        } else {
            $authSource = $waybill->auth_source;
        }
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $serviceId      = config('app.platform') == 'jd' ? $waybill->service_id : '';
        $waybillData    = $waybillService->waybillSubscriptionQuery('', $serviceId);

        $userId = $request->user_id;
        $shopId = $request->shop_id;
        $printData = Order::getPrintDataAndWaybillForOpenApi($userId, $shopId, $wpCode, $platform, $branchCode, $senderName, $sendMobile, $orderSnList, $waybillType, $batchNo, $packageNum, $waybillData, $waybill, $appId);

        return $this->successForOpenApi($printData);
    }

    /**
     * 回收运单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function wayBillCancel(Request $request)
    {
        $this->validate($request, [
            "wp_code"         => "required|string",
            'platform'        => 'required|string',
            "waybill_codes"   => "required|string",
            'phone'           => 'required|string',
        ]);

        Log::info('[对外接口-回收面单号] 机构回收面单号', [$request->input()]);
        $platform = $request->input('platform');
        $wpCode   = $request->input('wp_code');
        $waybillCode = $request->input('waybill_codes');

        $phone = $request->input('phone');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = Shop::query()->where('user_id', $user->id)->first();
        } else {
            $waybill = Waybill::query()->where(['user_id'=>$user->id, 'auth_source'=>$platform])->first();
        }
        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }

        // 一次回收限制50个订单
        $waybillCodeArr = explode(',', $waybillCode);
        if (count($waybillCodeArr) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        $result = [];
        foreach ($waybillCodeArr as $item) {
            // 是否获取过面单号
            $waybillHistory = WaybillHistory::query()->where(['waybill_code' => $item])->first();
            $data = [
                'waybillCode' => $item,
                'wpCode'      => $wpCode,
                'code'        => 0,
                'errorMsg'    => ''
            ];
            if (!empty($waybillHistory)) {
                $ret = Order::recoveryForOpenApi($waybillHistory, $waybill, $platform);
                if ($ret === true) {
                    $data['code'] = 1;
                } else {
                    $data['errorMsg'] = $ret;
                }
            }
            $result[] = $data;
        }

        return $this->successForOpenApi($result);
    }

    /**
     * 自由打印
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function freeGetWaybillCode(Request $request)
    {
        $this->validate($request, [
            'params'   => 'required|string',
            'platform' => 'required|string',
            'phone'    => 'required|string',
        ]);

        Log::info('[对外接口-自由打印面单] 机构自由打印面单', [$request->input()]);
        $platform = $request->input('platform');
        $params = $request->input('params');

        //抖音面单使用v2接口
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            return throw_error_code_exception(StatusCode::API_VERSION_ERROR);
        }
        // 是否有授权
        $phone = $request->input('phone');
        $appId = $request->input('app_id');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = Shop::query()->where('user_id', $user->id)->first();
        } else {
            $ownerId     = $request->input('owner_id', '');
            $where = ['user_id'=>$user->id, 'auth_source'=>$platform];
            if (!empty($ownerId)) {
                $where = ['user_id'=>$user->id, 'auth_source'=>$platform, 'owner_id'=>$ownerId];
            }
            $waybill = Waybill::query()->where($where)->first();
        }
        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }
        $params = json_decode($params, true);
        // 创建自由打印订单
        $data = [
            "sender_name"       => $params['sender_name'],
            "sender_phone"      => $params['sender_phone'],
            "sender_province"   => $params['sender_province'],
            "sender_city"       => $params['sender_city'],
            "sender_district"   => $params['sender_town'],
            "sender_detailaddress"=> $params['sender_detail'],
            "receiver_name"     => $params['receiver_name'],
            "receiver_phone"    => $params['receiver_phone'],
            "receiver_province" => $params['receiver_province'],
            "receiver_city"     => $params['receiver_city'],
            "receiver_district" => $params['receiver_town'],
            "receiver_town"     => $params['receiver_street'] ?? "",
            "receiver_address"  => $params['receiver_address'],
            'goods_info'        => $params['goods_info'],
            'production_type'   => '商品详细',
            'wp_code'           => $params['wp_code'],
            'order_no'          => $params['order_no'] ?? null
        ];

        if(empty($data['receiver_phone']) && empty($data['receiver_tel'])){
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }
        if (!isset($params['goods_info']) || empty($params['goods_info'])) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }

        $result = CustomizeOrder::create(array_merge($data, [
            'user_id'    => $request->user_id,
            'shop_id'    => $request->shop_id,
            'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
        ]));
        if (!$result) {
            return throw_error_code_exception(StatusCode::SYSTEM_ERROR);
        }

        $waybillType = $params['waybill_type'];
        $branchCode  = $request->input('branch_code');
        $printData = CustomizeOrder::getPrintDataAndWaybillForOpenApi(
            $request->user_id,
            $request->shop_id,
            $result,
            $waybill,
            $waybillType,
            $platform,
            $appId,
            $branchCode
        );

        return $this->successForOpenApi($printData);
    }

    /**
     * 自由打印
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function freeGetWaybillCodeV2(Request $request)
    {
        $this->validate($request, [
            'params'   => 'required|string',
            'platform' => 'required|string',
            'invite_code' => 'required|string',
        ]);

        Log::info('[对外接口-自由打印面单] 机构自由打印面单', [$request->input()]);
        $appId = $request->input('app_id');
        $params = $request->input('params');
        $platform = $request->input('platform');
        $inviteCode = $request->input('invite_code');
        $branchCode  = $request->input('branch_code');

        $shop = Shop::query()->where('shop_code', $inviteCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = $shop;
            if (empty($waybill) || $waybill->auth_status != Shop::AUTH_STATUS_SUCCESS) {
                return throw_error_code_exception(StatusCode::SHOP_WAYBILL_ERROR);
            }
        } else {
            $ownerId     = $request->input('owner_id', '');
            $where = ['shop_id'=>$shop->id, 'auth_source'=>$platform];
            if (!empty($ownerId)) {
                $where = ['shop_id'=>$shop->id, 'auth_source'=>$platform, 'owner_id'=>$ownerId];
            }
            $waybill = Waybill::query()->where($where)->first();
        }
        if (empty($waybill)) {
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }
        $params = json_decode($params, true);
        // 创建自由打印订单
        $data = [
            "sender_name"       => $params['sender_name'],
            "sender_phone"      => $params['sender_phone'],
            "sender_province"   => $params['sender_province'],
            "sender_city"       => $params['sender_city'],
            "sender_district"   => $params['sender_town'],
            "sender_detailaddress"=> $params['sender_detail'],
            "receiver_name"     => $params['receiver_name'],
            "receiver_phone"    => $params['receiver_phone'],
            "receiver_province" => $params['receiver_province'],
            "receiver_city"     => $params['receiver_city'],
            "receiver_district" => $params['receiver_town'],
            "receiver_town"     => $params['receiver_street'] ?? "",
            "receiver_address"  => $params['receiver_address'],
            'goods_info'        => $params['goods_info'],
            'production_type'   => '商品详细',
            'wp_code'           => $params['wp_code'],
            'order_no'          => $params['order_no'] ?? null
        ];

        if(empty($data['receiver_phone']) && empty($data['receiver_tel'])){
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }
        if (!isset($params['goods_info']) || empty($params['goods_info'])) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }

        if ($data['order_no']) {
            $result = CustomizeOrder::updateOrCreate(['order_no'=>$data['order_no']], array_merge($data, [
//                'user_id'    => $request->user_id,
                'shop_id'    => $request->shop_id,
                'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
            ]));
        } else {
            $result = CustomizeOrder::create(array_merge($data, [
//                'user_id'    => $request->user_id,
                'shop_id'    => $request->shop_id,
                'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
            ]));
        }

        if (!$result) {
            return throw_error_code_exception(StatusCode::SYSTEM_ERROR);
        }

        $waybillType = $params['waybill_type'];
        $printData = CustomizeOrder::getPrintDataAndWaybillForOpenApi(
            $request->user_id,
            $request->shop_id,
            $result,
            $waybill,
            $waybillType,
            $platform,
            $appId,
            $branchCode
        );

        return $this->successForOpenApi($printData);
    }

    /**
     * 发货接口
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function deliverGoods(Request $request)
    {
        $this->validate($request, [
            'params' => 'required|string'
        ]);


        $params = json_decode($request->input('params'), true);
        $appId = $request->input('app_id', '');
        Log::info('[对外接口-发货接口] 机构请求发货', [$params]);
        if (!is_array($params)) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }
        if (count($params) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        $result = [];
        foreach ($params as $key=>$item) {
            $tempData = [
                'order_sn'     => $item['order_sn'],
                'waybill_code' => $item['waybill_code'],
                'code'         => 0,
                'err_msg'      => '未查询到订单'
            ];

            $orderInfo = Order::query()->where(['tid'=>$item['order_sn']])->first();
            if (!empty($orderInfo)) {
                $data = [
                    'id'           => $orderInfo->id,
                    'express_no'   => $item['waybill_code'],
                    'express_code' => $item['wp_code']
                ];
                // 已经发货成功不再做处理
                if ($orderInfo->order_status == Order::ORDER_STATUS_DELIVERED) {
                    $ret = true;
                } else {
                    $ret  = Order::deliveryForOpenApi($appId, $data);
                }

                if ($ret === true) {
                    $tempData['code']    = 1;
                    $tempData['err_msg'] = '';
                } else {
                    $tempData['err_msg'] = $ret;
                }
            } else {
                $tempData['code']    = 0;
            }
            $result[] = $tempData;
        }

        return $this->successForOpenApi($result);
    }

    public function batchUploadFreeOrder(Request $request)
    {
        $this->validate($request, [
            'order_list'   => 'required|string',
            'phone'    => 'required|string',
        ]);

        Log::info('[对外接口-批量上传订单] 机构批量上传订单', [$request->input()]);
        $phone = $request->input('phone');
        $orderList = $request->input('order_list');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        $shop = Shop::query()->where('user_id', $user->id)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        $orderList = json_decode($orderList, true);
        //批量上传订单限制50条
        if (count($orderList) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        $pShopId = 0;
        //是否有上级邀请码 关联上下级关系
        $inviteCode = $request->input('invite_code', 0);
        if ($inviteCode) {
            $pShop = Shop::query()->where('shop_code', $inviteCode)->first();
            $pShopId = $pShop->id;
        }
        foreach ($orderList as $order){
            $data = [
                "sender_name"       => $order['sender_name'],
                "sender_phone"      => $order['sender_phone'],
                "sender_province"   => $order['sender_province'],
                "sender_city"       => $order['sender_city'],
                "sender_district"   => $order['sender_town'],
                "sender_detailaddress"=> $order['sender_detail'],
                "receiver_name"     => $order['receiver_name'],
                "receiver_phone"    => $order['receiver_phone'],
                "receiver_province" => $order['receiver_province'],
                "receiver_city"     => $order['receiver_city'],
                "receiver_district" => $order['receiver_town'],
                "receiver_address"  => $order['receiver_address'],
                'goods_info'        => $order['goods_info'],
                'production_type'   => '商品详细',
                'order_no'          => $order['order_no'],
                'platform'          => $order['platform'],
                'p_shop_id'         => $pShopId
            ];

            //创建or更新自由打印订单
            CustomizeOrder::updateOrCreate([
//                'user_id'    => $user->id,
                'shop_id'    => $shop->id,
                'order_no'   => (string)$order['order_no']
            ],array_merge($data, [
                'user_id'    => $user->id,
                'shop_id'    => $shop->id,
                'order_type' => CustomizeOrder::ORDER_TYPE_BATCH,
            ]));
        }

        return $this->successForOpenApi();
    }

    public function batchGetFreeOrderStatus(Request $request)
    {
        $this->validate($request, [
            'order_no_list'   => 'required|string',
            'phone'    => 'required|string',
        ]);

        $phone = $request->input('phone');
        $orderNoList = $request->input('order_no_list');
        $user = User::query()->where('phone', $phone)->first();
        if (empty($user)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        $shop = Shop::query()->where('user_id', $user->id)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        $orderNoList = json_decode($orderNoList, true);
        if (count($orderNoList) > 50) {
            return throw_error_code_exception(StatusCode::COUNT_OUT);
        }

        $result = [];
        foreach ($orderNoList as $orderNo){
            $orderInfo = CustomizeOrder::query()->where('order_no', (string)$orderNo)->first();
            $result[$orderNo] = [
                'status'      => isset($orderInfo->print_status) ? $orderInfo->print_status : 0,
                'wpCode'      => isset($orderInfo->wp_code) ? $orderInfo->wp_code : '',
                'waybillCode' => isset($orderInfo->waybill_code) ? $orderInfo->waybill_code : '',
            ];
        }

        return $this->successForOpenApi($result);
    }

    public function getDYPrintParams(Request $request)
    {
        $this->validate($request, [
            'invite_code'   => 'required|string',
        ]);

        $inviteCode  = $request->input('invite_code', '');
        $shop = Shop::query()->where('shop_code', $inviteCode)->first();

        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $client = (new DyClient($appKey, $secretKey))->setAccessToken($shop->access_token);
        $requestData = $client->buildRequestData([], 'logistics/getShopKey');
        $paramsStr = urldecode(http_build_query($requestData));

        return $this->successForOpenApi(['params' => $paramsStr]);
    }

    public function getDYPrintData(Request $request)
    {
        $this->validate($request, [
            'waybill_list'   => 'required|string',
            'invite_code'   => 'required|string',
        ]);

        $inviteCode  = $request->input('invite_code', '');
        $waybillList = json_decode($request->input('waybill_list', ''), true);

        $shop = Shop::query()->where('shop_code', $inviteCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        if (empty($waybillList)) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }

        //去除未查询到的运单
        $hasHistory = WaybillHistory::query()->where(['auth_source'=>Waybill::AUTH_SOURCE_DY, 'waybill_code'=> $waybillList['waybill_code'], 'wp_code' =>$waybillList['wp_code']])->first();
        if (empty($hasHistory)) {
            $failedData = [
                'waybillCode' => $waybillList['waybill_code'],
                'wpCode'      => $waybillList['wp_code'],
                'code'        => 0,
                'printData'   => '',
                'errorMsg'    => '请核对面单号、物流编码以及是否是抖音面单'
            ];
            return $this->successForOpenApi($failedData);
        } else {
            $printData = json_decode($hasHistory['print_data'], true);
            $waybillList['addData'] = $printData['addData'] ?? "";
            $waybillList['templateUrl'] = $printData['templateUrl'] ?? "";
        }

        $waybillService = WaybillServiceManager::init(Waybill::AUTH_SOURCE_DY, $shop->access_token);
        $res = $waybillService->getPrintData($waybillList);

        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $client = (new DyClient($appKey, $secretKey))->setAccessToken($shop->access_token);
        $requestData = $client->buildRequestData([], 'logistics/getShopKey');
        $paramsStr = urldecode(http_build_query($requestData));
        //foreach ($waybillList as $item){
            if (!array_key_exists('err_no', $res)) {
                $print_data = [
                    'encryptedData' => $res['print_data'],
                    'templateUrl'   => $waybillList['templateUrl'],
                    'addData'       => $waybillList['addData'],
                    'signature'     => $res['sign'],
                    'params'        => $paramsStr
                ];
                $data = [
                    'waybillCode' => $waybillList['waybill_code'],
                    'wpCode'      => $waybillList['wp_code'],
                    'code'        => 1,
                    'printData'   => json_encode($print_data),
                    'errorMsg'    => ''
                ];
            } else {
                $data = [
                    'waybillCode' => $waybillList['waybill_code'],
                    'wpCode'      => $waybillList['wp_code'],
                    'code'        => 0,
                    'printData'   => '',
                    'errorMsg'    => json_encode($res)
                ];
            }
        //}
        return $this->successForOpenApi($data);
    }
}
