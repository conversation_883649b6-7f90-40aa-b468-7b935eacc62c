<?php

namespace App\Http\Controllers;

use App\Constants\ErrorConst;
use App\Constants\LogisticsConst;
use App\Exceptions\ApiException;
use App\Models\Company;
use App\Models\DistrictAssign;
use App\Models\Shop;
use App\Models\Template;
use App\Services\DistrictAssignService;
use App\Services\Waybill\CompanyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException as ValidationExceptionAlias;
use Maatwebsite\Excel\Facades\Excel;

class DistrictAssignController extends Controller
{
    /**
     * @var DistrictAssignService
     */
    private $service;

    public function __construct(DistrictAssignService $service)
    {
        $this->service = $service;
    }

    public function getList(Request $request)
    {
        $list = DistrictAssign::query()->where('shop_id', $request->auth->shop_id)->get();
        return $this->success($list);
    }

    /**
     * 批量更新
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationExceptionAlias|ApiException
     * <AUTHOR>
     */
    public function batchUpdate(Request $request)
    {
        $validate = $this->validate($request, [
            'district_list' => 'required|array',
            'district_list.*.district_name' => 'required|string',
            'district_list.*.district_code' => 'required|int',
            'district_list.*.district_level' => 'required|int|between:0,4',
            'district_list.*.parent_code' => 'required|int',
            'district_list.*.mode' => 'required|int|between:1,3',
            'district_list.*.is_match' => 'required|int|between:0,1',
            'district_list.*.value' => 'string',
        ]);
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        \DB::transaction(function () use ($validate, $shopIds) {
            $district_list = array_get($validate, 'district_list');
            foreach ($shopIds as $shop_id) {
                foreach ($district_list as $index => $item) {
                    DistrictAssign::query()->updateOrCreate([
                        'shop_id' => $shop_id,
                        'district_code' => $item['district_code'],
                        'mode' => $item['mode'],
                    ], array_only($item, ['district_name', 'district_level', 'parent_code', 'is_match', 'value']));
                }
            }

        });

        return $this->success();
    }

    /**
     * 获取快递公司列表
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getExpressList(Request $request)
    {
        $myCompanies = CompanyService::getRelationCompanies($request->auth->user_id, $request->auth->shop_id);
        $templates = Template::with(['company', 'user'])
            ->whereIn('company_id', collect($myCompanies)->pluck('id')->toArray())
            ->get();
        $expressCompanyConfig = config('express_company');
        $expressCompanyConfig = array_pluck($expressCompanyConfig, null, 'wpCode');
        $expressCompanyList = [];
        foreach ($templates as $index => $template) {
            if (isset($expressCompanyConfig[$template['wp_code']])) {
                $var = $expressCompanyConfig[$template['wp_code']];
                $expressCompanyList[] = [
                    'wp_code' => $var['wpCode'],
                    'type' => $template['type'],
                    'union_wp_code' => $var['unionWpCode'],
                    'name' => LogisticsConst::NAME_MAP[$var['unionWpCode']],
                ];
            }
        }
        $expressCompanyList = collect($expressCompanyList)->unique('union_wp_code')->values()->toArray();
        return $this->success($expressCompanyList);
    }

    /**
     * 批量设置订单
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * <AUTHOR>
     */
    public function batchSetOrder(Request $request)
    {
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $districtAssignAll = DistrictAssign::query()
            ->whereIn('shop_id', $shopIds)
            ->get();
        foreach ($shopIds as $shopId) {
            $districtAssignArr = $districtAssignAll->where('shop_id', $shopId);
            $array = $districtAssignArr->where('mode', DistrictAssign::MODE_LOGISTICS)->toArray();
            $this->service->updateOrderByDistrict($array, $shopId, DistrictAssign::MODE_LOGISTICS);
//            $array = $districtAssignArr->where('mode', DistrictAssign::MODE_FLAG)->toArray();
//            $this->service->updateOrderByDistrict($array, $shopId,DistrictAssign::MODE_FLAG);
            $array = $districtAssignArr->where('mode', DistrictAssign::MODE_COLOR_LABEL)->toArray();
            $this->service->updateOrderByDistrict($array, $shopId, DistrictAssign::MODE_COLOR_LABEL);
        }


        return $this->success();
    }

    /**
     * 批量删除
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationExceptionAlias
     * <AUTHOR>
     */
    public function batchDelete(Request $request)
    {
        $validate = $this->validate($request, [
            'district_code_list' => 'required|array',
        ]);
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $district_code_list = array_get($validate, 'district_code_list');
        DistrictAssign::query()
            ->whereIn('shop_id', $shopIds)
            ->whereIn('district_code', $district_code_list)
            ->delete();
        foreach ($shopIds as $index => $shopId) {
            DistrictAssign::clearListCache($shopId);
        }
        return $this->success();
    }

    public function import(Request $request)
    {
        $data = $this->validate($request, [
            "file" => 'required|file',
        ]);
        $uploadedFile = $request->file('file');
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        try {
            $uploadedData = Excel::toArray(null, $uploadedFile);
        } catch (\Exception $e) {
            throw new ApiException(ErrorConst::FILE_PARSE_FAILURE);
        }
        foreach ($shopIds as $shopId) {
            $this->service->import($uploadedData, $shopId);
        }
        return $this->success();
    }

    /**
     * 导出
     * <AUTHOR>
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $shop_id = $request->auth->shop_id;

        return $this->service->export($shop_id);
    }

    /**
     * 重置
     * <AUTHOR>
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reset(Request $request)
    {
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        DistrictAssign::query()
            ->whereIn('shop_id', $shopIds)
            ->delete();
        foreach ($shopIds as $index => $shopId) {
            DistrictAssign::clearListCache($shopId);
        }
        return $this->success();
    }


}
