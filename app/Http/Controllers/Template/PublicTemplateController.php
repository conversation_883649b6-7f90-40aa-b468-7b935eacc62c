<?php

namespace App\Http\Controllers\Template;

use App\Constants\ErrorConst;
use App\Http\Controllers\Controller;
use App\Models\Template;
use App\Services\BusinessException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Laravel\Lumen\Http\ResponseFactory;

/**
 * 模板公共接口，不需要登录
 *
 */
class PublicTemplateController extends Controller
{

    /**
     * 依据目标ID获取目标内容
     * @param Request $request
     * @return Response|ResponseFactory
     * @throws BusinessException
     * @throws ValidationException
     */
    public function contentPrintTemplate(Request $request)
    {
        $data = $this->validate($request, [
            "id" => 'required|integer',
            "top" => 'numeric',
        ]);
        $id = $data['id'];
        $top = $data['top'] ?? 0;
//        $template = Template::findOrFail($id);
        $template = Template::query()->withTrashed()->find($id);
        if (empty($template)) {
            throw new BusinessException(ErrorConst::PARAM_ERROR[0], ErrorConst::PARAM_ERROR[1]);
        }
        $xmlData = $template->custom_config;
        $search = 'style="position:relative;"';
        $replaceStr = '';
        if ($top > 0){
            $replaceStr = 'style="position:relative;margin-top:'.$top.'px;"';
        }
        if (!empty($replaceStr)){
            $xmlData = str_replace($search, $replaceStr, $xmlData);
        }

        return response($xmlData, 200)->header('Content-Type', 'application/xml');

    }
    // 临时增加运行内存防止溢出
}
