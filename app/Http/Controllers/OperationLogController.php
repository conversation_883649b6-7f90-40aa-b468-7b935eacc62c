<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/6/22
 * Time: 14:19
 */

namespace App\Http\Controllers;

use App\Constants\OperationLogTypeConst;
use App\Models\OperationLog;
use App\Models\Shop;
use Illuminate\Http\Request;

class OperationLogController extends Controller
{
    public function index(Request $request)
    {
        $this->validate($request, [
//            "target_shop_id_list" => 'array',
            "shop_id_list" => 'array',
            'time_start' => 'date',
            'time_end' => 'date',
            'type_list' => 'array',
            'is_factory' => 'int',
            'offset' => 'int',
            'limit' => 'int',
        ]);

        $shop_id_list = $request->input('shop_id_list');
        $time_start = $request->input('time_start');
        $time_end = $request->input('time_end');
        $type_list = $request->input('type_list');
        $is_factory = $request->input('is_factory' ,0);
        $offset = $request->input('offset',0);
        $limit = $request->input('limit',20);

        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        $query = OperationLog::query();
        if ($is_factory) {
            $query->whereIn('target_shop_id', $shopIds);
        }else{
            $query->whereIn('shop_id', $shopIds);
        }
        if (!empty($shop_id_list)) {
            $query->whereIn('shop_id', $shop_id_list);
        }
        if (!empty($time_start)) {
            $query->where('time','>=', $time_start);
        }
        if (!empty($time_end)) {
            $query->where('time','<=', $time_end);
        }
        if (!empty($type_list)) {
            $query->whereIn('type', $type_list);
        }
        $count = $query->count();
        $list = $query->limit($limit)->offset($offset)->with(['shop:id,shop_name', 'targetShop:id,shop_name'])
            ->orderBy('id','desc')->get();

        return $this->success(['count' => $count, 'list' => $list]);
    }
}
