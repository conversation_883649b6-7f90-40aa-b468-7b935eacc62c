<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/5/20
 * Time: 15:06
 */

namespace App\Http\Controllers\Order;


use App\Http\Controllers\Controller;
use App\Models\Goods;
use App\Models\QueryTemplate;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class QueryTemplateController extends Controller
{
    /**
     * <AUTHOR>
     * 2022-1-27去掉userId
     * 查询模板列表
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function index(Request $request)
    {
        $this->validate($request, [
        ]);
        $condition = [];
        //$condition[] = ['user_id', $request->auth->user_id];
        $condition[] = ['shop_id', $request->auth->shop_id];
        $list = QueryTemplate::query()->where($condition)->get();

        return $this->success($list);
    }

    /**
     * 创建查询模板
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function create(Request $request)
    {
        $data = $this->validate($request, [
            'name' => 'required|string',
            'data' => 'required|array',
        ]);

	    $conditions = [
		    'shop_id' => $request->auth->shop_id,
	    ];
	    $list       = QueryTemplate::query()->where($conditions)->get();
	    $count      = collect($list)->count();
	    if ($count >= 30) {
		    return $this->fail('最多创建30条');
	    }

	    $exist = collect($list)->where('name', $data['name'])->first();
	    if ($exist) {
		    return $this->fail('一键查询名称重复');
	    }

        QueryTemplate::query()->create(array_merge($conditions, [
            'user_id' => $request->auth->user_id,
        	'name' => $data['name'],
        	'data' => json_encode($data['data'], true),
        ]));

        return $this->success();
    }

    /**
     * <AUTHOR>
     * 去掉userId
     * 更新查询模板
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function update(Request $request)
    {
        $data = $this->validate($request, [
            'id' => 'required|int',
            'name' => 'required|string',
            'data' => 'required|array',
        ]);

	    $simple = QueryTemplate::query()->findOrFail($data['id']);
	    $count  = QueryTemplate::query()->where([
		    ['shop_id', $request->auth->shop_id],
		    ['name', $data['name']],
	    ])->where('id', '<>', $data['id'])->count();
	    if ($count > 0) {

		    return $this->fail('一键查询名称重复');
	    }

        $simple->name = $data['name'];
        $simple->data = json_encode($data['data'], true);
        $simple->save();

        return $this->success();
    }

    /**
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 删除模板消息
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function delete(Request $request, $id)
    {
        $condition = [];
        $condition[] = ['shop_id', $request->auth->shop_id];
        $condition[] = ['id', $id];
        QueryTemplate::query()->where($condition)->delete();

        return $this->success();
    }
}
