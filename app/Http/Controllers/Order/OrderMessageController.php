<?php

namespace App\Http\Controllers\Order;

use App\Constants\DoudianMsgTag;
use App\Constants\KsMsgTag;
use App\Http\Controllers\Controller;
use App\Jobs\DoudianMsg\DyMsgServiceManagerJob;
use App\Jobs\KsMsg\KsMsgServiceManagerJob;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use SpiUtils;

/**
 * 订单消息
 */
class OrderMessageController extends Controller
{
    /**
     * 抖音的消息回调
     * @param Request $request
     * @return JsonResponse
     */
    public function dyPushMsg(Request $request)
    {
        // 临时加上log排查
//        \Log::info('pushMsg:', [$request->all()]);

        $msg = $request->all();
        if (!is_array($msg)) {
            Log::error('消息体格式错误', [$request->all()]);
            return response()->json(['code' => 0, 'msg' => 'success']);
        }
        Log::info("收到消息回调", [$msg]);

        foreach ($msg as $item) {
            if (in_array($item['tag'], DoudianMsgTag::NEED_MSG_TAG_ARR)) {
                dispatch(new DyMsgServiceManagerJob($item));
            }
        }

        return response()->json(['code' => 0, 'msg' => 'success']);
    }


    /**
     * 快手的消息回调
     * @param Request $request
     * @return JsonResponse
     */
    public function ksPushMsg(Request $request)
    {
        try {
            //消息解密
            $key = env('KS_QUEUE_SECRET', 'JM3S+Jq60axCXYl6XPo/zQ==');
            $message = file_get_contents("php://input");
//        Log::info('post_msg_param', [$message]);
            $params = json_decode(openssl_decrypt(base64_decode($message), 'AES-128-CBC', base64_decode($key), OPENSSL_RAW_DATA), true);

            if (isset($params['event']) && in_array($params['event'], KsMsgTag::NEED_MSG_TAG_ARR)) {
                dispatch(new KsMsgServiceManagerJob($params));
            }

            return response()->json(['result' => 1], 200);
        }catch (\Throwable $e){
            Log::error('快手消息回调异常', [$e->getMessage()]);
            return response()->json(['result' => 1], 200);
        }
    }

    /**
     * 淘宝奇门消息
     * <AUTHOR>
     * @param Request $request
     * @param $tag
     */
    public function tbQmMsg(Request $request)
    {
        \Log::info('tbQmMsg:', [$request->all(),$request->header(),$request->server()]);
        \Log::info('tbQmMsg2:', [http_build_query($request->input()), config('socialite.taobao_top.client_secret')]);


        $bool = SpiUtils::checkSign4TextRequest(null, config('socialite.taobao_top.client_secret'));
        if (!$bool) {
            $result = [
                'result' => [
                    'errorCode' => 'sign-check-failure',
                    'errorMsg' => 'Illegal request',
                    'success' => false,
                ]
            ];
            return response()->json($result);
        }
        $method = $request->input('method');
        $result = [
            'result' => [
                'errorCode' => 1,
                'errorMsg' => '未实现功能',
                'success' => false,
            ]
        ];
        switch ($method){
            case 'qimen.taobao.qianniu.cloudkefu.address.self.modify':
                $result = $this->handleTbQmAddressModify($request->input());
                break;
            default:
                break;
        }
        return response()->json($result, 200);
    }

    /**
     * @see https://developer.alibaba.com/docs/doc.htm?treeId=5&articleId=109107&docType=1
     * <AUTHOR>
     * @param array $input
     * @return array[]
     */
    private function handleTbQmAddressModify(array $input)
    {
        $tid = $input['bizOrderId'];
        $orderInfo = Order::firstByTid($tid);
        $resp = [
            'errorCode' => 0,
            'errorMsg' => '',
            'success' => true,
        ];
        if (!empty($orderInfo)) {
            $oaid = $input['oaid'] ??'';
            $modifiedAddress = $input['modifiedAddress'];
            $updateData = [
                "receiver_state" => $modifiedAddress['province'] ?? null, //收货人省份
                "receiver_city" => $modifiedAddress['city'] ?? null, //收货人城市
                "receiver_district" => $modifiedAddress['area'] ?? null, //收货人地区
                "receiver_town" => $modifiedAddress['town'] ?? null, //收货人街道
                "receiver_name" => $modifiedAddress['name'] ?? null, //收货人名字
                "receiver_phone" => md5($oaid), // 收货人手机，md5 是为了缩短字符串长度
                "address_md5" => md5($oaid), //
                "receiver_address" => $modifiedAddress['addressDetail'] ?? '', //收件人详细地址
            ];
            if (empty($oaid)) {
                $resp = [
                    'errorCode' => 3004,
                    'errorMsg' => '丢失地址信息，无法修改',
                    'success' => false,
                ];
            }elseif ($orderInfo->print_status > Order::PRINT_STATUS_NO) {
                $resp = [
                    'errorCode' => 1001,
                    'errorMsg' => '订单已安排发货无法修改',
                    'success' => false,
                ];
            }elseif ($orderInfo->order_status >= Order::ORDER_STATUS_DELIVERED) {
                $resp = [
                    'errorCode' => 1015,
                    'errorMsg' => '订单已发货无法修改',
                    'success' => false,
                ];
            }else{
                $row1 = Order::query()->where('id', $orderInfo->id)->update($updateData);
                $row2 = OrderCipherInfo::query()->where('order_id', $orderInfo->id)->update([
                    'oaid' => $oaid,
                ]);
                if ($row1 == 0 || $row2 == 0) {
                    $resp = [
                        'errorCode' => 3001,
                        'errorMsg' => '修改地址失败',
                        'success' => false,
                    ];
                }
            }
        }
        return ['result' => $resp];
    }

}
