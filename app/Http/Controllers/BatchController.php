<?php

namespace App\Http\Controllers;

use App\Models\Batch;
use App\Models\Package;
use App\Models\PrintRecord;
use App\Models\Shop;
use App\Models\WaybillHistory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BatchController extends Controller
{
    public function index(Request $request)
    {
        // 暂时不用这个
        return $this->success([]);
        $data = $this->validate($request, [
            'identifierArr' => 'required|array|max:10',
            'type' => 'required|between:1,10',
        ]);
        $page      = $request->input('page', 1);
        $pageSize  = $request->input('page_size', 20);
        $identifierArr = array_get($data, 'identifierArr');
        $type = array_get($data, 'type');
        $shopIds = Shop::shopIdsByidentifier($identifierArr);
        $list = Batch::query()
            ->where('type', $type)
            ->whereIn('shop_id', $shopIds)
            ->orderBy('id', 'desc')
            ->forPage($page, $pageSize)
            ->get();
        return $this->success($list);
    }

    public function indexByPrintRecord(Request $request)
    {
        $data = $this->validate($request, [
            'shopIdList' => 'array',
            'mode' => 'int',
        ]);
        $page      = $request->input('page', 1);
        $pageSize  = $request->input('page_size', 20);
//        $shopIdList = array_get($data, 'shopIdList');
        $shopIdList = $this->getShopIdList($request);
        $mode = array_get($data, 'mode');
//        $shopIds = ShopBind::getValidRelationShopIds($request->auth->shop_id, $ownerIdList);
        // PrintRecord 查询 7 天内的打印记录
        $query = PrintRecord::query()
            ->whereIn('to_shop_id', $shopIdList)
            ->groupBy(['batch_no'])
            ->orderByDesc('created_at')
            ->forPage($page, $pageSize)
            ->whereBetween('created_at', [Carbon::today()->subDays(7), Carbon::now()]);
        if (!is_null($mode)) {
            $query->where('version', $mode);
        }
        $list = $query
            ->select(['batch_no', \DB::raw('count(*) as count'), \DB::raw('max(created_at) as created_at'),'shop_id'])
            ->get();
        return $this->success($list);
    }

    public function indexByWaybillHistories(Request $request)
    {
        $data = $this->validate($request, [
            'shopIdList' => 'array',
            'mode' => 'int',
        ]);
        $page      = $request->input('page', 1);
        $pageSize  = $request->input('page_size', 20);
        $shopIdList = $this->getShopIdList($request);
        $mode = array_get($data, 'mode');
        $query = WaybillHistory::query()
            ->whereIn('to_shop_id', $shopIdList)
            ->groupBy(['batch_no'])
            ->orderByDesc('created_at')
            ->forPage($page, $pageSize)
            ->whereBetween('created_at', [Carbon::today()->subDays(7), Carbon::now()]);
        if (!is_null($mode)) {
            $query->where('version', $mode);
        }
        $list = $query
            ->select(['batch_no', \DB::raw('count(*) as count'), \DB::raw('max(created_at) as created_at'),'shop_id'])
            ->get();
        $list = $list->each(function($item){
            $arr = explode('-', $item['batch_no']);
            $item['batch_no'] = $arr[0];
            return $item;
        })->values()->toArray();
        return $this->success($list);
    }

    /**
     * 索引已发货列表
     * <AUTHOR>
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function indexByShippedList(Request $request)
    {
        $data = $this->validate($request, [
            'shopIdList' => 'array',
            'mode' => 'int',
            'printStatus' => 'int',
            'start_time' => 'date',
            'end_time' => 'date',
        ]);
        $page      = $request->input('page', 1);
        $pageSize  = $request->input('page_size', 20);
        $printStatus  = $request->input('printStatus', null);
        $shopIdList = array_get($data, 'shopIdList');
        $startTime = array_get($data, 'start_time',Carbon::today()->subDays(7)->toDateTimeString());
        $endTime = array_get($data, 'end_time',Carbon::today()->endOfDay()->toDateTimeString());
//        $shopIdList = $this->getShopIdList($request);
        $mode = array_get($data, 'mode',3);
//        $shopIds = ShopBget($data, 'mode');
////        $shopIds = Shopind::getValidRelationShopIds($request->auth->shop_id, $ownerIdList);
        // 查询 7 天内的打印记录
        $query = Package::query()
//            ->whereIn('shop_id', [$request->auth->shop_id])
            ->where('waybill_status', Package::WAYBILL_STATUS_SUCCESS)
            ->whereIn('status', [Package::ORDER_STATUS_DELIVERED,Package::ORDER_STATUS_PART_DELIVERED])
            ->whereIn('to_shop_id', $shopIdList)
            ->groupBy(['batch_no'])
            ->orderByDesc('created_at')
            ->forPage($page, $pageSize)
            ->whereBetween('created_at', [$startTime, $endTime]);
        if (!is_null($mode)) {
            $query->where('version', $mode);
        }
        if (!is_null($printStatus)) {
            $query->where('print_status', $printStatus);
        }
//        if (!empty($shopIdList)) {
//            $query->whereExists(function ($query)use ($shopIdList){
//                $query->select(DB::raw(1))
//                    ->from('waybill_histories')
//                    ->whereRaw('waybill_histories.package_id = packages.id')
//                    ->whereIn('to_shop_id', $shopIdList);
//            });
//        }
        $list = $query
            ->select(['batch_no', \DB::raw('count(*) as count'), \DB::raw('max(created_at) as created_at'),'shop_id'])
            ->get();
        return $this->success($list);
    }

}
