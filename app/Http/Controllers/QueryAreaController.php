<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\QueryArea;
use App\Models\ShopExtra;
use App\Services\QueryAreaService;
use Illuminate\Http\Request;

/**
 * <AUTHOR>
 */
class QueryAreaController extends Controller
{

    /**
     * @var QueryAreaService
     */
    private $service;

    public function __construct(QueryAreaService $service)
    {
        $this->service = $service;
    }

    /**
     * <AUTHOR>
     * 22-1-25去掉了user_id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
	public function index(Request $request)
	{
		$condition   = [];
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
		$condition[] = ['shop_id', $shopId];
		$list        = QueryArea::query()->where($condition)->get(['id','name','template_id','union_wp_code','data','include','custom_original_data','created_at']);

		return $this->success($list);
	}

    public function info(Request $request)
    {
        $params = $this->validate($request, [
            'id' => 'required|int',
        ]);
        $id = array_get($params, 'id');
        $info = QueryArea::query()->where('id',$id)->first();
        return $this->success($info);
    }

    /**
     * <AUTHOR>
     * 22-1-25 查询去掉userId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
	public function create(Request $request)
	{
		$params = $this->validate($request, [
			'name' => 'string',
			'data' => 'array',
			'include' => 'required|string',
			'union_wp_code' => 'string',
			'custom_original_data' => 'string',
			'version' => 'int',
        ]);
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $version = $params['version'] ?? 1;

		$conditions = [
			'shop_id' => $shopId
		];
		$list       = QueryArea::where($conditions)->get();
		$count      = collect($list)->count();
		if ($count > 50) {
			return $this->fail('最多创建10条');
		}

//		$exist = collect($list)->where('name', $params['name'])->first();
//		if ($exist) {
//			return $this->fail('名称重复');
//		}

        $data = QueryArea::handleStr($params['data'] ?? '', $version);
        if ($version == 2) {
            $customDistrictStr = $this->service->handleCustomDataV2($params['custom_original_data'] ?? '');
        }else{
            $customDistrictStr = $this->service->handleCustomData($params['custom_original_data']??'');
        }

		$templateId = $request->input('template_id', 0);
		QueryArea::create(array_merge($data, [
			'user_id' => $request->auth->user_id,
			'shop_id' => $shopId,
            'name' => $params['name'] ?? '',
			'include'     => $params['include'],
			'union_wp_code' => $params['union_wp_code']??'',
            'custom_original_data' => $params['custom_original_data'] ?? '',
            'custom_district_str' => $customDistrictStr,
            'template_id' => $templateId,
            'version' => $version
		]));

		return $this->success();
	}

    /**
     * <AUTHOR>
     * 22-1-25 查询去掉userId
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
	public function update(Request $request, $id)
	{
		$params = $this->validate($request, [
			'name' => 'string',
			'data' => 'array',
			'include' => 'required|string',
			'union_wp_code' => 'string',
			'custom_original_data' => 'string',
            'version' => 'int',
        ]);
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $version = $params['version'] ?? 1;
        $simple = QueryArea::query()->findOrFail($id);
//		$count  = QueryArea::query()->where([
//			['shop_id', $shopId],
//			['name', $params['name']],
//		])->where('id', '<>', $id)->count();
//		if ($count > 0) {
//			return $this->fail('名称已被使用');
//		}

        $data = QueryArea::handleStr($params['data'] ?? '', $version);
        if ($version == 2) {
            $customDistrictStr = $this->service->handleCustomDataV2($params['custom_original_data'] ?? '');
        }else{
            $customDistrictStr = $this->service->handleCustomData($params['custom_original_data']??'');
        }
        $templateId = $request->input('template_id', 0);

		$simple->name         = $params['name']??'';
		$simple->province_str = $data['province_str'];
		$simple->city_str     = $data['city_str'];
		$simple->district_str = $data['district_str'];
		$simple->data         = $data['data'];
		$simple->template_id  = $templateId;
		$simple->include      = $params['include'];
        $simple->union_wp_code = $params['union_wp_code']??'';
        $simple->custom_original_data = $params['custom_original_data'] ?? '';
        $simple->custom_district_str = $customDistrictStr;
        $simple->save();

		return $this->success();
	}

    /**
     * <AUTHOR>
     * 22-1-25 去掉了user_id
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
	public function delete(Request $request, $id)
	{
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
		$condition   = [];
		$condition[] = ['shop_id', $shopId];
		$condition[] = ['id', $id];
		QueryArea::query()->where($condition)->delete();

		return $this->success();
	}
    /**
     * 批量设置订单
     */
    public function batchSetOrder(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $params = $this->validate($request, [
        ]);
        $shopExtra = ShopExtra::firstByShopId($shopId);
        $default_wp_code = $shopExtra->preset_logistics_union_wp_code;
        $saveData = [
            'smart_logistics' => $default_wp_code,
        ];
        Order::query()
            ->where('shop_id', $shopId)
            ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
            ->update($saveData);
        if ($shopExtra->preset_logistics_district_switch == 1) {
            $wpCodeDistrictList = $this->service->getWpCodeDistrictList($shopId);
            $wpCodeDistrictListGroup = collect($wpCodeDistrictList)->groupBy('union_wp_code')->toArray();
            foreach ($wpCodeDistrictListGroup as $union_wp_code => $item) {
                if (empty($union_wp_code)) {
                    continue;
                }
                $saveData = [];
                $saveData['smart_logistics'] = $union_wp_code;
                Order::query()
                    ->where('shop_id', $shopId)
                    ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
                    ->whereIn('district_code', array_pluck($item, 'code'))
                    ->update($saveData);
            }
        }

        return $this->success();
    }

}
