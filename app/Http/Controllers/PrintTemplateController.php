<?php

namespace App\Http\Controllers;

use App\Models\PrintTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class PrintTemplateController extends Controller
{
    /**
     * 获取当前用户的打印模板分页列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $userId = $request->auth->user_id;

        $templates = PrintTemplate::query()->where('user_id', $userId)
            ->orderBy('id', 'desc')
            ->get();

        return $this->success($templates);
    }

    /**
     * 创建新的打印模板
     *
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function create(Request $request): JsonResponse
    {
        $this->validate($request, [
            'style' => 'required|integer',
            'name' => 'required|string|max:128',
            'width' => 'required|numeric',
            'height' => 'required|numeric',
            'waybill_type' => 'nullable|string|max:100',
            'show_logo' => 'boolean',
            'horizontal' => 'numeric',
            'horizontal_type' => 'integer',
            'vertical' => 'numeric',
            'vertical_type' => 'integer',
            'merge_template_url' => 'nullable|string',
            'wp_code' => 'required|string|max:64',
            'wp_name' => 'required|string|max:64',
            'custom_config' => 'nullable|string',
            'print_contents' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['user_id'] = $request->auth->user_id;

        $template = PrintTemplate::create($data);


        return $this->success($template);

    }

    /**
     * 获取指定打印模板的详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request,int $id): JsonResponse
    {
        $userId = $request->auth->user_id;
        $template = PrintTemplate::where('user_id', $userId)
            ->where('id', $id)
            ->first();

        if (!$template) {
            return $this->fail('模板不存在或无权访问');
        }

        return $this->success($template);

    }

    /**
     * 更新指定的打印模板
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     * @throws ValidationException
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $this->validate($request, [
            'style' => 'integer',
            'name' => 'string|max:128',
            'width' => 'numeric',
            'height' => 'numeric',
            'waybill_type' => 'nullable|string|max:100',
            'show_logo' => 'boolean',
            'horizontal' => 'numeric',
            'horizontal_type' => 'integer',
            'vertical' => 'numeric',
            'vertical_type' => 'integer',
            'merge_template_url' => 'nullable|string',
            'wp_code' => 'nullable|string|max:64',
            'wp_name' => 'nullable|string|max:64',
            'custom_config' => 'nullable|string',
            'print_contents' => 'nullable|string',
        ]);

        $userId = $request->auth->user_id;
        $template = PrintTemplate::where('user_id', $userId)
            ->where('id', $id)
            ->first();

        if (!$template) {
            return $this->fail('模板不存在或无权访问');
        }

        $template->update($request->all());

        return $this->success($template);
    }

    /**
     * 删除指定的打印模板
     *
     * @param int $id
     * @return JsonResponse
     */
    public function delete(Request $request,int $id): JsonResponse
    {
        $userId = $request->auth->user_id;
        $template = PrintTemplate::where('user_id', $userId)
            ->where('id', $id)
            ->first();

        if (!$template) {
            return $this->fail('模板不存在或无权访问');
        }
        $template->delete();
        return $this->success();
    }
}
