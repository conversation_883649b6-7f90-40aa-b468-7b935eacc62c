<?php

namespace App\Http\Controllers\Api;

use App\Constants\AuthStateTypeConst;
use App\Http\Controllers\Controller;
use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Auth\Impl\JdAuthImpl;
use App\Services\Order\OrderServiceManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * OpenAPIV3跟订单相关的接口
 */
class OpenApiV3OrderController extends BaseOpenApiController
{


    /**
     * 获取备注
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function getSyncRemarks(Request $request)
    {
        $data = $this->validate($request, [
            'shopCode' => 'required|string',
            'startTime' => 'required|string',
            'endTime' => 'required|string',
            'page' => 'required|int',
            'sortType' => 'int'
        ]);
        $orderService = OrderServiceManager::create();
        $orderService->setShop($request->shop);
        return $this->successForOpenApi($orderService->getRemarks($data['startTime'], $data['endTime'], $data['page'], $data['sortType'] ?? 1));

    }

}
