<?php

namespace App\Http\Controllers;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\ExportTask;
use App\Models\Shop;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class CommonController extends Controller
{
    /**
     * 导入并解析 Excel
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     * @throws \Illuminate\Validation\ValidationException
     * <AUTHOR>
     */
    public function importParseExcel(Request $request)
    {
        $data = $this->validate($request, [
            "file" => 'required|file',
        ]);
        // 临时增加运行内存防止溢出
//        ini_set('memory_limit', '1024M');
        $uploadedFile = $request->file('file');
        if (!in_array($request->file('file')->getClientOriginalExtension(), ['xlsx', 'csv'])) {
            throw new ApiException(ErrorConst::FILE_EXTENSION_NOT_SUPPORTED);
        }
        $header = [];
        try {
//            $uploadedData = Excel::toArray(null, $uploadedFile);
            $uploadedData = (new FastExcel)->import($uploadedFile, function ($value)use (&$header) {
                if (empty($header)){
                    $header = array_keys($value);
                }
                return array_values($value);
            });
            $uploadedData = [$uploadedData];
            if (!empty($uploadedData[0])){
                $uploadedData[0] = collect($uploadedData[0])->unique(function ($value){
                    return implode(',', $value);
                })->toArray();
                array_unshift($uploadedData[0], $header);
            }

        } catch (\Exception $e) {
            throw new ApiException(ErrorConst::FILE_PARSE_FAILURE);
        }
        return $this->success($uploadedData);
    }


    /**
     * 导出
     * @param Request $request
     * @return BinaryFileResponse
     * @throws ErrorCodeException
     */
    public function exportFile(Request $request)
    {
        $id = $request->input('id');
        $taskInfo = ExportTask::query()->where('id', $id)->first();
        if(!isset($taskInfo)){
            throw_error_code_exception(StatusCode::EXPORT_FILE_NOT_EXIST);
        }
        $filePath = $taskInfo->file_path;
        $fileName = $name = basename($filePath);
        $extPos=strripos($fileName,'.');
        $ext=substr($fileName,$extPos);
        if ($taskInfo->name) {
            $fileName = $taskInfo->name.$ext;
        }
        \Log::info("下载文件".$fileName);
        return response()->download($filePath, $fileName, ['Content-Type' => 'application/vnd.ms-excel']);
    }

    /**
     * 获取导出文件列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportList(Request $request)
    {
        $shopId = $request->auth->shop_id;
        $type = $request->input('type');
        $offset=$request->input('offset',0);
        $limit=$request->input('limit',10);
        $query = ExportTask::query()
            ->where(['shop_id' => $shopId, 'type' => $type])
            ->orderBy('id', 'desc');
        $rowsFound=$query->count();
        $result = $query
            ->offset($offset)
            ->limit($limit)
            ->get();
        $pagination = [
            'rows_found' => $rowsFound??null,
            'offset' => $offset,
            'limit' => $limit
        ];
        return $this->success(['pagination' => $pagination,$result]);
    }
}
