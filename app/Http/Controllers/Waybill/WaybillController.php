<?php

namespace App\Http\Controllers\Waybill;

use App\Constants\PlatformConst;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Models\Waybill;
use App\Models\Company;
use App\Models\Template;
use App\Models\WaybillShareAction;
use App\Services\BusinessException;
use App\Services\Waybill\DY\DYApi;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\WaybillUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 第三方电子面单Controller
 */
class WaybillController extends Controller
{
    /**
     * @var WaybillUtil $waybillUtil
     */
    protected $waybillUtil;

    /**
     * @param WaybillUtil $waybillUtil
     */
    public function __construct(WaybillUtil $waybillUtil)
    {
        $this->waybillUtil = $waybillUtil;
    }

    /**
     * 查询面单授权
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $currentShop = intval($request->input('shopId', $request->auth->shop_id));;
        $scope = $request->input('scope', [1]);
        $withShopId = $request->input('shop_id', null);
        $includeThird = $request->input('includeThird', false);
        return $this->success($this->waybillUtil->getWaybillAuth($currentShop, $scope, $withShopId, $includeThird));
    }

    /**
     * 电子面单授权Url
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function authUrl(Request $request): JsonResponse
    {
        $this->validate($request, [
            "shop_id" => "required",
            "auth_source" => "required|int",
        ]);
        $bool = WaybillServiceManager::checkAuthSource($request->input('auth_source'));
        if (!$bool) {
            throw new ApiException(ErrorConst::PLATFORM_TYPE_ERROR);
//            throw new \Exception('平台类型错误');
        }
        $waybillService = WaybillServiceManager::init($request->input('auth_source'));
        $url = $waybillService->getLoginUrl($request->input('shop_id'));

        return $this->success($url);
    }

    /**
     * 电子面单授权
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws BusinessException
     * @throws ValidationException
     */
    public function auth(Request $request): JsonResponse
    {
        $authInfo = $this->validate($request, [
            "code" => "required",
            "shop_id" => "required",
            "auth_source" => "required|int",
//            "access_token" => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "owner_id"     => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "owner_name"   => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "expires_in"   => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
        ]);
        $result = Waybill::auth($authInfo, $request->auth->user_id, $request->auth->shop_id);
        Log::info("电子面单授权", $result);
        return $this->success($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 电子面单服务查询
     */
    public function waybillSearch(Request $request): JsonResponse
    {
        $this->validate($request, [
            "auth_source" => "int",
            "owner_id" => "string",
            "wp_code" => "string",
        ]);
        $where = ['shop_id' => $request->auth->shop_id];
        if ($request->input('shop_id')) {
            $where = ['shop_id' => $request->input('shop_id')];
        }
        $auth = Waybill::where($where);
        $source = $request->input('auth_source', 0);
        $ownerId = $request->input('owner_id', 0);
        //拼多多站内 && 抖音
        if (in_array($source, [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
            $waybills = Shop::where('user_id', $request->auth->user_id)->get();
            if ($waybills->isEmpty()) {
                return $this->success();
            }
            $result = [];
            foreach ($waybills as $key => $value) {
                $waybillService = WaybillServiceManager::init($source, $value->access_token);
                $waybill = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''), $value->service_id);
                $result[$key]['owner_id'] = $value->identifier;
                $result[$key]['owner_name'] = $value->shop_name;
                $result[$key]['list'] = $waybill;
            }

            return $this->success($result);
        }
        if ($source) {
            $auth->where('auth_source', $source);
        }
        if ($ownerId) {
            $auth->where('owner_id', $ownerId);
        }
        $waybills = $auth->get();

        $old = collect($waybills)->where('auth_source', Waybill::AUTH_SOURCE_TWC)
            ->where('created_at', '<', '2020-05-07 17:00:00')
            ->count();
        if ($old > 0) {
            return $this->fail('淘宝电子面单授权失效，请重新授权');
        }

        if ($waybills->isEmpty()) {
            return $this->success();
        }

        $result = [];
        foreach ($waybills as $key => $value) {
            $waybillService = WaybillServiceManager::init($value->auth_source, $value->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
            $result[$key]['owner_id'] = $value->owner_id;
            $result[$key]['owner_name'] = $value->owner_name;
            $result[$key]['list'] = $waybill;
        }

        return $this->success($result);
    }

    /**
     * @param Request $request
     * @param         $ownerId
     * @return JsonResponse
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 面单查询 by ownerId
     */
    public function getWaybillByOwnerId(Request $request, $ownerId)
    {
        $auth = Waybill::where('shop_id', $request->auth->shop_id)
            ->where('owner_id', $ownerId)->firstOrFail();
        $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
        $waybills = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
        $result = [];
        foreach ($waybills as $waybill) {
            $result[] = collect(config('express_company'))->where('wpCode', $waybill->wp_code)->first();
        }

        return $this->success($result);
    }

    /**
     * @param Request $request
     * @param         $ownerId
     * @return JsonResponse
     * <AUTHOR> 22-1-27 去掉userID
     * 面单解绑授权
     */
    public function unbind(Request $request, $ownerId)
    {
        $ret = Waybill::where('shop_id', $request->auth->shop_id)
            ->where('owner_id', $ownerId)
            ->delete();
        if (!$ret) {
            return $this->fail('解绑失败');
        }

        return $this->success();
    }


    /**
     * 获取电子面单的订阅信息
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws ApiException
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 面单查询 by ownerId
     */
    public function getWaybillByOwnerIdAndBranch(Request $request): JsonResponse
    {
        $this->validate($request, [
            'owner_id' => "required",
            'auth_source' => "required"
        ]);
        $ownerId = $request->input('owner_id');
        $authSource = $request->input('auth_source');
//        if (!in_array($authSource, [Waybill::AUTH_SOURCE_DY,Waybill::AUTH_SOURCE_TAOBAO,Waybill::AUTH_SOURCE_KS,Waybill::AUTH_SOURCE_WXSP])) {
//            $auth = Waybill::where('shop_id', $request->auth->shop_id)
//                ->where('owner_id', $ownerId)->firstOrFail();
//        } else {
////            $auth = Shop::query()->where('identifier', $ownerId)->firstOrFail();
//            $auth = Shop::firstByIdentifier($ownerId);
//            $auth->auth_source = $authSource;
//        }
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $waybillAuth = WaybillUtil::findShopWaybillAuth($shopId, $authSource, $ownerId, false);
        $waybillService = WaybillServiceManager::init($waybillAuth->auth_source, $waybillAuth->access_token);
        if ($waybillAuth instanceof Shop) {
            $waybillService->setShop($waybillAuth);
        }

        $waybills = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
        $expressCompanyArr = [];
        foreach ($waybills as $waybill) {
            $temp = collect(config('express_company'))->where('wpCode', $waybill['wp_code'])->first();
            if (!empty($temp)) {
                $expressCompanyArr[] = $temp;
            }
        }
//        return $this->success($result);
        return $this->success(compact('expressCompanyArr', 'waybills'));
    }



    /**
     * 只是获取当前登录店铺的电子面单服务列表（含三方和站外）
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 22-1-26去掉了userId
     * 电子面单服务查询
     */
    public function waybillQuery(Request $request): JsonResponse
    {
        $this->validate($request, [
            "auth_source" => "int",
            "wp_code" => "string",
        ]);
        $currentShop = intval($request->input('shopId', $request->auth->shop_id));
        $scope = $request->input('scope', [1]);

//        //拼多多站内
//        if (in_array(config('app.platform'), [PlatformConst::TAOBAO, Waybill::AUTH_SOURCE_JD])) {
//            $waybills = Shop::where('user_id', $request->auth->user_id)->get();
//            if ($waybills->isEmpty()) {
//                return $this->success();
//            }
//            $result = [];
//            foreach ($waybills as $key => $value) {
//                switch (config('app.platform')) {
//                    case PlatformConst::TAOBAO:
//                        $source = Waybill::AUTH_SOURCE_TAOBAO;
//                        break;
//                    case Waybill::AUTH_SOURCE_JD:
//                        $source = Waybill::AUTH_SOURCE_JD;
//                        break;
//                    default:
//                        $source = 0;
//                }
//                $waybillService             = WaybillServiceManager::init($source, $value->access_token);
//                $waybill                    = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''), $value->service_id);
//                $result[$key]['owner_id']   = $value->identifier;
//                $result[$key]['owner_name'] = $value->shop_name ?? $value->name;
//                $result[$key]['auth_source']= $source;
//                $result[$key]['list']       = $waybill;
//            }
//
//            return $this->success($result);
//        }


        return $this->success($this->waybillUtil->getAllWaybillSubscription($currentShop, "", $scope));
    }

    /**
     * @throws ValidationException
     */
    public function waybillQueryV4(Request $request): JsonResponse
    {
        $this->validate($request, [
            "auth_source" => "int",
            "wp_code" => "string",
            "scope" => "array",
        ]);
        $currentShop = intval($request->input('shopId', $request->auth->shop_id));
        // 缓存 1 分钟
        $companies = Cache::remember('waybillQueryV4:'.$currentShop, 1, function () use ($currentShop,$request) {
            return $this->waybillUtil->waybillQueryV4($currentShop, $request->auth->user_id);
        });
        return $this->success($companies);
    }

//    /**
//     * 我的快递公司
//     * @param Request $request
//     * @return JsonResponse
//     */
//    public function waybillQueryByApi(Request $request): JsonResponse
//    {
//        $user_id = $request->auth->user_id;
//        $user = User::find($user_id);
//        $shopList = $user->shops();
//        foreach ($shopList as $index => $shop) {
//            $platform = Shop::getPlatform();
//            $orderService = OrderServiceManager::create($platform);
//            $orderService->setShop($shop);
//            $orderService->batchDecrypt();
//        }
//    }




}
