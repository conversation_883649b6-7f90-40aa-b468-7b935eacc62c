<?php

namespace App\Http\Controllers\Goods;

/*
 * 商品编辑
 */
class GoodsEditItem
{

    /**
     * @var string $numIid
     */
    public $numIid;

    /**
     * @var null|string $customTitle
     */
    public $customTitle;

    /**
     * @var GoodsSkuEditeItem[] $skus
     */
    public $skus=[];

    public static function ofArray($item): self
    {
        $obj = new self();
        $obj->numIid=array_get($item,'numIid');
        $obj->customTitle=array_get($item,'customTitle');
        if(array_has($item,'skus')) {
            $obj->skus=GoodsSkuEditeItem::ofArray($item['skus']);
        }
        return $obj;
    }


}
