<?php

namespace App\Http\Controllers\Shop;

use App\Exceptions\ClientException;
use App\Http\Controllers\Controller;
use App\Services\Client\DyClient;
use App\Services\Client\JdClient;
use App\Services\Shop\ShopService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * JD店铺控制器
 */
class JdController extends Controller
{
    /**
     * 获取jd nps token
     * @param Request $request
     * @return JsonResponse
     * @throws ClientException
     * @throws \Throwable
     */
    public function getNpsTokens(Request $request): JsonResponse
    {
//        return $this->success([]) ;
        $shopIds = $request->input('shopIds');
        $shops = ShopService::shopsByIds($shopIds);
        $carbon = Carbon::now();
        $result = [];
        foreach ($shops as $shop) {
            //比较过期时间和当前时间

            try {
                if ($shop->expire_at < $carbon) {
                    \Log::info("JD token过期", [$shop->expireAt, $carbon]);
                    continue;
                }
                $accessToken = $shop->access_token;
                $jdClient = JdClient::newInstance($accessToken);
                $apiParams = ["tokenStr"=>$accessToken];

                $response = $jdClient->execute("jingdong.jos.isv.token.encryption", $apiParams);
                \Log::info("获取JD token", [$response]);
                $data=$response['jingdong_jos_isv_token_encryption_responce']['returnType']['data'];
                if (!empty($data)) {
                    $result[] = ["shopId"=>$shop->id,"token" => $data ];
                }
            } catch (\Throwable $exception) {

                \Log::error("获取JD token失败", $exception->getTrace());

            }

        }
        return $this->success($result);

    }
}
