<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\ShopBindSort;
use App\Services\Shop\ShopBindService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * 更店铺绑定相关
 */
class ShopBindController extends Controller
{

    private  $shopBindService ;

    /**
     * @param $shopBindService
     */
    public function __construct(ShopBindService $shopBindService)
    {
        $this->shopBindService = $shopBindService;
    }

    /**
     * 绑定授权
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function changeAuth(Request $request)
    {
        $data = $this->validate($request, [
            'invite_code' => 'required|string',
            'type' => 'required|int|between:1,3',
            'remark' => 'string',
        ]);

        $remark = $request->input('remark');
        $shop = Shop::find($request->auth->shop_id);
        //不能传入当前店铺的shopCode
        if ($shop->shop_code == trim($data['invite_code'])) {
            return $this->fail('不可绑定当前店铺', 400);
        }
        $shopInfo = Shop::query()->where('shop_code', trim($data['invite_code']))->first();
        if (empty($shopInfo)) {
            return $this->fail('店铺码有误', 400);
        }
        $oShopId = $shopInfo['id'];
        $bindedfShopIds = ShopBind::getShopSceneBindShopIds($oShopId);
//        \Log::info('绑定店铺',[$oShopId,$bindedfShopIds]);
//        if(!empty($bindedfShopIds)){
//            $shopNames=implode(array_column(Shop::shopsByIds($bindedfShopIds)->toArray(),'shop_name'),",");
//            return $this->fail('店铺【'.$shopInfo->shop_name.'】已被【'.$shopNames."】绑定，请联系商家解绑", 400);
//        }
        // 查找当前店铺是否有授权表，当前店铺绑定他
        Company::query()->where('shop_id', $shop->id)->where('source_shopid', $oShopId)
            ->update(['source_status' => Company::SOURCE_STATUS_STOP]);
        Company::query()->where('shop_id', $oShopId)->where('source_shopid', $shop->id)
            ->update(['source_status' => Company::SOURCE_STATUS_STOP]);
        $res = ShopBind::bindShop($request->auth->shop_id, $oShopId, $data['type'],$remark);

        if (!$res) {
            return $this->fail('绑定失败', 400);
        }

        return $this->success();
    }


    /**
     * 接触店铺绑定关系
     * @param Request $request
     * @param $shopId
     * @return JsonResponse
     */

    public function unbindShop(Request $request, $shopId)
    {
        if ($request->auth->shop_id == $shopId) {
            return $this->fail('不能删除当前店铺', 400);
        }

        ShopBind::unbindShop($request->auth->shop_id, $shopId);

        return $this->success();
    }

    /**
     * 批量解除店铺绑定关系
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */

    public function batchUnbindShop(Request $request)
    {
        $data = $this->validate($request, [
            'shopIds' => 'required|array',
        ]);
        $shopIds = $data['shopIds'];
        if (in_array($request->auth->shop_id, $shopIds)) {
            return $this->fail('不能删除当前店铺', 400);
        }
        foreach ($shopIds as $shopId) {
            ShopBind::unbindShop($request->auth->shop_id, $shopId);
        }
        return $this->success();
    }


    /**
     * 获取全部的店铺绑定关系
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllBindShops(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'isShowUnbind' => 'int|in:0,1',  //是否显示解绑
            'showUnbindDay' => 'int|between:1,30', // 显示解绑天数
        ]);
        $isShowUnbind = array_get($data, 'isShowUnbind', 0);
        $showUnbindDay = array_get($data, 'showUnbindDay', 7);
        $currentShopId = intval($request->input('shopId', $request->auth->shop_id));
        //我绑定的店铺
        $query = ShopBind::query();
        if ($isShowUnbind) {
            $query->withTrashed();
            if ($showUnbindDay > 0) {
                $time = date('Y-m-d', strtotime(sprintf("-%s day", $showUnbindDay)));
                $query->where(function($query) use ($time){
                    $query->where('deleted_at',  '>', $time);
                    $query->orWhereNull('deleted_at');
                });
            }
        }

        $bindShops = $query
            ->where(function($query) use($currentShopId){
                $query->where(['f_shop_id' => $currentShopId])
                ->orWhere(['o_shop_id' => $currentShopId]);
            })
            ->whereIn('type',ShopBind::SCENE_SHOP)
            ->get()
            ->toArray();

        $meBindIds = [];
        $bindMeIds = [];
        foreach ($bindShops as $value) {
            //我绑定的店铺
            if ($value['f_shop_id'] == $currentShopId) {
                $meBindIds[] = $value['o_shop_id'];
            }
            //绑定我的店铺
            if ($value['o_shop_id'] == $currentShopId) {
                $bindMeIds[] = $value['f_shop_id'];
            }
        }

        $fTypeArr = array_column($bindShops, 'type', 'f_shop_id');
        $oTypeArr = array_column($bindShops, 'type', 'o_shop_id');
        $bindShopsKeyByF = array_column($bindShops, null, 'f_shop_id');
        $bindShopsKeyByO = array_column($bindShops, null, 'o_shop_id');
        $identifierShort = ShopBindSort::getShopBindSort($currentShopId);
        $meBindShops = Shop::query()->with(['shopGroup'])->whereIn('id', $meBindIds)->get()->toArray();
        if (!empty($meBindShops)) {
            foreach ($meBindShops as &$value) {
                $type = $oTypeArr[$value['id']];
                if ($type == ShopBind::BIND_TYPE_BROTHER) {
                    $value['bind_type'] = ShopBind::TYPE_ME_BIND;
                } else {
                    $value['bind_type'] = ShopBind::TYPE_BIND_ME;
                    $value['bind_level']= ShopBind::BIND_ME_MASTER;
                }
                $value['bind_created_at'] = $bindShopsKeyByO[$value['id']]['created_at'];
                $shopBindSort = $identifierShort->get($value['identifier']);
                $value['sort'] = isset($shopBindSort)?$shopBindSort->sort: 0;
            }
        }
        $bindMeShops = Shop::query()->with(['shopGroup'])->whereIn('id', $bindMeIds)->get()->toArray();
        if (!empty($bindMeShops)) {
            foreach ($bindMeShops as &$value) {
                $type = $fTypeArr[$value['id']];
                if ($type == ShopBind::BIND_TYPE_BROTHER) {
                    $value['bind_type'] = ShopBind::TYPE_ME_BIND;
                } else {
                    $value['bind_type'] = ShopBind::TYPE_BIND_ME;
                    $value['bind_level']= ShopBind::BIND_ME_SLAVE;
                }
                $shopBindSort = $identifierShort->get($value['identifier']);
                $value['bind_created_at'] = $bindShopsKeyByF[$value['id']]['created_at'];
                $value['sort'] = isset($shopBindSort)?$shopBindSort->sort: 0;
            }
        }

        // 合并去重
        $shopList = array_merge($meBindShops, $bindMeShops);
        \Log::info(["meBindShops"=>$meBindShops,"meBindIds"=>$meBindIds,"bindMeIds"=>$bindMeIds,"bindMeShops"=>$bindMeShops]);
        if (!empty($shopList)) {
            $shopList = collect($shopList)->sortBy('sort')->unique('id')->values()->all();
        }
        //当前店铺
        $myShop = Shop::query()->with(['shopGroup'])->where('id', $currentShopId)->get()->toArray();
        $myShop[0]['bind_type'] = ShopBind::TYPE_MY_SHOP;
        $myShop[0]['sort'] = 0;

        return $this->success(array_merge($myShop, $shopList));
    }

    /**
     * 厂家我的商家列表
     * <AUTHOR>
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getMerchantList(Request $request)
    {
        $this->validate($request, [
        ]);
        $type = ShopBind::TYPE_AGENT_PRINT_SHOP_2_FACTORY;
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $oShopIds = ShopBind::getOShopIdsByType($shopIds, $type);
        $list = ShopBind::query()->with(['oshop:id,shop_name,identifier','fshop:id,shop_name,identifier'])
            ->where('type', $type)
            ->whereIn('o_shop_id', $oShopIds)->get();
        return $this->success($list);
    }
    /**
     * 我绑定的厂家列表
     * <AUTHOR>
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getFactoryList(Request $request)
    {
        $this->validate($request, [
        ]);
        $type = ShopBind::TYPE_AGENT_PRINT_SHOP_2_FACTORY;
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $list = ShopBind::query()->with(['oshop:id,shop_name,identifier','fshop:id,shop_name,identifier'])
            ->where('type', $type)
            ->whereIn('o_shop_id', $shopIds)->select(['id','f_shop_id','o_shop_id','type','created_at'])->get();
        return $this->success($list);
    }

    /**
     * 解除店铺绑定关系
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */

    public function unbindFactoryShop(Request $request)
    {
        $this->validate($request, [
            'shop_id' => 'required|int',
        ]);
        $shopId = $request->input('shop_id');
        if ($request->auth->shop_id == $shopId) {
            return $this->fail('不能删除当前店铺', 400);
        }

        ShopBind::unbindFactoryShop($request->auth->shop_id, $shopId);

        return $this->success();
    }

    /**
     * 修改厂家绑定信息
     * <AUTHOR>
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateFactoryBindInfo(Request $request)
    {
        $this->validate($request, [
            'id' => 'required|int',
            'remark' => 'string',
        ]);
        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $id = $request->input('id');
        $remark = $request->input('remark',null);
        $updateData = [];
        if (!is_null($remark)) {
            $updateData['remark'] = $remark;
        }
        ShopBind::query()->whereIn('f_shop_id', $shopIds)->where('id',$id)->update($updateData);
        return $this->success();
    }

    /**
     * 修改排序
     * @param Request $request
     * @return void
     */
    public function sort(Request  $request){
        $currentShopId = $request->auth->shop_id;
        $sorts = $request->json()->all();
        $data=[];
        foreach ($sorts as $sort){
            $data[$sort['identifier']]= $sort['sort'];
        }
        ShopBindSort::updateShopBindSort($currentShopId,$data);
        return $this->success();
    }


}
