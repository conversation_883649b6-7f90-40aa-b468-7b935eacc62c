<?php

namespace App\Events\Orders;

use App\Events\BaseRequestEvent;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderDecryptEvent extends BaseRequestEvent
{

    public $user;
    public $shop;
    public $time;

    /**
     * @var array
     */
    public $orderIds;

    /**
     * @var int
     */
    public $orderTotal;

    /**
     * @var array
     */
    public $orderArr;

    /**
     * @var string 解密的请求ID
     */
    public $decryptRequestId = '';
    /**
     * @var int 解密的时间
     */
    public $decryptTime = 0;

    /**
     *
     * @param $operatingUser
     * @param $shop
     * @param $time int 时间
     * @param array $orderIds [tid]
     */
    public function __construct($operatingUser, $shop, int $time, array $orderIds)
    {
        $this->user = $operatingUser;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderIds = $orderIds;
        $orderList = Order::query()->whereIn('tid', $orderIds)->select(['id', 'tid'])->get()->pluck(null, 'id');
        // [id,tid,waybill_code,wp_code]
        $this->orderArr = $orderList->toArray();
    }
}
