<?php

namespace App\Events\Orders;

use App\Events\BaseRequestEvent;
use App\Models\Order;
use Illuminate\Http\Request;

class OrderCreateEvent extends BaseRequestEvent
{

    public $user;
    public $shop;
    public $time;

    /**
     * @var array
     */
    public $orderIds;

//    /**
//     * @var int
//     */
//    public $orderTotal;

//    /**
//     * @var array
//     */
//    public $orderArr;

    /**
     *
     * @param $operatingUser
     * @param $shop
     * @param $time int 时间
     * @param array $orderTidArr [tid]
     */
    public function __construct($operatingUser, $shop, int $time, array $orderTidArr)
    {
        $this->user = $operatingUser;
        $this->shop = $shop;
        $this->time = $time;
        $this->orderIds = $orderTidArr;
//        $orderList = Order::query()->whereIn('tid', $orderIds)->select(['id', 'tid'])->get()->pluck(null, 'id');
        // [id,tid,waybill_code,wp_code]
//        $this->orderArr = $orderList->toArray();
    }
}
