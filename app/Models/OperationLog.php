<?php
namespace App\Models;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class OperationLog extends Model
{
    protected $table = "operation_logs";

    protected $guarded = [
        'id',
    ];

    public function shop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'shop_id', 'id');
    }
    public function targetShop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'target_shop_id', 'id');
    }

    public function printRecord()
    {
        return $this->hasOne(\App\Models\PrintRecord::class, 'id', 'print_record_id');
    }
}
