<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/11/22
 * Time: 17:05
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class FactoryOrder extends Model
{
    use SoftDeletes;

    protected $table = "factory_orders";

    protected $guarded = ['id'];


    const DIST_STATUS_ASSIGNED = 1; // 已分配
    const DIST_STATUS_RETURNED = 2; // 已回传
    const DIST_STATUS_CANCELLED = 3; // 已取消

    const PRINT_STATUS_YES          = 1;         //已打印
    const PRINT_STATUS_NO           = 0;         //未打印

    const PRINT_STATUS_ONE          = 4;         //打印一次
    const PRINT_STATUS_MORE         = 5;         //打印多次
    const PRINT_STATUS_SHIPPING     = 6;         //已打印发货单

    const REFUND_STATUS_NO          = 1;         //无售后
    const REFUND_STATUS_YES         = 2;         //有售后

    public function packages()
    {
        return $this->belongsToMany(Package::class, 'package_orders', 'order_id', 'package_id');
    }

    public static function batchSave(array $tradesOrders, int $factoryShopId)
    {
        foreach ($tradesOrders as $index => $tradesOrder) {
            $tradesOrder['shop_id'] = $factoryShopId;
            DB::transaction(function () use ($tradesOrder) {
                FactoryOrder::query()->updateOrCreate(['distr_oid' => $tradesOrder['distr_oid'] . ''], $tradesOrder);
            });

        }
    }

}
