<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserExtra extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'identifier',
        'version',
        'version_desc',
        'buy_at',
        'expire_at',
        'expire_days',
        'pay_amount',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    const VERSION_FREE = 'Free'; //免费试用版
    const VERSION_STANDARD = 'Standard'; //标准版
    const VERSION_PROFESSIONAL = 'Professional'; //专业版
    const VERSION_SENIOR = 'Senior'; //高级版

    const VERSION_FREE_NAME = '试用版';
    const VERSION_STANDARD_NAME = '标准版';
    const VERSION_PROFESSIONAL_NAME = '专业版';
    const VERSION_SENIOR_NAME = '高级版';

    const VERSION_MAP_ARR = [
        self::VERSION_FREE => '试用版',
        self::VERSION_STANDARD => '标准版',
        self::VERSION_PROFESSIONAL => '专业版',
        self::VERSION_SENIOR => '高级版',
    ];
    const VERSION_NAME_MAP = [
        '试用版' => self::VERSION_FREE,
        '标准版' => self::VERSION_STANDARD,
        '专业版' => self::VERSION_PROFESSIONAL,
        '高级版' => self::VERSION_SENIOR,
    ];
    const VERSION_LEVEL_ARR = [
        self::VERSION_FREE => 1,
        self::VERSION_STANDARD => 2,
        self::VERSION_PROFESSIONAL => 3,
        self::VERSION_SENIOR => 4,
    ];

    public static function getVersionByShopId(string $shopId)
    {
        $userExtra = self::where('shop_id', $shopId)->first();
        if (empty($userExtra)) {
            return self::VERSION_FREE;
        }
        return $userExtra->version;
    }

    /**
     * 获取版本名称
     * @param string $name
     * @return string
     */
    public static function getVersionValueByName(string $name): string
    {
        return self::VERSION_NAME_MAP[$name] ?? self::VERSION_FREE;
    }

    public static function getVersionLevel($sku_spec)
    {
        return self::VERSION_LEVEL_ARR[$sku_spec] ?? 1;
    }

    /**
     * 判断是否为高级版
     * @return bool
     */
    public function isSenior(): bool
    {
        return $this->version == self::VERSION_SENIOR;
    }

    /**
     * 判断是否为专业版
     * @return bool
     */
    public function isProfessional(): bool
    {
        return $this->version == self::VERSION_PROFESSIONAL;


    }

    /**
     * 判断是否未过期,比较过期时间和当前时间时间
     * @return bool
     */
    public function notExpired(): bool
    {

        return strtotime($this->expire_at) > time();
    }
}
