<?php

namespace App\Models;

use App\Utils\DateTimeUtil;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderTraceList extends Model
{
    protected $table = 'order_trace_list';

    protected $fillable = [
        'user_id',
        'shop_id',
        'type',
        'tid',
        'express_code',
        'express_no',
        'status',
        'action',
        'receiver_province',
        'receiver_name',
        'send_at',
        'latest_updated_at',
        'latest_trace',
        'trace_list',
        'auth_source'
    ];

    const STATUS_SHIPPED = 0; //待揽收
    const STATUS_GOT = 1; //揽件
    const STATUS_SEND = 2; //派件
    const STATUS_SIGN = 3; //签收
    const STATUS_ARRIVAL = 4; //到件,到末端配送点
    const STATUS_DEPARTURE = 5; //发件
    const STATUS_FAIL = 6; //问题件
    const STATUS_REJECTION = 7; //拒签
    const STATUS_STAY_IN_WAREHOUSE = 8; //留仓,因为某种原因，例如：违禁物品，破包物品，或者是由于运输以及货仓人员疏忽导致的滞仓物品，停留在仓库内，需要重新发送，返回，或者扣押等
    const STATUS_SIGN_ON_BEHALF = 9; //代收点代签
    const STATUS_OTHER = 10; //其他
    const STATUS_RETURN = 11; //退件
    const STATUS_IN_CABINET = 12; //入柜/入代收点
    const STATUS_OUT_CABINET = 13; //出柜/出代收点
    const STATUS_DISCARD = 100; //由于授权过期，更新不到数据，标识已作废

    /**
     * 物流状态不进行同步
     */
    const STATUS_STOP_SYNC = [
        self::STATUS_SIGN,
        self::STATUS_REJECTION,
        self::STATUS_DISCARD,

    ];

    // 运输中
    const STATUS_TRANSPORTING_ARRAY = [
        self::STATUS_SEND,
        self::STATUS_ARRIVAL,
        self::STATUS_DEPARTURE,
        self::STATUS_REJECTION,
        self::STATUS_STAY_IN_WAREHOUSE,
        self::STATUS_RETURN,
        self::STATUS_OTHER,
    ];

    //物流状态
    const STATUS_ARR = [
        self::STATUS_GOT,
        self::STATUS_SEND,
        self::STATUS_SIGN,
        self::STATUS_ARRIVAL,
        self::STATUS_DEPARTURE,
        self::STATUS_FAIL,
        self::STATUS_REJECTION,
        self::STATUS_STAY_IN_WAREHOUSE,
        self::STATUS_SIGN_ON_BEHALF,
        self::STATUS_OTHER,
        self::STATUS_RETURN,
        self::STATUS_IN_CABINET,
        self::STATUS_OUT_CABINET,
    ];

    const STATUS_NAME_MAPPING = [
        self::STATUS_SHIPPED => '已发货待揽收',
        self::STATUS_GOT => '揽件',
        self::STATUS_SEND => '派件',
        self::STATUS_SIGN => '签收',
        self::STATUS_ARRIVAL => '到件',
        self::STATUS_DEPARTURE => '发件',
        self::STATUS_FAIL => '问题件',
        self::STATUS_REJECTION => '拒签',
        self::STATUS_STAY_IN_WAREHOUSE => '留仓',
        self::STATUS_SIGN_ON_BEHALF => '代收点代签',
        self::STATUS_OTHER => '其他',
        self::STATUS_RETURN => '退件',
        self::STATUS_IN_CABINET => '入柜/入代收点',
        self::STATUS_OUT_CABINET => '出柜/出代收点',
    ];

    /**
     * 把一些物流轨迹标志成废弃
     * @param string $expressNo
     * @param string|null $expressCode
     * @return int
     */
    public static function discard(string $expressNo,?string $expressCode=null): int
    {
        $query = self::query()->where('express_no', $expressNo);
        if (!empty($expressCode)) {
            $query->where('express_code', $expressCode);

        }
        return $query->update(['status' => self::STATUS_DISCARD]);
    }

    /**
     * 更新物流轨迹
     * @param array $traceList
     * @param int $userId
     * @param int $shopId
     * @param bool $create
     */
    public static function batchSave(array $traceList, int $userId, int $shopId,bool $create=true)
    {
        foreach ($traceList as $index => $trace) {
            try {
                DB::transaction(function () use ($trace, $userId, $shopId,$create) {
                    $trace['user_id'] = $userId;
                    unset($trace['status_time']);
                    $traceDataExceptTraceList= array_diff_key($trace, array_flip(['trace_list']));
//                    Log::info('更新物流轨迹', ["expressNo" => $expressNo, "trace" => $trace]);
//                    $update = self::query()->where('express_code', array_pull($trace, 'express_code'))->
//                    where('express_no', $expressNo)
//                        ->update($trace);
                    $expressCode = array_pull($trace, 'express_code');
                    $expressNo = array_pull($trace, 'express_no');
                    $tid = array_pull($trace, 'tid');
                    if($create) {
                        self::query()->updateOrCreate([
                            'shop_id' => $shopId,
                            'tid' => $tid,
                            'express_code' => $expressCode,
                            'express_no' => $expressNo,
                        ], $trace);
                        Log::info('创建物流轨迹', ["expressNo" => $expressNo, "shopId" => $shopId,"trace"=>$trace]);
                    }else{
                        $update=[
                            "status"=>$trace['status'],
                            "latest_updated_at"=>$trace['latest_updated_at'],
                            "action"=>$trace['action'],
                            "latest_trace"=>$trace['latest_trace'],
                        ];
                        self::query()->where('shop_id', $shopId)
                            ->where('express_no', $expressNo)
                            ->where('express_code', $expressCode)->update($update);
                        Log::info('更新物流轨迹', ["expressNo" => $expressNo, "shopId" => $shopId,"trace"=>$trace]);
                    }

                });
            } catch (\Exception $e) {

                Log::error('更新物流轨迹异常', ["trace" => $trace, "message" => $e->getMessage(), "traceString" => $e->getTraceAsString()]);
            }
        }
    }
}
