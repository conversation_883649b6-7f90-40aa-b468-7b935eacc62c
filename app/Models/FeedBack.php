<?php
namespace App\Models;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class FeedBack extends Model
{
    protected $table = "feed_backs";
    use SoftDeletes;
    protected $fillable = [
        'user_id',
        'shop_id',
        'type',
        'content',
        'phone',
        'qq',
    ];

    public function shop()
    {
        return $this->belongsTo('App\Models\Shop', 'shop_id', 'id');
    }

    public static function search(array $condition, string $search, int $offset, int $limit, string $orderBy = '')
	{
		$query = self::query()->with('shop')->where($condition);
		if ($search) {
			$query = $query->where(function ($query) use ($search) {
                $query->where('phone', 'like', '%' . $search . '%');
			});
		}
		$sortArr = explode(' ', $orderBy);

		return $query->limit($limit)
			->offset($offset)
			->orderBy($sortArr[0], $sortArr[1])
			->get();
	}
}