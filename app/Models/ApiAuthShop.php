<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class ApiAuth
 * @package App\Models
 */
class ApiAuthShop extends Model
{

    use SoftDeletes;

    protected $connection = 'mysql';

    protected $table = 'api_auth_shop';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'shop_identifier',
        'shop_expires_at',
        'shop_token',
        'token_expires_at',
        'updated_at',
        'deleted_at'
    ];

    public static function search(array $condition, int $offset, int $limit, string $orderBy = 'id desc')
    {
        $sortArr = explode(' ', $orderBy);

        $query   = ApiAuth::where($condition);

        return $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();
    }
}
