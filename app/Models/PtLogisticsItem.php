<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PtLogisticsItem extends Model
{
    use SoftDeletes;

    protected $table = 'pt_logistics_items';

    protected $fillable = [
        'pt_logistics_id',
        'oid',
        'order_id',
        'order_item_id',
        'sku_id',
        'sku_uuid',
        'outer_sku_id',
        'num_iid',
        'num',
        'waybill_wp_index',
//        'delivery_at',
//        'delivery_id',
    ];


    public function order()
    {
        return $this->hasOne(\App\Models\Fix\Order::class, 'id', 'order_id');
    }

    public function orderItem()
    {
        return $this->hasOne(\App\Models\Fix\OrderItem::class, 'id', 'order_item_id');
    }

}
