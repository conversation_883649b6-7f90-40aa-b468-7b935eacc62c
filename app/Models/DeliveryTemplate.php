<?php

namespace App\Models;
use App\Services\BusinessException;
use Illuminate\Database\Eloquent\Model;
class DeliveryTemplate extends Model
{
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'delivery_contents',
        'store_contents',
        'beihuo_contents',
        'fahuo_contents',
    ];
    //发货单默认项
   const contents='{"shopInfo":[{"key":"mallName","name":"店铺名称","showName":"测试店铺","checked":true},{"key":"deliveryPhone","name":"卖家电话","showName":"***********","checked":true},{"key":"deliveryAddress","name":"发货地址","showName":"江苏省苏州市工业园区测试地址","checked":true},{"key":"remark","name":"卖家备注","showName":"这是一条测试备注","checked":true}],"goodsInfo":[{"key":"goodsName","name":"商品名称","showName":"运动衫","checked":true},{"key":"goodsSpec","name":"规格","showName":"XL码，灰色","checked":true},{"key":"goodsPrice","name":"单价(元)","showName":"9.9","checked":true},{"key":"goodsCount","name":"数量","showName":"1","checked":true},{"key":"payment","name":"金额","showName":"9.9","checked":true}],"receiverInfo":[{"key":"orderSn","name":"订单编号","showName":"200202-488026276692XXX","checked":true},{"key":"confirmTime","name":"下单时间","showName":"2020-02-24 17:11:22","checked":true},{"key":"receiverName","name":"收件人","showName":"张三","checked":true},{"key":"receiverPhone","name":"收件人电话","showName":"***********","checked":true},{"key":"address","name":"收件人地址","showName":"江苏省苏州市工业园区XXXXXXXXX","checked":true}],"customContent":""}';
   //拣货单默认设置
   const storeContents='{"goodsInfo":[{"key":"goodsName","name":"商品标题","showName":"运动衫","checked":true},{"key":"goodsSpec","name":"规格","showName":"XL码，灰色","checked":true},{"key":"goodsCount","name":"数量","showName":"1","checked":true},{"key":"goodsPrice","name":"单价(元)","showName":"100","checked":true}],"titleInfo":[{"key":"orderCount","name":"累计订单","showName":"1笔","checked":true},{"key":"productCount","name":"累计商品","showName":"1个","checked":true},{"key":"totalSku","name":"累计SKU","showName":"1个","checked":true},{"key":"totalPrice","name":"累计金额","showName":"100元","checked":true},{"key":"printTime","name":"打印时间","showName":"2020-02-25 12:22:22","checked":true}]}';
   //备货单默认设置
   const beihuo_contents='{"goodsInfo":[{"id":1,"title":"序号","key":"keyNum","slot":"keyNum","width":80,"colspan":true,"type":"text","checked":true},{"id":12,"title":"商品主图","key":"goodsPic","slot":"goodsPic","align": "center","width":100,"type":"image","colspan":true,"checked":true,"width":120,"height":120},{"id":2,"title":"商品信息","key":"goods","slot":"goods","width":300,"colspan":true,"type":"text","checked":true},{"id":3,"title":"商品ID","key":"goodsId","slot":"goodsId","colspan":true,"type":"text","checked":true},{"id":7,"title":"规格图片","key":"skuPic","slot":"skuPic","width":100,"align": "center","type":"image","checked":true},{"id":8,"title":"规格名称","key":"skuName","slot":"skuName","type":"text","checked":true},{"id":6,"title":"规格ID","key":"skuId","slot":"skuId","type":"text","checked":true},{"id":9,"title":"单价","key":"goodsPrice","slot":"goodsPrice","type":"text","checked":true},{"id":10,"title":"数量","key":"skuCount","slot":"skuCount","align":"center","type":"text","checked":true},{"id":11,"title":"总计","key":"total","width":100,"align":"center","colspan":true,"type":"text","checked":true}],"goodsPicSize":120,"skuPicSize":50}';

}
