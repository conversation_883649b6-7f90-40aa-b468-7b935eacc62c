<?php

namespace App\Models;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RedisKeyConst;
use App\Events\Orders\OrderCreateEvent;
use App\Events\Orders\OrderLockEvent;
use App\Events\Orders\OrderUnLockEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\Orders\QualityQueryOrderJob;
use App\Models\Address as AddressModel;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Client\DyClient;
use App\Services\DistrictAssignService;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\OrderServiceManager;
use App\Services\QueryAreaService;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\ArrayUtil;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


/**
 * @property  OrderCipherInfo orderCipherInfo
 * @property int $id ID * @property int$user_id 用户id
 * @property int $shop_id 店铺id * @property int$factory_id 厂家id
 * @property string $tid 主订单 * @property int$type 订单类型
 * @property string|null $express_no 快递单号 * @property int$template_id 打印模板id
 * @property string $buyer_id 买家id * @property string|null$buyer_nick 买家昵称
 * @property string|null $seller_nick 卖家昵称 * @property int$order_status 订单状态
 * @property int $refund_status 退款状态 * @property int$print_status 打印状态
 * @property int $print_num 打印次数 * @property \Illuminate\Support\Carbon|null$printed_at 打印时间
 * @property \Illuminate\Support\Carbon|null $print_shipping_at 发货单打印时间 * @property \Illuminate\Support\Carbon|null$print_tag_at 打印标签时间
 * @property string|null $shop_title 店铺名 * @property string|null$receiver_state 收货人省份
 * @property string|null $receiver_city 收货人城市 * @property string|null$receiver_district 收货人地区
 * @property string|null $receiver_town 收货人街道 * @property string|null$receiver_name 收货人名字
 * @property string|null $receiver_phone 收货人手机 * @property int|null$receiver_zip 收件人邮编
 * @property string|null $receiver_address 收货地址 * @property string|null$address_md5 收件人邮政编码
 * @property int $address_flag 地址标记 * @property float$payment 实付金额
 * @property float $total_fee 总金额 * @property float$discount_fee 优惠金额
 * @property float $post_fee 运费 * @property string$seller_flag 卖家旗帜
 * @property string|null $seller_memo 卖家备注 * @property string|null$platform_memo 平台备注
 * @property string|null $buyer_message 买家留言 * @property int$has_buyer_message 是否有买家留言
 * @property string|null $express_code 快递公司代码 * @property int$num 商品数量
 * @property int $sku_num SKU数量 * @property int$is_pre_sale 是否预售1是0否
 * @property int $village_flag 村子标记 * @property string|null$goods_title 商品标题
 * @property string|null $goods_title_last 商品标题 * @property string|null$sku_value SKU的值
 * @property string|null $sku_value_last SKU的值 * @property string$merge_flag 合单标记
 * @property \Illuminate\Support\Carbon|null $promise_ship_at 承诺发货时间 * @property \Illuminate\Support\Carbon|null$order_created_at 订单创建时间
 * @property \Illuminate\Support\Carbon|null $order_updated_at 订单修改时间 * @property \Illuminate\Support\Carbon|null$send_at 发货时间
 * @property \Illuminate\Support\Carbon|null $finished_at 订单完成时间 * @property \Illuminate\Support\Carbon|null$groupon_at 成团时间
 * @property \Illuminate\Support\Carbon|null $locked_at 锁定时间 * @property \Illuminate\Support\Carbon|null$recycled_at 回收时间
 * @property \Illuminate\Support\Carbon|null $pay_at 支付时间 * @property int$is_comment 是否评价 (1:已评价)
 * @property string $smart_logistics 智能物流 * @property int$district_code 区域编码
 * @property string $custom_print_content 自定义打印内容 * @property string$custom_group 自定义分组
 * @property int $store_type 仓库类型 * @property int$send_type 配送类型
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间 * @property \Illuminate\Support\Carbon|null$updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间 * @property string|null$store_id 京东仓库编码
 * @property string|null $order_code 订单代码，微信视频号用到 * @property int$is_split 是否拆单 0否 1是
 * @property int $is_home_delivery 是否送货上门 * @property int$is_live_order 是否直播订单
 * @property int $is_gift_order 是否赠品订单 * @property \Illuminate\Support\Carbon|null$urge_shipment_at 催发货时间
 * @property string|null $soft_remark 软件备注 * @property \Illuminate\Support\Carbon|null$take_waybill_at 取号时间
 * @property int $pre_shipment_status 预发货状态 * @property \Illuminate\Support\Carbon|null$pre_shipment_at 预发货时间
 * @property int $is_remote_transit 是否为偏远中转订单 0否 1是 * @property int$abnormal_type 异常类型
 * @property int $is_give_gift 是否赠送礼物
 */
class Order extends Model
{
    /**
     * 订单状态
     */
    const ORDER_STATUS_UNKNOWN = 0;  //未知状态
    const ORDER_STATUS_PADDING = 10; //待付款
    const ORDER_STATUS_PAYMENT = 30; //已付款待发货
    const ORDER_STATUS_PAYMENT_REVIEW = 31; //已付款 审核中
    const ORDER_STATUS_PART_DELIVERED = 35; //部分发货
    const ORDER_STATUS_DELIVERED = 40; //已发货待签收
    const ORDER_STATUS_RECEIVED = 50; //已签收
    const ORDER_STATUS_SUCCESS = 70; //订单成功
    const ORDER_STATUS_FAILED = 80; //订单失败（订单未付款取消）
    const ORDER_STATUS_CLOSE = 90; //交易关闭
    const ORDER_STATUS_ABNORMAL = 100; //交易异常


    /**
     * 订单状态组，已发货 正常
     */
    const ORDER_STATUS_DELIVERED_ARRAY_NORMAL = [
        self::ORDER_STATUS_PART_DELIVERED,
        self::ORDER_STATUS_DELIVERED,
        self::ORDER_STATUS_RECEIVED,
        self::ORDER_STATUS_SUCCESS,
    ];
    /**
     * 订单状态组，已发货
     */
    const ORDER_STATUS_DELIVERED_ARRAY = [
        self::ORDER_STATUS_PART_DELIVERED,
        self::ORDER_STATUS_DELIVERED,
        self::ORDER_STATUS_RECEIVED,
        self::ORDER_STATUS_SUCCESS,
        self::ORDER_STATUS_FAILED,
        self::ORDER_STATUS_CLOSE,
        self::ORDER_STATUS_ABNORMAL,
    ];
    /**
     * 订单状态组，已发货 不包含部分
     */
    const ORDER_STATUS_DELIVERED2_ARRAY = [
        self::ORDER_STATUS_DELIVERED,
        self::ORDER_STATUS_RECEIVED,
        self::ORDER_STATUS_SUCCESS,
        self::ORDER_STATUS_FAILED,
        self::ORDER_STATUS_CLOSE,
        self::ORDER_STATUS_ABNORMAL,
    ];
    /**
     * 订单状态组，全部
     */
    const ORDER_STATUS_ALL_ARRAY = [
        self::ORDER_STATUS_UNKNOWN,
        self::ORDER_STATUS_PADDING,
        self::ORDER_STATUS_PAYMENT,
        self::ORDER_STATUS_PART_DELIVERED,
        self::ORDER_STATUS_DELIVERED,
        self::ORDER_STATUS_RECEIVED,
        self::ORDER_STATUS_SUCCESS,
        self::ORDER_STATUS_FAILED,
        self::ORDER_STATUS_CLOSE,
        self::ORDER_STATUS_ABNORMAL,
    ];
    /**
     *  订单状态订单已经错误完结
     */
    const ORDER_STATUS_ERROR_ARRAY = [
        self::ORDER_STATUS_FAILED,
        self::ORDER_STATUS_CLOSE,
        self::ORDER_STATUS_ABNORMAL,
    ];

    /**
     * 退款状态
     */
    const REFUND_STATUS_NO = 0;
    const REFUND_STATUS_YES = 1;
    const REFUND_STATUS_PART = 2;

    const FLAG_NONE = null; //没有标的时候，用灰
    const FLAG_GRAY = 'GRAY'; // 灰
    const FLAG_RED = 'RED'; // 红
    const FLAG_YELLOW = 'YELLOW'; // 黄
    const FLAG_GREEN = 'GREEN'; // 绿
    const FLAG_BLUE = 'BLUE'; // 蓝
    const FLAG_PURPLE = 'PURPLE'; // 紫
    const FLAG_CYAN = 'CYAN'; // 青
    const FLAG_ORANGE = 'ORANGE'; // 橙
    const FLAG_PINK = 'PINK'; // 粉色
    const FLAG_DARK_GREEN = 'DARK_GREEN'; // 暗绿
    const FLAG_ROSE_RED = 'ROSE_RED'; // 玫红

    const IS_COMMENT_YES = 1; //已评价

    const PRINT_STATUS_YES = 1;         //已打印
    const PRINT_STATUS_NO = 0;         //未打印
    const PRINT_STATUS_PART = 2;         //部分打印
    const PRINT_STATUS_PRINTING = 3;         //打印中
    const PRINT_STATUS_ONE = 4;         //打印一次
    const PRINT_STATUS_MORE = 5;         //打印多次
    const PRINT_STATUS_SHIPPING = 6;         //已打印发货单

    const WAYBILL_RECOVERY_NO = 0;         //未回收
    const WAYBILL_RECOVERY_YES = 1;         //已回收
    const MERGE_STATUS_YES = 1;             //满足合单
    const MERGE_STATUS_NO = 0;             //不满足合单
    const PRINT_RESULT_SUCCESS = 'success'; //打印状态 成功

    // 偏远中转
    const REMOTE_TRANSIT_YES = 1;
    const REMOTE_TRANSIT_NO = 0;
    const SYNC_ORDER_LIMIT = 10000; //同步订单限制

    /**
     * 订单类型
     */
    const TYPE_TAOBAO = 1; // 淘宝
    const TYPE_PDD = 2; // 拼多多

    //历史残留订单是否提示订单显示
    const MAX_ORDER_NUM = 1000;

    //包裹数量按条件
    const PRINT_BY_GOODS_NUM = -1; //按规格数量
    const PRINT_BY_GOODS_KIND = -2; //按规格种类

    const PRINT_BY_MAX_SKU = -3; //按SKU的最大数量


    // 1(退货退款),2(已发货仅退款),3(未发货仅退款),4(换货),5(补寄)
    const AFTERSALE_TYPE_REFUND = 1; // 退货退款
    const AFTERSALE_TYPE_ONLY_REFUND = 2; // 已发货仅退款
    const AFTERSALE_TYPE_NOT_DELIVERED_ONLY_REFUND = 3; // 未发货仅退款
    const AFTERSALE_TYPE_EXCHANGE = 4; // 换货
    const AFTERSALE_TYPE_RESEND = 5; // 补寄
    const AFTERSALE_TYPE_OTHER = 99; // 其他


    const PRE_SHIPMENT_STATUS_NO = 0; //未设置成预发货
    const PRE_SHIPMENT_STATUS_YES = 1; //设置成预发货，等待发货
    const PRE_SHIPMENT_STATUS_PAUSE = 2; //预发货暂停
    const PRE_SHIPMENT_STATUS_FINISHED = 3; //预发货完成

    const ASSIGN_TYPE_SELF = 'self'; // 自己发货

    const ASSIGN_TYPE_ARRAY = [
        self::ASSIGN_TYPE_SELF,
    ];

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable
        = [
            "tid",
            "type",
            "user_id",
            "shop_id",
            "express_no",
            "template_id",
            "buyer_id",
            "buyer_nick",
            "seller_nick",
            "order_status",
            "refund_status",
            "print_status",
            "is_stockout_print",
            "printed_at",
            "shop_title",
            "receiver_state",
            "receiver_city",
            "receiver_district",
            "receiver_town",
            "receiver_name",
            "receiver_phone",
            "receiver_zip",
            "receiver_address",
            "address_md5",
            "address_flag",
            "payment",
            "total_fee",
            "discount_fee",
            "post_fee",
            "seller_flag",
            "seller_memo",
            "buyer_message",
            "has_buyer_message",
            "goods_pic",
            "goods_title",
            "goods_title_last",
            "sku_value",
            "sku_value_last",
            "goods_price",
            "goods_num",
            "express_code",
            "num",
            "sku_num",
            "is_pre_sale",
            "village_flag",
            "promise_ship_at",
            "order_created_at",
            "order_updated_at",
            "send_at",
            "finished_at",
            "groupon_at",
            "locked_at",
            "recycled_at",
            "pay_at",
            "is_comment",
            "district_code",
            "smart_logistics",
            "color_label",
            "refund_id",
            "send_type",
            "store_id",
            "created_at",
            "updated_at",
            "deleted_at",
            "order_code",
            "is_split",
            "is_home_delivery",
            "is_live_order",
            "is_gift_order",
            "is_give_gift",
            "urge_shipment_at",
            "soft_remark",
            "print_tag_at",
            "take_waybill_at",
            "pre_shipment_at",
            "pre_shipment_status",
            "is_remote_transit",
            "abnormal_type",
            "assign_type",
        ];

    protected $appends = [
        'item_refund_status', 'item_refund_created_at', 'identifier', 'latest_trace', 'trace_list',
        'request_id', 'is_child_parent_order', 'child_parent_packages_count'
    ];

    public $request_id = null;
    public $is_child_parent_order = null;
    public $child_parent_packages_count = null;


    public static function countByUnshipped($shopId)
    {
        return Order::query()->where('shop_id', $shopId)
            ->where('order_status', Order::ORDER_STATUS_PAYMENT)
            ->whereNull('printed_at')
            ->whereNull('locked_at')
            ->count();
    }

    public static function getSearchOrderStatus($order_status)
    {
        $order_status = intval($order_status);
        $order_status_arr = [];
        switch ($order_status) {
            case Order::ORDER_STATUS_PAYMENT:
            case Order::ORDER_STATUS_PART_DELIVERED:
                $order_status_arr = [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED];
                break;
            case Order::ORDER_STATUS_DELIVERED:
                $order_status_arr = [Order::ORDER_STATUS_DELIVERED];
                break;
            case Order::ORDER_STATUS_RECEIVED:
                $order_status_arr = [Order::ORDER_STATUS_RECEIVED];
                break;
            case Order::ORDER_STATUS_SUCCESS:
                $order_status_arr = [Order::ORDER_STATUS_SUCCESS];
                break;
        }
        return $order_status_arr;
    }

    public function getRequestIdAttribute()
    {
        return $this->request_id;
    }

    public function setRequestIdAttribute($value)
    {
        $this->request_id = $value;
    }

    public static function firstByTid(string $tid)
    {
        $tid = tidAddA($tid);
        return self::query()->where('tid', $tid)->first();
    }


    public function getItemRefundStatusAttribute()
    {
        return collect($this->orderItem)->pluck('refund_status')->toArray();
    }

    public function getItemRefundCreatedAtAttribute()
    {
        $refundCreatedAt = collect($this->orderItem)->max('refund_created_at');
        return is_null($refundCreatedAt) ? null : $refundCreatedAt;
    }

    public function getIdentifierAttribute()
    {
        return $this->shop->identifier ?? '';
    }

    public function getLatestTraceAttribute()
    {
        return $this->trace->latest_trace ?? '';
    }

    public function getTraceListAttribute()
    {
        return $this->trace->trace_list ?? '';
    }

    /**
     * 订单子项
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function orderItem()
    {
        return $this->hasMany('App\Models\OrderItem', 'order_id', 'id');
    }

    public function packages(): \Illuminate\Database\Eloquent\Relations\HasManyThrough
    {
        return $this->hasManyThrough(Package::class, 'App\Models\PackageOrder',
            'order_id', 'id', 'id', 'package_id')
            ->where('waybill_status', Package::WAYBILL_STATUS_SUCCESS)
            ->distinct();
    }

    /**
     * 关联模板
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function template()
    {
        return $this->belongsTo('App\Models\Template', 'template_id', 'id');
    }

    /**
     * 关联模板
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo('App\Models\Fix\Shop', 'shop_id', 'id');
    }

    public function factoryShop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'factory_id', 'id');
    }

    public function trace()
    {
        return $this->hasOne('App\Models\OrderTraceList', 'tid', 'tid');
    }

    public function orderCipherInfo()
    {
        return $this->hasOne(OrderCipherInfo::class, 'order_id', 'id');
    }

    public function operationLogs()
    {
        return $this->hasMany(OperationLog::class, 'order_id', 'id');
    }

    public function orderExtra()
    {
        return $this->hasOne(OrderExtra::class, 'order_id', 'id');
    }

    public function ptLogistics()
    {
        return $this->hasMany(PtLogistics::class, 'order_id', 'id');
    }

    public function mergeFlagOrders()
    {
        return $this->hasMany(\App\Models\Fix\Order::class, 'merge_flag', 'merge_flag')
            ->from(DB::raw('orders'.' force index(orders_merge_flag_index)'))
            ->where('merge_flag', '!=', '')
            ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED]);
    }

    public function blacklist()
    {
        return $this->hasOne('App\Models\Blacklist', 'identifier', 'buyer_id')
            ->where('type', Blacklist::TYPE_BUYER_ID);
    }

    /**
     * 条件处理
     * @param       $query
     * @param  array  $fixedCondition
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function handleFixCondition($query, array $fixedCondition = [])
    {
        if ($fixedCondition) {
            if (in_array('HAVA_SELLER_MEMO', $fixedCondition)) {
                $query->where('seller_memo', '<>', '[]');
            }
            if (in_array('HAVA_BUYER_MESSAGE', $fixedCondition)) {
                $query->where('buyer_message', '<>', '');
            }
            if (in_array('BUYER_REQ_REFUND', $fixedCondition)) {
                $query->where('refund_status', '<>', self::REFUND_STATUS_NO);
            }
            if (in_array('LOCKED', $fixedCondition)) {
                $query->whereNotNull('locked_at');
            }
            if (in_array('MORE_GOOD_COUNT', $fixedCondition)) {
                $query->where('num', '>', 1);
            }
        }

        return $query;
    }

    /**
     * 排序处理
     * @param        $query
     * @param  string  $sort
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function handleOrderBy($query, string &$sort)
    {
        if (empty($sort)) {
            return $query;
        }
        $sort = explode(',', $sort);

        $orderBy = '';
        switch ($sort[0]) {
            case 'last_ship_time':
            case 'confirm_time':
            case 'pay_time':
                $orderBy = 'pay_at';
                break;
            case 'shipping_time':
                $orderBy = 'send_at';
                break;
            case 'printed_time':
                $orderBy = 'printed_at';
                break;
            case 'goods_count':
                $orderBy = 'num';
                break;
            case 'goods_price':
                $orderBy = 'payment';
                break;
            case 'order_created_time':
                $orderBy = 'order_created_at';
                break;
            case 'goods_title':
                if ($sort[1] == 'desc') {
                    $orderBy = 'goods_title';
                } else {
                    $orderBy = 'goods_title_last';
                }
                return $query->orderByRaw('CONVERT(orders.'.$orderBy.' USING gbk) COLLATE gbk_chinese_ci '.$sort[1]);
                break;
            case 'sku_value':
                if ($sort[1] == 'desc') {
                    $orderBy = 'sku_value_last';
                } else {
                    $orderBy = 'sku_value';
                }
                return $query->orderByRaw('CONVERT(orders.'.$orderBy.' USING gbk) COLLATE gbk_chinese_ci '.$sort[1]);
                break;
            case 'promise_ship_at':
                $orderBy = 'promise_ship_at';
                break;
            case 'outer_iid':
                $orderBy = 'outer_iid';
                break;
            case 'outer_sku_iid':
                $orderBy = 'outer_sku_iid';
                break;
            default:
                $orderBy = 'id';
                break;
        }

        $sort = [$orderBy, $sort[1]];
        return $query;
    }

    //    /**
//     * 批量发货
//     * @param array $delivers
//     * @return array
//     * @throws ApiException
//     */
//    public static function delivery(array $delivers)
//    {
//
//        $failOrders = [];
//        if (!is_array($delivers)) {
//            throw new ApiException(ErrorConst::ORDER_DELIVERY_FAIL);
//        }
//        $orderTotal = count($delivers);
//        $orderSuccess = 0;
//        //重新组装数据
//        $newDelivers = [];
//        foreach ($delivers as $deliver) {
//            if (strstr($deliver['idStr'], '_')) {
//                $idArr = explode('_', $deliver['idStr']);
//                foreach ($idArr as $idStr) {
//                    $deliver['idStr'] = $idStr;
//                    $newDelivers[] = $deliver;
//                }
//            } else {
//                $newDelivers[] = $deliver;
//            }
//        }
//        foreach ($newDelivers as $deliver) {
//            $order = null;
//            try {
//                // 是否部分发货 取出部分发货
//                $idStr = $deliver['idStr'];
//                $orderItemOId = [];
//                if (strstr($idStr, ":")) {
//                    $temp = explode(":", $idStr);
//                    $orderId = $temp[0];
//                    // temp[1]没有数据就是没勾选子订单 前端做了校验不会出现直接跳过
//                    if (!empty($temp[1])) {
//                        //打印完立即发货 需要转换oid
//                        if (isset($deliver['orderOid'])) {
//                            $orderItemOIdArr = OrderItem::query()->whereIn('id', explode(',', $temp[1]))->get()->toArray();
//                            $orderItemOId = array_column($orderItemOIdArr, 'oid');
//                        } else {
//                            $orderItemOId = explode(',', $temp[1]);
//                        }
//                    } else {
//                        continue;
//                    }
//                } else {
//                    $orderId = $idStr;
//                }
//
//                $order = Order::find($orderId);
//                $shop = $order->shop;
//                $userId = $order->user_id;
//                $shopId = $order->shop_id;
//                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
//                $orderService->setUserId($userId);
//                $orderService->setShop($shop);
//
//                //判断整单发还是部分发
//                $orderItemArr = OrderItem::query()->where('order_id', $orderId)->get()->toArray();
//                $orderItemOidArr = array_column($orderItemArr, 'oid');
//                //没有差集就是全部发货 不传oid
//                if (empty(array_diff($orderItemOidArr, $orderItemOId))) {
//                    $orderItemOId = [];
//                }
//
//                //多包情况,取第一个快递号发货
//                $expressNo = explode(',', $deliver['waybillCodeStr'])[0];
//                //订单状态是大于等于发货状态不去再发货
//                if ($order->order_status >= Order::ORDER_STATUS_DELIVERED) {
//                    // 判断退款
//                    if ($order->refund_status != Order::REFUND_STATUS_NO) {
//                        $order->error_code = 0;
//                        $order->error_msg = '订单有退款，发货失败！';
//                        $order->waybillCodeStr = $deliver['waybillCodeStr'];
//                        $failOrders[] = $order;
//                    } else {
//                        //todo 子订单重新发货需要存pack_id
//                        if (count($orderItemOId) == 0) {
//                            $order->express_no = $expressNo;
//                            $order->express_code = $deliver['express_code'];
//                            $order->save();
//                            $orderService->deliveryOrdersAgain($order->tid, $deliver['express_code'], $expressNo, $orderItemOId);
//                        }
//                        $orderSuccess++;
//                    }
//                    continue;
//                }
//
//
//                $package = Package::query()->where('waybill_code', $expressNo)->first();
//                if (!empty($package) && !empty($package->recycled_at)) {
//                    throw new ApiException(ErrorConst::ORDER_WAYBILL_RECYCLED_DELIVERED_FAIL);
//                }
//                $res = $orderService->deliveryOrders($order->tid, $deliver['express_code'], $expressNo, $orderItemOId);
//                if (!$res) {
//                    throw new ApiException(ErrorConst::ORDER_DELIVERY_REQUEST_FAIL);
//                }
//
//                // todo 后面要改成异步
//                //订阅物流信息
//                if ($order->template &&
//                    in_array($order->template->auth_source, [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB]) &&
//                    $deliver['express_code'] != 'JTSD'
//                ) {
//                    $waybillService = WaybillServiceManager::init($order->template->auth_source);
//                    $waybillService->sendLogisticsTraceMsg($order);
//                }
//
//                $sendAt = date('Y-m-d H:i:s');
//                //修改orderItem发货状态
//                $orderItemQuery = OrderItem::query()->where('order_id', $orderId);
//                $orderItemUpdateData = ['status' => Order::ORDER_STATUS_DELIVERED, 'send_at' => $sendAt];
//                if ($orderItemOId) {
//                    $orderItemQuery->whereIn('oid', $orderItemOId);
//                    $orderItemUpdateData['waybill_code'] = $expressNo;
//                }
//                $orderItemQuery->update($orderItemUpdateData);
//                $order->order_status = Order::ORDER_STATUS_DELIVERED;
//                //修改orders订单状态 未传子订单修改为已发货，传了子订单判断是否已经全部发货
//                if (!empty($orderItemOId)) {
//                    //子订单中是否还有未发货的
//                    $orderItem = OrderItem::query()->where(['order_id' => $orderId])->get()->toArray();
//                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
//                        return $t['status'] == Order::ORDER_STATUS_PAYMENT;
//                    });
//                    if (!empty($orderItemHasNoDeliver)) {
//                        $order->order_status = Order::ORDER_STATUS_PART_DELIVERED;
//                    }
//                    //取过号的子订单是否还有未发货的
//                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
//                        return $t['waybill_code'] != null;
//                    });
//                    if ($order->order_status == Order::ORDER_STATUS_DELIVERED) {
//                        $order->print_status = Order::PRINT_STATUS_YES;
//                    } else {
//                        $order->print_status = Order::PRINT_STATUS_NO;
//                        foreach ($orderItemHasNoDeliver as $val) {
//                            if (in_array($val['oid'], $orderItemOId) && $val['status'] != Order::ORDER_STATUS_DELIVERED) {
//                                $order->print_status = Order::PRINT_STATUS_PART;
//                                break;
//                            }
//                        }
//                    }
//                } else {
//                    $order->order_status = Order::ORDER_STATUS_DELIVERED;
//                }
//                $order->send_at = $sendAt;
//                $order->express_no = $expressNo;
//                $order->express_code = $deliver['express_code'];
//                //if (empty($order->express_code)) {
//                //$order->express_code = $deliver['express_code'];
//                //}
//                //if (empty($order->express_no)) {
//                //$order->express_no = $expressNo;
//                //}
//
//                if (!$order->save()) {
//                    throw new ApiException(ErrorConst::ORDER_STATUS_UPDATE_FAIL);
////                    throw new \Exception('订单发货状态修改失败');
//                }
//
//                // todo 后面要改成异步
//                $history = WaybillHistory::where([
//                    'shop_id' => $shopId,
//                    'waybill_code' => $expressNo,
//                    'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
//                ])->first();
//                $ret = DeliveryRecord::create([
//                    'user_id' => $userId,
//                    'shop_id' => $shopId,
//                    'history_id' => $history ? $history->id : 0,
//                    'order_id' => $order->id,
//                    'order_no' => $order->tid,
//                    'waybill_code' => $expressNo,
//                    'wp_code' => $deliver['express_code'],
//                    'result' => $res,
//                ]);
//                if (!$ret) {
//                    throw new ApiException(ErrorConst::DELIVERY_RECORD_EXCEPTION);
////                    throw new \Exception('发货记录异常');
//                }
//                $orderSuccess++;
//            } catch (ApiException $e) {
//                $order->error_code = $e->getErrorCode();
//                $order->error_msg = $e->getMessage();
//                $order->waybillCodeStr = $deliver['waybillCodeStr'];
//                $failOrders[] = $order;
////                if (ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED == $e->getErrorCode()) {
////                    throw $e;
////                }
//            } catch (ConnectException $e) {
//                Log::error($e->getMessage(), ['trace' => $e->getTrace(), 'deliver' => $deliver, 'orders' => $order->toArray()]);
//                $order->error_code = $e->getCode();
//                $order->error_msg = '请求平台发货超时，请稍后重试！';
//                $order->waybillCodeStr = $deliver['waybillCodeStr'];
//                $failOrders[] = $order;
//            } catch (\Exception $e) {
//                Log::error($e->getMessage(), ['deliver' => $deliver, 'orders' => $order->toArray()]);
//                $error_code = $e->getCode();
//                if (method_exists($e, 'getErrorCode')) {
//                    $error_code = $e->getErrorCode();
//                }
//                $order->error_code = $error_code;
//                $order->error_msg = $e->getMessage();
//                $order->waybillCodeStr = $deliver['waybillCodeStr'];
//                $failOrders[] = $order;
////                throw $e;
////                continue;
//            }
//        }
//        $orderFail = count($failOrders);
//        return [
//            'orderTotal' => $orderTotal,
//            'orderSuccess' => $orderSuccess,
//            'orderFail' => $orderFail,
//            'failOrders' => $failOrders
//        ];
//    }


    private static function getBatchNoKey(int $shopId, string $batchNoKey)
    {
        return 'batch_no:'.$shopId.':'.$batchNoKey;
    }

    /**
     * 设置同一批打印索引
     * @param  int  $shopId
     * @param  string  $batchNoKey
     * @param  int  $index
     * @return bool
     */
    private static function setBatchNoIndex(int $shopId, string $batchNoKey, int $index = 0)
    {
        $redis = redis('cache');
        $redisKey = self::getBatchNoKey($shopId, $batchNoKey);
        $redis->setex($redisKey, 30 * 60, $index);

        return true;
    }

    /**
     * 获取同一批打印索引
     * @param  int  $shopId
     * @param  string  $batchNoKey
     * @return int
     */
    public static function getBatchNoIndex(int $shopId, string $batchNoKey)
    {
        $redis = redis('cache');
        $redisKey = self::getBatchNoKey($shopId, $batchNoKey);
        $index = $redis->incr($redisKey);
        $redis->expire($redisKey, 30 * 60);
//		$index = 1;
//		if ($bool) {
//			$index = $redis->get($redisKey);
//		}

        return $index;
    }

    /**
     * 订单锁定
     * @param  array  $data
     * @param $shop_id
     * @return bool
     * @throws ApiException
     */
    public static function lock(array $data, $shop_id)
    {
        $shop = Shop::query()->with(['user'])->where('id', $shop_id)->first();
        $locked = array_get($data, 'locked', true);
        $orderIds = array_get($data, 'ids', true);

        $successOrderIdArr = [];
        foreach ($orderIds as $id) {
            $order = Order::findOrFail($id);
            if ($locked) {
                $orderModel = self::query()->where([
//                    'user_id' => $order->user_id,
                    'shop_id' => $order->shop_id,
                    'locked_at' => null,
                    'id' => $id,
                ])->first();
                if ($orderModel->order_status >= Order::ORDER_STATUS_DELIVERED) {
                    throw new ApiException(ErrorConst::ORDER_LOCK_FAIL_BY_STATUS);
                }
                $orders = $orderModel->update(['locked_at' => Carbon::now()]);
                if (!$orders) {
                    return false;
                }
                $successOrderIdArr[] = $id;
            } else {
                $orders = self::query()->where([
//					'user_id' => $order->user_id,
                    'shop_id' => $order->shop_id,
                ])->where('id', $id)->update(['locked_at' => null]);
                if (!$orders) {
                    return false;
                }
                $successOrderIdArr[] = $id;
            }

        }
        $orderArr = Order::query()->whereIn('id', $successOrderIdArr)->get(['id', 'tid'])->toArray();
        if ($locked) {
            event((new OrderLockEvent($shop->user, $shop, time(), $orderArr))->setClientInfoByRequest(\request()));
        } else {
            event((new OrderUnLockEvent($shop->user, $shop, time(), $orderArr))->setClientInfoByRequest(\request()));
        }
        return true;
    }

    /**
     * 批量保存订单
     * @param  array  $orders
     * @param  int  $userId
     * @param  int  $shopId
     * @param  string  $platform
     * @throws \Throwable
     * <AUTHOR>
     */
    public static function batchSave(array $orders, int $userId, int $shopId, string $platform = '')
    {
        $uniqid = uniqid();
        $runTime = microtime(true);
        if (empty($platform)) {
            $platform = Shop::firstById($shopId)->getPlatform();
        }
        Log::debug("batchSave:begin", [$uniqid, microtime(true) - $runTime]);

        $statusArr = [
            Order::ORDER_STATUS_PAYMENT,
            Order::ORDER_STATUS_DELIVERED,
            Order::ORDER_STATUS_PART_DELIVERED,
            Order::ORDER_STATUS_CLOSE,
            Order::ORDER_STATUS_SUCCESS,
            Order::ORDER_STATUS_RECEIVED,
            Order::ORDER_STATUS_FAILED,
            Order::ORDER_STATUS_ABNORMAL,
        ];

//        if ($needStatusReceived) {
//            array_push($statusArr, Order::ORDER_STATUS_RECEIVED);
//        }

        //加密字段处理
        $cipherInfoArr = [];
        $shop = Shop::query()->where('id', $shopId)->first();
        $shopExtra = ShopExtra::query()
            ->where('shop_id', $shopId)
            ->first();
        $allTids = array_column($orders, 'tid');
        $oldOrderMap = Order::query()->whereIn('tid', $allTids)->get()->keyBy("tid");

        // 地址有变化的订单
        $addressChangeOrders = [];
        foreach ($orders as $order) {
            if (empty($order)) {
                continue;
            }
            // 没有入库 或者 未发货的订单
            if (empty($oldOrderMap[$order['tid']]) || in_array($order['order_status'],
                    [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
                $addressChangeOrders[$order['tid']] = $order['tid'];
            }
        }

        if (!$shop->isJd()) {
            $addressChangeOrders = array_column($orders, 'tid', 'tid');
        }
        $orderService = OrderServiceManager::create($platform);
        $orderService->setShop($shop);
        //in_array(config('app.platform'), [PlatformConst::JD])
        if ($shop->isJd()) {
            self::handleSaveCipher($orderService, $orders, $cipherInfoArr, $addressChangeOrders);
            Log::info("handleSaveCipher", $cipherInfoArr);
        } elseif ($shop->isTaobao()) {
            $cipherInfoArr = collect($orders)->pluck('cipher_info', 'tid')->toArray();
        } elseif ($shop->isKs()) {
            self::handleSaveCipher($orderService, $orders, $cipherInfoArr, $addressChangeOrders);
        }
        $districtAssignService = new DistrictAssignService();
        $queryAreaService = new QueryAreaService();

        // 需要质检查询的订单
        $qualityQueryOrders = [];
        // 记录订单变化日志
        $logOrderInfoArr = array_map(function ($item) use ($oldOrderMap, &$qualityQueryOrders) {
            $orderItems = [];
//            foreach ($item['items'] as $it) {
//                $orderItems[] = array_only($it, ['oid', 'status', 'refund_status', 'order_updated_at']);
//            }
            if (!empty($item['order_extra']['order_biz_type'])
                && $item['order_extra']['order_biz_type'] == OrderExtra::BIZ_TYPE_QUALITY_INSPECTION
                && !$oldOrderMap->has($item['tid'])) {
                $qualityQueryOrders[] = $item['tid'];
            }
            return array_only($item,
                    ['tid', 'order_status', 'refund_status', 'order_updated_at']) + ['order_items' => $orderItems];
        }, $orders);
        $user = User::query()->where('id', $userId)->first();
        // 计算出首次保存的订单
        $oldOrderIidArr = $oldOrderMap->pluck('tid')->toArray();
        $firstCreateOrderTidArr = array_diff($allTids, $oldOrderIidArr);
//        \Log::info("firstCreateOrderTidArr:", [$firstCreateOrderTidArr,$allTids,$oldOrderIidArr]);
        $shippedOrderIdArr = []; // 已发货的订单ID

        Log::info("batchSave:orders_begin", [$uniqid, microtime(true) - $runTime]);
        foreach ($orders as $index => $order) {
            Log::info("batchSave:orders:$index", [$uniqid, microtime(true) - $runTime]);

            $tid = $order['tid'] ?? '';
            $tid = (string) $tid;
            if (empty($order) || !in_array($order['order_status'], $statusArr) || empty($tid)) {
                // 不符合状态跳出
                Log::info("不符合状态跳出",[ $order]);
                continue;
            }
            // 发货取消锁定
            if ($order['order_status'] >= Order::ORDER_STATUS_DELIVERED) {
                $order['locked_at'] = null;
            }
            // 部分发货不更新发货时间，因为有可能接口返回的发货时间是空的
            if (Order::ORDER_STATUS_PART_DELIVERED == $order['order_status'] && empty($order['send_at'])) {
                unset($order['send_at']);
            }
            $oldOrder = $oldOrderMap->get($tid);      //Order::query()->where('tid', (string)$order['tid'])->first();
            // 新的 order_status 比老的 order_status 小就跳过
            // 取消订单可以回退 (90取消订单=>30待发货)
//            \Log::info("oldOrder=",[$oldOrder]);
            if (!empty($oldOrder)
                && $oldOrder->order_status != Order::ORDER_STATUS_CLOSE
//                && $oldOrder->order_status != Order::ORDER_STATUS_DELIVERED // 有的时候平台状态更新慢，已经改成已发货，但是平台更新为待发货
                && $oldOrder->order_status > $order['order_status']
                && $oldOrder->refund_status == Order::REFUND_STATUS_NO) {
                Log::info("老订单状态大于新订单状态，跳过", [$oldOrder->order_status, $order['order_status']]);
                continue;
            }
            //比较新订单和老订单的更新时间错，如果一样的话就跳过
            if ($shop->isKs() && !empty($oldOrder) && $oldOrder->order_updated_at == ($order['order_updated_at'] ?? DateTimeUtil::strNow())) {
                Log::info("老订单更新时间等于新订单，跳过",
                    [$tid, $oldOrder->order_updated_at, $order['order_updated_at']]);
                continue;
            } else {
//                Log::info("老订单更新时间不等于新订单，继续", [$tid,$oldOrder->order_updated_at, $order['order_updated_at']]);
            }

//            \Log::debug("batchSave order:",[ $order]);
            // 如果是部分打印且订单部分退款，改成已打印
//            if (!empty($oldOrder) && $oldOrder['print_status'] == Order::PRINT_STATUS_PART && $oldOrder['refund_status'] == Order::REFUND_STATUS_PART) {
//                $printedCount = 0;
//                $oldOrderItems = $oldOrder->orderItem();
//                foreach ($order['items'] as $item) {
//                    $oldOrderItem = $oldOrderItems->where('oid', $item['oid'])->first();
//                    if ($item['refund_status'] == Order::REFUND_STATUS_YES || $oldOrderItem['print_status'] == Order::PRINT_STATUS_YES){
//                        $printedCount++;
//                    }
//                }
//                if ($printedCount == $oldOrderItems->count()) {
//                    $order['print_status'] = Order::PRINT_STATUS_YES;
//                }
//            }

            //关闭订单并且是退款订单，不修改订单状态
//            if ($order['order_status'] == Order::ORDER_STATUS_CLOSE && $order['refund_status'] == Order::REFUND_STATUS_YES) {
//                unset($order['order_status']);
//            }
//            log::info("保存隐私数据",$cipherInfoArr);
            DB::transaction(function () use (
                $order,
                $userId,
                $shopId,
                $cipherInfoArr,
                $shop,
                $oldOrder,
                $runTime,
                $uniqid,
                $index,
                $districtAssignService,
                $queryAreaService,
                $shopExtra,
                $addressChangeOrders,
                $orderService,
                &$shippedOrderIdArr
            ) {
                $order['user_id'] = $userId;
                $order['shop_id'] = $shopId;
                $cipherInfo = $cipherInfoArr[$order['tid']] ?? ($order['cipher_info'] ?? []);
                $cipherInfo['tid'] = $order['tid'];
                unset($order['cipher_info']);

                $isCrossShopMergeOrder = $shopExtra->is_cross_shop_merge_order ?? ShopExtra::IS_CROSS_SHOP_MERGE_ORDER_DEFAULT;
                $order['address_md5'] = $orderService->buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder,$oldOrder);

                if (empty($order['items'])) {
                    Log::info('empty items', $order);
                }

                $orderItems = $order['items'];
                unset($order['items']);
                $orderExtra = $order['order_extra'] ?? [];
                unset($order['order_extra']);
                unset($order['logistics_data']);

                // 村庄标记
                $village_flag = 0;

                $arr = explode(',', ShopExtra::WARNING_ADDRESS_DEFAULT);
                if ($shopExtra && $shopExtra->warning_address_str) {
                    //	$arr = ['村', '庄', '旗', '屯', '邮电', '邮局', '监狱', '看守所'];
                    $arr = explode(',', $shopExtra->warning_address_str);
                }
                foreach ($arr as $item) {
                    if (strpos($order['receiver_address'], $item) !== false) {
                        $village_flag = 1;
                        break;
                    }
                }
                $order['village_flag'] = $village_flag;

                // 偷运费地址标记
                $isFakeAddress = static::isFakeAddress($order['receiver_state'], $order['receiver_city'],
                    $order['receiver_district'], $order['receiver_address']);
                if ($isFakeAddress == 0) {
                    // 有的地址
                    $isFakeAddress = static::isFakeAddress($order['receiver_state'], $order['receiver_city'],
                        $order['receiver_district'], $cipherInfo['receiver_address_mask'] ?? '');
                }
                $order['address_flag'] = $isFakeAddress ? 1 : 0;

                $goodsTitleArr = array_column($orderItems, 'goods_title');
                sort($goodsTitleArr);
                $order['goods_title'] = Arr::first($goodsTitleArr);
                $order['goods_title_last'] = Arr::last($goodsTitleArr);

                $skuValueArr = array_column($orderItems, 'sku_value');
                sort($skuValueArr);
                $order['sku_value'] = Arr::first($skuValueArr);
                $order['sku_value_last'] = Arr::last($skuValueArr);


                Log::debug("batchSave:orders:$index:是否有发货异常", [$uniqid, microtime(true) - $runTime]);
                // 已发货订单判断是否有发货异常(用于修复已发货订单，但是发货失败的 packages 状态修复)
                if ($order['order_status'] == Order::ORDER_STATUS_DELIVERED && !empty($orderExtra['logistics_data'])) {
                    $redisKey = sprintf(RedisKeyConst::ORDER_DELIVERY_FAIL, $order['tid']);
                    $orderDeliveryFailInfo = redis('cache')->get($redisKey);
//                    Log::debug('orderDeliveryFailInfo', [$redisKey,$orderDeliveryFailInfo,$orderExtra]);
                    if (!empty($orderDeliveryFailInfo)) {
                        $logisticsDataArr = json_decode($orderExtra['logistics_data'], true);
                        // 已发货的运单号数组
                        $shippedWaybillCodeArr = array_column($logisticsDataArr, 'waybill_code');
                        Package::query()
                            ->whereIn('waybill_code', $shippedWaybillCodeArr)
                            ->where('status', '!=', Package::ORDER_STATUS_DELIVERED)
                            ->update(['status' => Package::ORDER_STATUS_DELIVERED]);
                    }
                }


                //地址异常判断
                /*$addressInfo = Address::smart($order['receiver_address']);
                if (((!empty($addressInfo['province']) && $addressInfo['province'] != $order['receiver_state']) ||
                    (!empty($addressInfo['city']) && $addressInfo['city'] != $order['receiver_city']) ||
                    (!empty($addressInfo['region']) && $addressInfo['region'] != $order['receiver_district']))) {
                    $extra = ['address' => $order['receiver_state'].$order['receiver_city'].$order['receiver_district'].$order['receiver_address']];
                    AbnormalOrder::query()->updateOrCreate(['order_id' => $oldOrder->id, 'type' => AbnormalOrder::TYPE_OF_ADDRESS_ABNORMAL, 'status' => AbnormalOrder::STATUS_OF_UNREAD],[
                        'type' => AbnormalOrder::TYPE_OF_ADDRESS_ABNORMAL,
                        'status' => AbnormalOrder::STATUS_OF_UNREAD,
                        'shop_id' => $shopId,
                        'desc' => '买家收货地址异常',
                        'extra' => json_encode($extra),
                        'order_id' => $oldOrder->id
                    ]);
                }*/
                Log::debug("batchSave:orders:$index:districtCode", [$uniqid, microtime(true) - $runTime]);
                $districtCode = AddressModel::getDistrictCodeAndOther($order['receiver_state'], $order['receiver_city'],
                    $order['receiver_district']);

                //判断该订单是否是手动合并订单
                $isManualMerge = false;
//                if (!empty($oldOrder['merge_flag']) && strpos($oldOrder['merge_flag'], '_') && explode('_', $oldOrder['merge_flag'])[0] == "merge") {
//                    $isManualMerge = true;
//                    unset($order['receiver_state']);
//                    unset($order['receiver_city']);
//                    unset($order['receiver_district']);
//                    unset($order['receiver_town']);
//                    unset($order['receiver_address']);
//                    unset($order['receiver_name']);
//                    unset($order['receiver_phone']);
//                    unset($order['address_md5']);
//                }
                $order['district_code'] = $districtCode;
                Log::debug("batchSave:orders:$index:智能物流", [$uniqid, microtime(true) - $runTime]);
                // 智能物流
                if (!empty($order['district_code'])) {
//                    $smartLogistics = $districtAssignService->getDistrictAssignValue($shopId,
//                        DistrictAssign::MODE_LOGISTICS, $order['district_code']);
                    $order['smart_logistics'] = $queryAreaService->getUnionWpCodeByDistrictCode($shopId,
                        $order['district_code']);
//                    $smartFlag = $districtAssignService->getDistrictAssignValue($shopId,
//                        DistrictAssign::MODE_FLAG, $order['district_code']);
//                    !empty($smartFlag) && $order['flag'] = $smartFlag;
//                    $colorLabel = $districtAssignService->getDistrictAssignValue($shopId,
//                        DistrictAssign::MODE_COLOR_LABEL, $order['district_code']);
//                    $order['color_label'] = $colorLabel;
                }
                // 地址没变化就不需要改变了
                if (!isset($addressChangeOrders[$order['tid']])) {
                    unset($order['receiver_address'], $order['receiver_name'], $order['receiver_phone']);
                }
                // 某些平台非待发货没有地址信息,不修改,这些信息如果为空就保留原值

                $receiverFieldArr = [
                    'receiver_state', 'receiver_city', 'receiver_district', 'receiver_town', 'receiver_address',
                    'receiver_name', 'receiver_phone', 'districtCode'
                ];
                foreach ($receiverFieldArr as $index => $receiverField) {
                    if (empty($order[$receiverField])) {
                        unset($order[$receiverField]);
                    }
                }
                $cipherFieldArr = [
                    'receiver_phone_mask', 'receiver_name_mask', 'receiver_address_mask', 'receiver_telephone_mask','oaid',
                    'receiver_phone_ciphertext','receiver_name_ciphertext','receiver_address_ciphertext','receiver_telephone_ciphertext'
                ];
                foreach ($cipherFieldArr as $index => $cipherField) {
                    if (empty($cipherInfo[$cipherField])) {
                        unset($cipherInfo[$cipherField]);
                    }
                }
                if($shop->isPdd()){
                    Log::info("batchSave:orders:pdd订单", [$order,$cipherInfo]);
                }


                Log::debug("batchSave:orders:$index:保存订单", [$uniqid, microtime(true) - $runTime]);

                $orderModel = self::query()->updateOrCreate([
                    'tid' => (string) $order['tid'],
                    'type' => $order['type'],
                ], $order);
                OrderExtra::query()->updateOrCreate([
                    'order_id' => $orderModel->id,
                ], $orderExtra);

                Log::debug("batchSave:orders:$index:保存子订单", [$uniqid, microtime(true) - $runTime]);

                foreach ($orderItems as $datum) {
                    $attributes = [
                        'oid' => (string) $datum['oid'],
                    ];
                    if (PlatformConst::WX == config('app.platform')) {
                        // 唯一索引被去掉了
                        $attributes = [
                            'oid' => (string) $datum['oid'],
                            'sku_id' => (string) $datum['sku_id'],
                        ];
                    }
                    $orderItemModel = OrderItem::query()->firstOrNew($attributes);
                    if (isset($datum['refund_status']) && Order::REFUND_STATUS_YES == $datum['refund_status']
                        && empty($orderItemModel['refund_created_at'])) {
                        $datum['refund_created_at'] = date('Y-m-d H:i:s');
                    }
                    $send_num = $datum['send_num'] ?? 0;
                    $send_remain_num = $datum['goods_num'] - $send_num;
                    if ($send_remain_num <= 0) {
                        $send_remain_num = 0;
                    }

                    $orderItemSave = [
                        'order_id' => $orderModel['id'],
                        'user_id' => $userId,
                        'shop_id' => $shopId,
                        'type' => $datum['type'],
                        'send_remain_num' => $send_remain_num,
                    ];
                    if ($send_remain_num == 0 && in_array($datum['status'],
                            [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
                        // 可发货数量为0，订单状态改为已发货
                        $datum['status'] = Order::ORDER_STATUS_DELIVERED;
                    }
                    $bool = $orderItemModel->fill(array_merge($datum, $orderItemSave))->save();
//                    $bool = OrderItem::query()->updateOrCreate($attributes, array_merge($datum, [
//						'order_id' => $orderModel['id'],
//						'user_id'  => $userId,
//						'shop_id'  => $shopId,
//						'type'     => $datum['type'],
//					]));
                    if (!$bool) {
                        Log::error('插入订单详情失败！', ['orderItem' => $datum]);
                    }
                }

                Log::debug("batchSave:orders:$index:加密数据", [$uniqid, microtime(true) - $runTime]);

                //手动合并订单不更改加密数据
                if (!$isManualMerge && !empty($cipherInfo)) {
                    OrderCipherInfo::query()->updateOrCreate([
                        'order_id' => $orderModel['id'],
                    ], $cipherInfo);
//                    Log::info('更新订单加密信息', ['orderId' => $orderModel['id'], 'cipherInfo' => $cipherInfo]);
                }
                // 判断已发货的订单
                if (in_array($order['order_status'], Order::ORDER_STATUS_DELIVERED_ARRAY)) {
                    $shippedOrderIdArr[] = $orderModel['id'];
                }
                Log::debug("batchSave:orders:$index:写入异常通知表", [$uniqid, microtime(true) - $runTime]);
                //判断收货地址&备注信息是否有修改 写入异常通知表
                static::handleAbnormalOrder($oldOrder, $orderModel, $cipherInfo, $shopId);
                Log::debug("batchSave:orders:$index:写入异常通知表结束", [$uniqid, microtime(true) - $runTime]);
            });
        }
        Log::debug("batchSave:重新发货", [$uniqid, microtime(true) - $runTime]);
        if (!empty($shippedOrderIdArr)) {
            // 处理平台返回的已发货包裹数据
            static::handleLogisticsData($shippedOrderIdArr);
        }
        Log::debug("batchSave:质检", [$uniqid, microtime(true) - $runTime]);
        if (!empty($qualityQueryOrders)) {
            dispatch(new QualityQueryOrderJob($shopId, $qualityQueryOrders));
        }
        if (!empty($firstCreateOrderTidArr)) {
            event((new OrderCreateEvent($user, $shop, time(),
                $firstCreateOrderTidArr))->setClientInfoByRequest(request()));
        }
    }


    public static function deliveryForOpenApi($appId, $deliver)
    {
        if (!is_array($deliver)) {
            return false;
        }
        try {
            $order = Order::find($deliver['id']);
            $shop = $order->shop;
            $userId = $order->user_id;
            $shopId = $order->shop_id;
            $orderService = OrderServiceManager::create(PlatformConst::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
            $orderService->setUserId($userId);
            $orderService->setShop($shop);
            //多包情况,取第一个快递号发货
            $expressNo = explode(',', $deliver['express_no'])[0];
            $res = $orderService->deliveryOrdersForOpenApi($order->tid, $deliver['express_code'], $expressNo);
            if ($res !== true) {
                return $res;
            }

            //订阅物流信息
            if ($order->template &&
                in_array($order->template->auth_source, [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB])
            ) {
                $waybillService = WaybillServiceManager::init($order->template->auth_source);
                $waybillService->sendLogisticsTraceMsg($order);
            }

            $order->order_status = Order::ORDER_STATUS_DELIVERED;
            $order->send_at = date('Y-m-d H:i:s');
            if (is_null($order->express_code)) {
                $order->express_code = $deliver['express_code'];
            }
            if (is_null($order->express_no)) {
                $order->express_no = $deliver['express_no'];
            }
            if (!$order->save()) {
                return false;
            }

            $history = WaybillHistory::where([
                'shop_id' => $shopId,
                'waybill_code' => $expressNo,
                'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
            ])->first();
            $ret = DeliveryRecord::create([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'history_id' => $history ? $history->id : 0,
                'order_id' => $order->id,
                'order_no' => $order->tid,
                'waybill_code' => $expressNo,
                'wp_code' => $deliver['express_code'],
                'result' => $res,
                'app_Id' => $appId,
            ]);
            if (!$ret) {
                return false;
            }
        } catch (\Exception $e) {
            Log::error('deliveryForOpenApi:'.$e->getMessage(), ['deliver' => $deliver, 'orders' => $order->toArray()]);
            return $e->getMessage();
        }

        return true;
    }

    public static function getOpenApiAddressInfo($platform, $waybillData, $waybill, $branchCode, $wpCode)
    {
        $addressInfo = [];
        // 判断传的是branchCode还是地址拼接
        $tempBranch = explode(',', $branchCode);
        foreach ($waybillData as $key => $val) {
            if ($val['wp_code'] == $wpCode) {
                foreach ($val['branch_account_cols'] as $v => $k) {
                    if (count($tempBranch) <= 1) {
                        if ($branchCode == $k['branch_code']) {
                            foreach ($k['shipp_addresses'] as $shipp) {
                                if ($waybill->auth_source == 1) {
                                    $addressInfo = [
                                        'province' => $shipp->province,
                                        'city' => $shipp->city,
                                        'district' => $shipp->district,
                                        'address' => $shipp->detail,
                                    ];
                                    break;
                                } else {
                                    $addressInfo = [
                                        'province' => $shipp['province'],
                                        'city' => $shipp['city'],
                                        'district' => $shipp['district'],
                                        'address' => $shipp['detail'],
                                    ];
                                    break;
                                }
                            }

                        }
                    } else {
                        foreach ($k['shipp_addresses'] as $shipp) {
                            if ($waybill->auth_source == 1 && $shipp->province == $tempBranch[0] && $shipp->city == $tempBranch[1] && $shipp->district == $tempBranch[2] && $shipp->detail == $tempBranch[3]) {
                                $addressInfo = [
                                    'province' => $shipp->province,
                                    'city' => $shipp->city,
                                    'district' => $shipp->district,
                                    'address' => $shipp->detail,
                                ];
                                break;
                            } elseif ($waybill->auth_source != 1 && $shipp['province'] == $tempBranch[0] && $shipp['city'] == $tempBranch[1] && $shipp['district'] == $tempBranch[2] && $shipp['detail'] == $tempBranch[3]) {
                                $addressInfo = [
                                    'province' => $shipp['province'],
                                    'city' => $shipp['city'],
                                    'district' => $shipp['district'],
                                    'address' => $shipp['detail'],
                                ];
                                break;
                            }
                        }

                    }
                }
            }
        }
        return $addressInfo;
    }

    public static function getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $wpCode)
    {
        $addressInfo = [];
        // 判断传的是branchCode还是地址拼接
        $tempBranch = explode(',', $branchCode);
        foreach ($waybillData as $key => $val) {
            if ($val['wp_code'] == $wpCode) {
                foreach ($val['branch_account_cols'] as $v => $k) {
                    if (count($tempBranch) <= 1) {
                        if ($branchCode == $k['branch_code']) {
                            foreach ($k['shipp_addresses'] as $shipp) {
                                $shipp = json_decode(json_encode($shipp), true);
                                $addressInfo = [
                                    'province' => $shipp['province'],
                                    'city' => $shipp['city'],
                                    'district' => $shipp['district'],
                                    'street' => $shipp['street'] ?? '',
                                    'address' => $shipp['detail'],
                                    'branch_code' => $k['branch_code'],
                                    'branch_name' => $k['branch_name'],
                                    'wp_code' => $val['wp_code'],
                                    'settle_account' => $k['settle_account'] ?? '',
                                ];
                            }

                        }
                    } else {
                        foreach ($k['shipp_addresses'] as $shipp) {
                            $shipp = json_decode(json_encode($shipp), true);
                            $tmpAddress = 'null';
                            if (count($tempBranch) == 4) {
                                $tmpAddress = $shipp['province'].','.$shipp['city'].','.$shipp['district'].','.$shipp['detail'];
                            } elseif (count($tempBranch) == 5) {
                                $tmpAddress = $shipp['province'].','.$shipp['city'].','.$shipp['district'].','.($shipp['street'] ?? '').','.$shipp['detail'];
                            }
                            if ($tmpAddress == $branchCode) {
                                $addressInfo = [
                                    'province' => $shipp['province'],
                                    'city' => $shipp['city'],
                                    'district' => $shipp['district'],
                                    'street' => $shipp['street'] ?? '',
                                    'address' => $shipp['detail'],
                                    'branch_code' => $k['branch_code'],
                                    'branch_name' => $k['branch_name'],
                                    'wp_code' => $val['wp_code'],
                                    'settle_account' => $k['settle_account'] ?? '',
                                ];
                                break;
                            }
                        }

                    }
                }
            }
        }
        return $addressInfo;
    }

    public static function getPrintDataAndWaybillForOpenApi(
        $userId,
        $shopId,
        $wpCode,
        $platform,
        $branchCode,
        $senderName,
        $sendMobile,
        $orderSnList,
        $waybillType,
        $batchNo,
        $packageNum,
        $waybillData,
        $waybill,
        $appId,
        $orderPrintParams = []
    ) {
        // 匹配发货地址
//        $addressInfo = self::getOpenApiAddressInfo($platform, $waybillData, $waybill, $branchCode, $wpCode);
        $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $wpCode);
        if (empty($addressInfo)) {
//            \Log::error('没有匹配到发货地址', [$waybillData, $branchCode]);
            throw_error_code_exception(StatusCode::COMPANY_ERROR);
        }

        $addressInfo = array_merge($addressInfo, ['sender_name' => $senderName, 'mobile' => $sendMobile]);

        // 查询是否取过面单号
        $hasWaybillHistories = self::hasWaybillHistoriesForOpenApi($userId, $shopId, $orderSnList, $wpCode, $waybill,
            $platform, $appId);
        $newOrders = $hasWaybillHistories['new_orders'];
        $failedData = $printData = [];

        if (!empty($newOrders)) {
            $packageIdArr = array_pluck($orderPrintParams, 'packageId', 'orderSn');
            foreach ($newOrders as $index => $newOrder) {
                if (isset($packageIdArr[$newOrder->tid])) {
                    $newOrder->package_id = $packageIdArr[$newOrder->tid];
                }
            }
            //未取号取号处理
            $newWaybillHistories = self::newWaybillHistoriesForOpenApi(
                $userId,
                $shopId,
                $platform,
                $newOrders,
                $addressInfo,
                $wpCode,
                $waybill,
                $batchNo,
                $waybillType,
                $packageNum,
                $appId
            );

            //取号失败信息
            if (count($newWaybillHistories['failed_data']) > 0) {
                $failedData = $newWaybillHistories['failed_data'];
            }
        }
        $printData = array_merge($hasWaybillHistories['print_data'] ?? [], $newWaybillHistories['print_data'] ?? []);
        if (!$printData && !$failedData) {
            //throw new \Exception('订单获取打印数据异常！');
            return throw_error_code_exception(StatusCode::PRINT_ERROR);
        }

        return array_merge($printData, $failedData);
    }

    public static function hasWaybillHistoriesForOpenApi(
        $userId,
        $shopId,
        $orderSnList,
        $wpCode,
        $waybill,
        $platform,
        $appId
    ) {
        $printRecords = [];
        $printData = [];
        $newOrders = [];

        $orders = [];
        foreach ($orderSnList as $orderSn) {
            $mainOrder = Order::query()->with('OrderCipherInfo')->where(['tid' => $orderSn])->first();
            if (empty($mainOrder)) {
                throw_error_code_exception(StatusCode::ORDER_NOT_FOUND);
            }
            //
            $mainOrder->mergeOrders = null;
            $orders[] = $mainOrder;
        }

        $allHistories = WaybillHistory::where([
            'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
        ])
            ->whereIn('order_no', $orderSnList)
            ->get();
        //如果无历史取号记录，直接去取号
        if ($allHistories->isEmpty()) {
            return ['print_data' => $printData, 'new_orders' => $orders];
        }
        $shop = Shop::query()->where('id', $shopId)->first();
        foreach ($orderSnList as $orderSnArr) {
            $order = collect($orders)->where('tid', $orderSnArr)->first();
            $waybillHistory = collect($allHistories)->whereIn('order_no', $orderSnArr)->all();
            if (count($waybillHistory) > 0) {
                Order::query()->where('tid', $orderSnArr)->update([
                    'express_no' => implode(',', collect($waybillHistory)->pluck('waybill_code')->toArray()),
                    'express_code' => $wpCode,
                ]);
                foreach ($waybillHistory as $history) {
                    if ($platform == Waybill::OPEN_API_DEFAULT && config('app.platform') == 'dy') {
                        //重新打印需要重新生成params参数
                        $appKey = config('socialite.dy.client_id');
                        $secretKey = config('socialite.dy.client_secret');
                        $client = (new DyClient($appKey, $secretKey))->setAccessToken($shop->access_token);
                        $requestData = $client->buildRequestData([], 'logistics/getShopKey');
                        $paramsStr = urldecode(http_build_query($requestData));
                        $print_data = json_decode($history['print_data'], true);
                        $print_data['params'] = $paramsStr;
                        $history['print_data'] = json_encode($print_data);
                    }
                    $printData[] = [
                        'orderSn' => $history['order_no'],
                        'waybillCode' => $history['waybill_code'],
                        'parentWaybillCode' => $history['parent_waybill_code'],
                        'printData' => $history['print_data'],
                        'code' => 1,
                        'errorMsg' => '',
                    ];
                    $printRecords[] = [
                        'wp_code' => $history['wp_code'],
                        'waybill_code' => $history['waybill_code'],
                        'order' => $order->toArray(),
                        'user_id' => $history['user_id'],
                        'package_id' => $history['package_id'],
                        'history_id' => $history['id'],
                        'app_id' => $appId,
                    ];
                }
            } else {
                $newOrders[] = $order;
            }
        }
        //添加打印记录
        /*$records = PrintRecord::generate($printRecords, $userId, $shopId);
        if (!$records) {
            Log::error('[对外接口-获取面单号]  打印记录添加失败!', ['data' => $printRecords,'user_id'=>$userId,'shop_id'=>$shopId]);
            //throw new \Exception('打印记录添加失败');
        }*/

        return ['print_data' => $printData, 'new_orders' => $newOrders];
    }

    public static function newWaybillHistoriesForOpenApi(
        $userId,
        $shopId,
        $platform,
        $orders,
        $sender,
        $wpCode,
        $waybill,
        $batchNoKey,
        $waybillType,
        $packageNum = 1,
        $appId = '',
        $productType = null
    ) {
        $printData = [];
        $failedOrders = [];

        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        StatisticsCost('获取 $authSource done');
        if (!empty($orders)) {
            // 查询标准模板 waybillType：0平台 1拼多多站外电子面单 2淘宝菜鸟电子面单 3菜鸟物流云电子面单
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
            StatisticsCost('获取 $waybillTemp done');

            // 获取运单号
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $packages = $waybillService->assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode,
                $waybillType, $waybillTemp, $packageNum, $productType);
            StatisticsCost('获取 $packages done');

            $index = Order::getBatchNoIndex($shopId, $batchNoKey);
            foreach ($packages as $idStr => $package) {
                $batchNo = $batchNoKey.'-'.$index;
                $oidArr = collect(explode('_', $idStr))->sort()->all();
                $order = collect($orders)->where('id', $oidArr[0])->first();

                //是否取号错误
                if (!is_string($package[0]) && is_array($package[0]) && (isset($package[0]['err_no']) ? $package[0]['err_no'] == 0 : true)
                    && (Waybill::OPEN_API_DEFAULT == $platform ? (!isset($package[0]['data']['ebill_infos'])) : true)) {
                    Order::where('id', $oidArr[0])->update([
                        //'express_no'   => implode(',', collect($package)->pluck('waybill_code')->toArray()),
                        'template_id' => '',
                        'express_code' => $wpCode,
//                        'print_status' => Order::PRINT_STATUS_YES,
                    ]);
                    foreach ($package as $key => $item) {
                        $p = Package::create([
                            'user_id' => $userId,
                            'shop_id' => $shopId,
                            'template_id' => '',
                            'waybill_code' => array_get($item, 'waybill_code'),
                            'tids' => $order['tid'] ?? null,
                            'wp_code' => $wpCode,
                            'auth_source' => $authSource,
                            'batch_no' => $batchNo,
                            'print_status' => Order::PRINT_STATUS_YES,
                            'tid_oids' => json_encode([['id' => (string) $oidArr[0], 'subIds' => []]])//默认主订单全勾
                        ]);
                        $po = PackageOrder::create([
                            'order_id' => $oidArr[0],
                            'package_id' => $p->id,
                        ]);
                        if (!$p || !$po) {
                            //throw new \Exception('包裹记录失败！');
                            \Log::error('机构获取面单号 包裹记录失败', [$p, $po]);
                        }
                        $history = WaybillHistory::create([
                            'user_id' => $userId,
                            'shop_id' => $shopId,
                            'order_id' => $order->id,
                            'package_id' => $p->id,
                            'order_no' => $order['tid'] ?? null,
                            'template_id' => '',
                            'auth_source' => $authSource,
                            'parent_waybill_code' => array_get($item, 'parent_waybill_code') ?? "",
                            'waybill_code' => array_get($item, 'waybill_code'),
                            'wp_code' => $wpCode,
                            'print_data' => array_get($item, 'print_data') ?? "",
                            //'receiver_province' => $order->receiver_state,
                            //'receiver_city' => $order->receiver_city,
                            //'receiver_district' => $order->receiver_district,
                            //'receiver_name' => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_name_mask : $order->receiver_name,
                            //'receiver_phone' => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                            //'receiver_address' => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_address_mask : $order->receiver_address,
                            'app_id' => $appId,
                            'to_shop_id' => $order->shop_id,
                            'batch_no' => $batchNo,
//                            'waybill_index' => $counter,
//                            'waybill_count' => $total,
                        ]);
                        /*PrintRecord::create([
                            'user_id'           => $order->user_id,
                            'shop_id'           => $order->shop_id,
                            'order_id'          => $order->id,
                            'history_id'        => $history->id,
                            'package_id'        => $p->id,
                            'order_no'          => $order['tid'] ?? null,
                            'waybill_code'      => array_get($item, 'waybill_code'),
                            'wp_code'           => $wpCode,
                            'receiver_province' => $order->receiver_state,
                            'receiver_city'     => $order->receiver_city,
                            'receiver_district' => $order->receiver_district,
                            'receiver_town'     => $order->receiver_town,
                            'receiver_address'  => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_address_mask : $order->receiver_address,
                            'receiver_name'     => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_name_mask : $order->receiver_name,
                            'receiver_zip'      => $order->receiver_zip,
                            'receiver_phone'    => isset($order->orderCipherInfo) ? $order->orderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                            'buyer_remark'      => $order->buyer_message,
                            'app_id'            => $appId,
                        ]);*/

                        $printData[] = [
                            'orderSn' => $order->tid,
                            'waybillCode' => array_get($item, 'waybill_code'),
                            'parentWaybillCode' => array_get($item, 'parent_waybill_code') ?? "",
                            'printData' => array_get($item, 'print_data') ?? "",
                            'code' => 1,
                            'errorMsg' => '',
                        ];
                    }
                } else {
                    //取号错误信息
                    $failedOrders[] = [
                        'orderSn' => $order->tid,
                        'waybillCode' => '',
                        'parentWaybillCode' => '',
                        'printData' => '',
                        'code' => 0,
                        'errorMsg' => $package[0],
                    ];
                }
            }
        } else {
            //throw new \Exception('无可获取打印数据订单!');
            return throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }

        return ['print_data' => $printData, 'failed_data' => $failedOrders];
    }

    public static function recoveryForOpenApi($waybillHistory, $waybill, $platform)
    {
        // 判断是正常订单还是自由打印订单
        $orders = self::query()->where('id', $waybillHistory->order_id)->first();
        if (empty($orders)) {
            $orders = CustomizeOrder::query()->where(['id', $waybillHistory->order_id])->first();
            if (empty($orders)) {
                return '无此运单号';
            }
        }

        try {
            if ($platform == Waybill::OPEN_API_DEFAULT) {
                $authSource = config('app.platform') == 'dy' ? Waybill::AUTH_SOURCE_DY : Waybill::AUTH_SOURCE_JD;
            } else {
                $authSource = $waybill->auth_source;
            }
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $ret = $waybillService->wayBillCancelDiscard($waybillHistory->wp_code, $waybillHistory->waybill_code,
                $waybillHistory->platform_waybill_id ?? '');
            if (!$ret) {
                Log::error('waybill recovery failed !', ['Orders' => $orders->toArray()]);
                return '取消失败';
            }

            WaybillHistory::where('package_id', '<>', 0)
                ->where([
                    'order_id' => $orders->id,
                    'waybill_code' => $waybillHistory->waybill_code
                ])->update(['waybill_status' => WaybillHistory::WAYBILL_RECOVERY_YES]);

            Package::where('waybill_code', $waybillHistory->waybill_code)
                ->update([
                    'recycled_at' => Carbon::now()
                ]);
            Order::query()->where('id', $waybillHistory->order_id)
                ->update(['print_status' => Order::PRINT_STATUS_NO]);
        } catch (\Exception $e) {
            Log::error('recovery failed !',
                ['msg' => $e->getMessage(), 'express_no' => $waybillHistory->waybill_code, 'order' => $orders]);
            return '取消失败';

        }

        return true;
    }


    /**
     * 对前端的订单状态映射
     * @param $orderStatus
     * @return array
     * <AUTHOR>
     */
    public static function orderStatusFrontMap($orderStatus): array
    {
        switch ($orderStatus) {
            case static::ORDER_STATUS_PAYMENT:
                $arr = [
                    static::ORDER_STATUS_PAYMENT,
                    static::ORDER_STATUS_PART_DELIVERED,
                ];
                break;
            case static::ORDER_STATUS_DELIVERED:
                $arr = [
                    static::ORDER_STATUS_PART_DELIVERED,
                    static::ORDER_STATUS_DELIVERED
                ];
                break;
            default:
                $arr = [
                    $orderStatus
                ];
                break;
        }
        return $arr;
    }

    /**
     * 对前端的打印状态映射
     * @param $printStatus
     * @return array
     * <AUTHOR>
     */
    public static function printStatusFrontMap($printStatus): array
    {
        switch ($printStatus) {
            case static::PRINT_STATUS_YES:
                $arr = [
                    static::PRINT_STATUS_YES,
                    static::PRINT_STATUS_PART,
                ];
                break;
            case static::PRINT_STATUS_NO:
                $arr = [
                    static::PRINT_STATUS_NO,
                    static::PRINT_STATUS_PART,
                ];
                break;
            default:
                $arr = [
                    $printStatus
                ];
                break;
        }
        return $arr;
    }


    /**
     * 保存加密数据
     * @param  AbstractOrderService  $orderService
     * @param  array  $orders
     * @param  array  $cipherInfoArr
     * @param  array  $addressChangeOrders  地址变更的订单
     * <AUTHOR>
     */
    public static function handleSaveCipher(
        AbstractOrderService $orderService,
        array &$orders,
        array &$cipherInfoArr,
        array $addressChangeOrders
    ) {
        if ($orderService instanceof OrderCipherInterface) {
            $maskArr = $searchArr = [];
            //需要关注的字段
            $fieldArr = ['receiver_phone', 'receiver_name', 'receiver_address', 'receiver_telephone'];
            //集中处理掩码，把掩码准备好
            foreach ($fieldArr as $field) {
                //京东自带掩码手机号和固话
                if (config('app.platform') == 'jd' && ($field == 'receiver_phone' || $field == 'receiver_telephone')) {
                    foreach ($orders as $item) {
                        $maskArr[$field][] = [
                            'tid' => $item['tid'],
                            'text' => $item[$field.'_mask']
                        ];
                    }
                } else {
                    //把这批订单的加密数据都提取出来，这些数据进行脱敏处理
                    $list = collect($orders)->map(function ($order) use ($field, $addressChangeOrders) {
                        // 没有地址变更的订单不处理
                        $oaid = array_get($order, 'cipher_info.oaid');
                        if (!isset($addressChangeOrders[$order['tid']])) {
                            return [];
                        }
                        if ($field == 'receiver_telephone') {
                            return [];
                        }
                        $isRemoteTransit = $order['is_remote_transit'] ?? 0;
                        if (Environment::isJd() && ($isRemoteTransit)) {
                            //jD的集运订单是没有加密信息的
                            return [];
                        }
                        $arr = [
                            'tid' => $order['tid'],
                            'text' => $order[$field],
                            'field' => $field
                        ];
                        return $arr;
                    })->filter()->toArray();
//                    Log::info("脱敏订单数据", ['list' => $list]);

                    // return ['tid','text']
                    /*if (count($list) <= 50 && config('app.platform') != 'jd') {
                        $maskArr[$field] = $orderService->cipherDecryptMaskBatch($list);
                    } else {
                        $maskArr[$field] = [];
                        $listArr = array_chunk($list, 50);
                        foreach ($listArr as $listItem) {
                            $temp = $orderService->cipherDecryptMaskBatch($listItem);
                            $maskArr[$field] = array_merge($temp, $maskArr[$field]);
                        }
                    }*/


                    if (config('app.platform') != PlatformConst::KS) {
                        $maskArr[$field] = [];
                        $listArr = array_chunk($list, 50);
                        foreach ($listArr as $listItem) {
                            //进行解密，脱敏字段
                            $temp = $orderService->cipherDecryptMaskBatch($listItem);

                            $maskArr[$field] = array_merge($temp, $maskArr[$field]);
                        }
                    }
                }
            }
            foreach ($orders as $index => $order) {
                $platformType = $order['type'];
                $isRemoteTransit = array_get($order, 'is_remote_transit', false);

                $tid = $order['tid'];
                $cipher_info = $order['cipher_info'];
//                Log::info("JD oaid", [$cipher_info, $order]);
                //如果是jd，而且有oaid，不处理了，有oaid的情况下$order里面会自带脱敏信息
                $oaid = array_get($order, 'cipher_info.oaid');

                if (PlatformConst::isJdByPlatformType($platformType) && $oaid) {
                    //判断是不是加密的，如果名字的掩码字段超过20个字符，说明是加密的
                    $isEncrypt = mb_strlen(array_get($cipher_info, 'receiver_name_mask')) > 20;
                    //不加密的情况下，已经有脱敏信息已经是对的了，不需特殊处理
                    if (!$isEncrypt) {
                        continue;
                    }

                }


                //如果是集运订单无需解密，订单里面放的就是明文
                $cipher_info = [];

                foreach ($fieldArr as $field) {
                    // 没有地址变更的订单不处理
                    if (!isset($addressChangeOrders[$tid])) {
                        continue;
                    }
                    if (PlatformConst::isJdByPlatformType($platformType) && ($isRemoteTransit)) {
                        //如果是JD的集运订单，联系人信息不做掩码处理,直接从order的联系人信息复制过来
                        $cipher_info[$field.'_mask'] = $order[$field];
                        continue;
                    }
                    $ciphertext = $order[$field] ?? '';

                    if (PlatformConst::isKsByPlatformType($platformType)) {
                        $cipher_info[$field.'_mask'] = $order['cipher_info'][$field.'_mask'] ?? '';
                    } else {
                        $maskKV = collect($maskArr[$field])->pluck('text', 'tid')->toArray();
                        $cipher_info[$field.'_mask'] = $maskKV[$tid];
                    }
                    $cipher_info[$field.'_ciphertext'] = $ciphertext;
                    // 搜索字段替换原来字段
                    if (PlatformConst::isJdByPlatformType($platformType)) {
//                        \Log::info("订单信息",$order);
                        if (ArrayUtil::getArrayValue($order, 'cipher_info.oaid')) {
                            //如果有oaid，用oaid进行后续搜索 不需要用这个进行搜索，这个搜索字段就放返回的联系人信息，其实就是掩码
                            $cipherExtractSearch = $ciphertext;
                        } else {
                            //京东直接用加密的手机号进行搜索
                            if ('receiver_phone' == $field || 'receiver_telephone' == $field) {
                                $cipherExtractSearch = $ciphertext;
                            } else {
                                $cipherExtractSearch = $orderService->cipherExtractSearch($ciphertext);
                            }
                        }
                    } else {
                        $cipherExtractSearch = $orderService->cipherExtractSearch($ciphertext);
                        if ('receiver_phone' == $field && !PlatformConst::isKsByPlatformType($platformType)) {
                            $cipherExtractSearch = '$'.$cipherExtractSearch.'$';
                        }
                    }

                    $orders[$index][$field] = $cipherExtractSearch;

                }
                $cipherInfoArr[$tid] = $cipher_info;
//                Log::info('加密数据', ['cipherInfoArr' => $cipher_info, 'orders' => $orders]);
            }

        }

    }

    /**
     * 是否虚假地址
     * @param $province
     * @param $city
     * @param $district
     * @param $address
     * @return false
     * <AUTHOR>
     */
    public static function isFakeAddress($province, $city, $district, $address): bool
    {
        //        地址检查的逻辑主要是这样的
        //1. 对以上省份：检查详细地址是否包含“海南省|内蒙古自治区|青海省|西藏自治区|新疆维吾尔自治区|海南|内蒙古|青海|西藏|新疆)[^路街]")”
        // 1.1 如果找到的省份信息和详细地址中的省份信息不匹配，不通过属于异常.
        //2. 详细地址是不包含“县|区|旗|市|甘南藏族自治州|临夏回族自治州|阿拉善盟|锡林郭勒盟|兴安盟|果洛藏族自治州|海北藏族自治州|海南藏族自治州|海西蒙古族藏族自治州|黄南藏族自治州|玉树藏族自治州|巴音郭楞蒙古自治州|博尔塔拉蒙古自治州|昌吉回族自治州|克孜勒苏柯尔克孜自治州|伊犁哈萨克自治州” -正常
        // 2.1 提取详细地址中的“城市”，找到城市和省的信息，跟邮寄地址中的省份、城市信息进行比较，不通过属于异常
        // 2.2 提取详细地址中的“区县”，找到城市和省的信息，跟邮寄地址中的省份,区进行匹配，不通过属于异常
        //3 通过以上校验-正常地址
        $specialStr = '(海南省|内蒙古自治区|青海省|西藏自治区|新疆维吾尔自治区|海南|内蒙古|青海|西藏|新疆)';
        $statePattern = "/{$specialStr}/";
        $addressPattern = "/{$specialStr}[^路街]/";
        // 详细地址里有特殊的省份且省份里面没有匹配，那就是假地址
        if (preg_match($addressPattern, $address) && !preg_match($statePattern, $province)) {
            return true;
        }
        // 偏远地区的区名 取自 \App\Models\Address::getRemoteDistrictName
        $remoteDistrictListStr = '["白沙黎族自治县","保亭黎族苗族自治县","昌江黎族自治县","儋州市","定安县","东方市","龙华区","美兰区","琼山区","秀英区","澄迈县","乐东黎族自治县","临高县","陵水黎族自治县","琼海市","琼中黎族苗族自治县","南沙区","西沙区","海棠区","吉阳区","天涯区","崖州区","屯昌县","万宁市","文昌市","五指山市","阿拉善右旗","阿拉善左旗","额济纳旗","达尔罕茂明安联合旗","东河区","固阳县","九原区","昆都仑区","青山区","石拐区","白云鄂博矿区","土默特右旗","磴口县","临河区","乌拉特后旗","乌拉特中旗","乌拉特前旗","五原县","杭锦后旗","敖汉旗","阿鲁科尔沁旗","巴林右旗","巴林左旗","喀喇沁旗","克什克腾旗","红山区","林西县","宁城县","翁牛特旗","松山区","元宝山区","阿尔山市","科尔沁右翼前旗","科尔沁右翼中旗","扎赉特旗","突泉县","乌兰浩特市","和林格尔县","回民区","清水河县","赛罕区","托克托县","土默特左旗","武川县","新城区","玉泉区","阿荣旗","陈巴尔虎旗","额尔古纳市","鄂温克族自治旗","根河市","海拉尔区","扎赉诺尔区","满洲里市","莫力达瓦达斡尔族自治旗","新巴尔虎右旗","鄂伦春自治旗","新巴尔虎左旗","牙克石市","扎兰屯市","达拉特旗","东胜区","伊金霍洛旗","杭锦旗","准格尔旗","康巴什区","鄂托克旗","鄂托克前旗","乌审旗","霍林郭勒市","科尔沁区","科尔沁左翼后旗","科尔沁左翼中旗","库伦旗","扎鲁特旗","开鲁县","奈曼旗","海勃湾区","海南区","乌达区","丰镇市","化德县","集宁区","凉城县","察哈尔右翼后旗","察哈尔右翼前旗","察哈尔右翼中旗","商都县","四子王旗","兴和县","卓资县","阿巴嘎旗","东乌珠穆沁旗","多伦县","二连浩特市","苏尼特右旗","苏尼特左旗","太仆寺旗","西乌珠穆沁旗","镶黄旗","锡林浩特市","正蓝旗","正镶白旗","班玛县","达日县","甘德县","久治县","玛多县","玛沁县","刚察县","海晏县","门源回族自治县","祁连县","化隆回族自治县","互助土族自治县","乐都区","民和回族土族自治县","平安区","循化撒拉族自治县","共和县","贵德县","贵南县","同德县","兴海县","大柴旦行政区","德令哈市","都兰县","格尔木市","茫崖市","天峻县","乌兰县","河南蒙古族自治县","尖扎县","同仁市","泽库县","城北区","城东区","城西区","城中区","大通回族土族自治县","湟源县","湟中区","称多县","囊谦县","曲麻莱县","玉树市","杂多县","治多县","阿克苏市","阿瓦提县","拜城县","柯坪县","库车市","温宿县","乌什县","沙雅县","新和县","阿勒泰市","布尔津县","福海县","富蕴县","哈巴河县","吉木乃县","青河县","阿拉尔市","博湖县","和静县","和硕县","库尔勒市","轮台县","且末县","若羌县","焉耆回族自治县","尉犁县","北屯市","阿拉山口市","博乐市","精河县","温泉县","昌吉市","阜康市","呼图壁县","吉木萨尔县","玛纳斯县","木垒哈萨克自治县","奇台县","巴里坤哈萨克自治县","伊吾县","伊州区","和田市","和田县","洛浦县","民丰县","墨玉县","皮山县","策勒县","于田县","胡杨河市","巩留县","霍尔果斯市","霍城县","奎屯市","尼勒克县","察布查尔锡伯自治县","特克斯县","新源县","伊宁县","伊宁市","昭苏县","白碱滩区","独山子区","克拉玛依区","乌尔禾区","巴楚县","伽师县","喀什市","麦盖提县","泽普县","莎车县","疏附县","疏勒县","塔什库尔干塔吉克自治县","叶城县","英吉沙县","岳普湖县","可克达拉市","阿合奇县","阿克陶县","阿图什市","乌恰县","昆玉市","石河子市","双河市","额敏县","和布克赛尔蒙古自治县","沙湾市","塔城市","托里县","乌苏市","裕民县","铁门关市","图木舒克市","高昌区","鄯善县","托克逊县","达坂城区","米东区","水磨沟区","天山区","头屯河区","乌鲁木齐县","新市区","沙依巴克区","五家渠市","当雄县","达孜区","堆龙德庆区","城关区","林周县","墨竹工卡县","尼木县","曲水县","安多县","班戈县","巴青县","比如县","嘉黎县","聂荣县","尼玛县","色尼区","双湖县","申扎县","索县","普兰县","措勤县","改则县","噶尔县","革吉县","日土县","札达县","巴宜区","波密县","察隅县","工布江达县","米林县","墨脱县","朗县","边坝县","八宿县","察雅县","丁青县","贡觉县","江达县","卡若区","类乌齐县","洛隆县","芒康县","左贡县","聂拉木县","定结县","江孜县","谢通门县","岗巴县","康马县","白朗县","昂仁县","仁布县","拉孜县","南木林县","桑珠孜区","萨嘎县","萨迦县","亚东县","定日县","仲巴县","吉隆县","措美县","错那县","贡嘎县","加查县","浪卡子县","洛扎县","隆子县","乃东区","琼结县","曲松县","桑日县","扎囊县"]';
        $remoteDistrictList = json_decode($remoteDistrictListStr, true);
        foreach ($remoteDistrictList as $index => $remoteDistrict) {
            if (strpos($address, $remoteDistrict) !== false && !in_array($remoteDistrict,
                    [$province, $city, $district])) {
                // 区里面没有匹配，那就是假地址
//                echo $remoteDistrict.PHP_EOL;
                return true;
            }
        }
        if (Environment::isDy()) {
            // 提取地址前 2 个字符,比如:四川
            $address2Words = mb_substr($address, 0, 2);
            // 提取地址第 3 个字
            $addressThirdWords = mb_substr($address, 2, 1);
            $province2Words = mb_substr($province, 0, 2);
            if ($address2Words != $province2Words // 这 2 个字不在省里
                && in_array($address2Words, getAllProvincesBy2Words()) // 这 2 个字和省份的前 2 个字匹配
                && $addressThirdWords != '路' // 排除 xx路 这种情况，比如：北京路
            ) {
                return true;
            }
        } else {
            // 从详细地址从提取出 xx省，判断和省份是否一样
//            $pattern = "/(\w{2}省)/u";
//            preg_match($pattern, $address, $matches);
//            if (!empty($matches[1]) && $province != $matches[1]) {
//                return true;
//            }
        }

        return false;
    }

    /**
     * 获取sku信息和编码的查询
     * @param  bool  $isInclude
     * @param $value
     * @param $shopIds
     * @param $accurateWhere
     * @return mixed
     * <AUTHOR>
     */
    public static function getAccurateWhereByGoodsSku($value, $shopIds, $accurateWhere, bool $isInclude)
    {
        $skuIdArr = GoodsSku::query()
            ->where('custom_sku_value', '=', $value)
            ->whereIn('shop_id', $shopIds)
            ->get(['sku_id'])
            ->pluck('sku_id')
            ->toArray();
        $accurateWhere[] = [
            'func' => 'where',
            'args' => function (Builder $query) use ($value, $skuIdArr, $isInclude) {
                if ($isInclude) {
                    $query->where('order_items.sku_value', 'like', "%{$value}%");
                    $query->orWhere('order_items.outer_sku_iid', 'like', "%{$value}%");
                    !empty($skuIdArr) && $query->orWhereIn('order_items.sku_id', $skuIdArr);
                } else {
                    $query->where('order_items.sku_value', 'not like', "%{$value}%");
                    $query->where('order_items.outer_sku_iid', 'not like', "%{$value}%");
                    !empty($skuIdArr) && $query->whereNotIn('order_items.sku_id', $skuIdArr);
                }
            },
        ];
        return $accurateWhere;
    }
    public static function getAccurateWhereByGoodsSku12($field,$value, $accurateWhere, bool $isInclude)
    {
        $accurateWhere[] = [
            'func' => 'where',
            'args' => function (Builder $query) use ($field,$value, $isInclude) {
                if ($isInclude) {
                    $query->where('order_items.'.$field, 'like', "%{$value}%");
                } else {
                    $query->where('order_items.'.$field, 'not like', "%{$value}%");
                }
            },
        ];
        return $accurateWhere;
    }

    /**
     * 获取商品信息和编码的查询
     * @param $value
     * @param $shopIds
     * @param $accurateWhere
     * @param  bool  $isInclude
     * @return mixed
     * <AUTHOR>
     */
    public static function getAccurateWhereByGoods($value, $shopIds, $accurateWhere, bool $isInclude)
    {
        $numIidArr = Goods::query()
            ->where('custom_title', '=', $value)
            ->whereIn('shop_id', $shopIds)
            ->get(['num_iid'])
            ->pluck('num_iid')
            ->toArray();
        $accurateWhere[] = [
            'func' => 'where',
            'args' => function (Builder $query) use ($value, $numIidArr, $isInclude) {
                if ($isInclude) {
                    $query->where('order_items.goods_title', 'like', "%{$value}%");
                    $query->orWhere('order_items.outer_iid', 'like', "%{$value}%");
                    $query->orWhere('order_items.outer_sku_iid', 'like', "%{$value}%");
                    !empty($numIidArr) && $query->orWhereIn('order_items.num_iid', $numIidArr);
                } else {
                    $query->where('order_items.goods_title', 'not like', "%{$value}%");
                    $query->where('order_items.outer_iid', 'not like', "%{$value}%");
                    $query->where('order_items.outer_sku_iid', 'not like', "%{$value}%");
                    !empty($numIidArr) && $query->whereNotIn('order_items.num_iid', $numIidArr);
                }

            },
        ];
        return $accurateWhere;
    }

    //获取真实包裹数量

    /**
     * @return null
     */
    public function getIsChildParentOrderAttribute()
    {
        return $this->is_child_parent_order;
    }


    /**
     * @param  null  $is_child_parent_order
     */
    public function setIsChildParentOrderAttribute($is_child_parent_order): void
    {
        $this->is_child_parent_order = $is_child_parent_order;
    }

    /**
     * @return null
     */
    public function getChildParentPackagesCountAttribute()
    {
        return $this->child_parent_packages_count;
    }

    /**
     * @param  null  $child_parent_packages_count
     */
    public function setChildParentPackagesCountAttribute($child_parent_packages_count): void
    {
        $this->child_parent_packages_count = $child_parent_packages_count;
    }

    /**
     *
     * @param $oldOrder
     * @param $order
     * @param $cipherInfo
     * @param  int  $shopId
     * @return void
     */
    public static function handleAbnormalOrder($oldOrder, $order, $cipherInfo, int $shopId): void
    {

        //if (!empty($oldOrder) && $oldOrder->print_status == Order::PRINT_STATUS_YES && $oldOrder->order_status == Order::ORDER_STATUS_PAYMENT) {
        // 打印后改地址
        $isPrint = in_array($oldOrder['print_status'], [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART]);
        // 打印后合单有变化：没有老订单且有已打印未发货的同地址订单
        if (empty($oldOrder)) {
            $desc = "打印后合单有变化";

            $abnormal_type = AbnormalOrder::TYPE_OF_ORDER_MERGE_CHANGE;
            $abnormalOrders = \App\Models\Fix\Order::query()
                ->where('shop_id', $shopId)
                ->where('address_md5', $order['address_md5'])
                ->whereIn('print_status', [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART])
                ->where('order_status', Order::ORDER_STATUS_PAYMENT)
                ->get();
            $count = $abnormalOrders->count();
            if ($count > 0) {
                AbnormalOrder::query()->updateOrCreate(['order_id' => $order['id'], 'type' => $abnormal_type], [
                    'type' => $abnormal_type,
                    'status' => AbnormalOrder::STATUS_OF_UNREAD,
                    'shop_id' => $order['shop_id'],
                    'desc' => $desc,
                    'order_id' => $order['id']
                ]);
                $orderIds = $abnormalOrders->pluck('id')->toArray();
                \App\Models\Fix\Order::query()->whereIn('id', $orderIds)->update(['abnormal_type' => $abnormal_type]);
                return;
            }
        }
//        Log::debug('handleAbnormalOrder',['oldOrder'=>$oldOrder,'order'=>$order,'cipherInfo'=>$cipherInfo,'shopId'=>$shopId,]);

        $abnormal_type = null;
        if ($isPrint && $oldOrder->address_md5 != $order['address_md5']) { // 地址有变化
            $extra = [
                'old' => [
                    'name' => $cipherInfo['receiver_name_mask'] ?? $oldOrder['receiver_name'],
                    'phone' => $cipherInfo['receiver_phone_mask'] ?? $oldOrder['receiver_phone'],
                    'address' => $oldOrder['receiver_state'].$oldOrder['receiver_city'].$oldOrder['receiver_district'].($cipherInfo['receiver_address_mask'] ?? $oldOrder['receiver_address'])
                ],
                'new' => [
                    'name' => $cipherInfo['receiver_name_mask'] ?? $order['receiver_name'],
                    'phone' => $cipherInfo['receiver_phone_mask'] ?? $order['receiver_phone'],
                    'address' => $order['receiver_state'].$order['receiver_city'].$order['receiver_district'].($cipherInfo['receiver_address_mask'] ?? $order['receiver_address'])
                ]
            ];
//                if ($oldOrder->print_status == Order::PRINT_STATUS_YES) {
//                    $desc = "打印后修改收货地址";
//                } else {
//                    $desc = "打印前修改收货地址";
//                }
            $desc = "打印后修改收货地址";
            $abnormal_type = AbnormalOrder::TYPE_OF_ADDRESS_UPDATE;
            AbnormalOrder::query()->updateOrCreate(['order_id' => $oldOrder->id, 'type' => $abnormal_type], [
                'type' => $abnormal_type,
                'status' => AbnormalOrder::STATUS_OF_UNREAD,
                'shop_id' => $shopId,
                'desc' => $desc,
                'extra' => json_encode($extra),
                'order_id' => $oldOrder->id
            ]);
        }

        // 备注是否修改 打印后修改备注
        if ($isPrint && $oldOrder['seller_memo'] != $order['seller_memo']) {
            if (!in_array($order['order_status'], [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
                // 非待发货不提示
                return;
            }
            $extra = [
                'old' => json_decode($oldOrder['seller_memo'].true)[0] ?? "",
                'new' => json_decode($order['seller_memo'], true)[0] ?? ""
            ];
            $abnormal_type = AbnormalOrder::TYPE_OF_ORDER_REMARK;
            AbnormalOrder::query()->updateOrCreate([
                'order_id' => $oldOrder->id, 'type' => $abnormal_type, 'status' => AbnormalOrder::STATUS_OF_UNREAD
            ], [
                'type' => $abnormal_type,
                'status' => AbnormalOrder::STATUS_OF_UNREAD,
                'shop_id' => $shopId,
                'desc' => '打印后修改备注',
                'extra' => json_encode($extra),
                'order_id' => $oldOrder->id
            ]);
        }

        // 打印后有售后
        if ($isPrint && $oldOrder['refund_status'] == Order::PRINT_STATUS_NO
            && in_array($order['refund_status'], [Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART])) {

            if (!empty($order['send_at'])) {
                $desc = "打印后订单已发货，用户退款";
                $abnormal_type = AbnormalOrder::TYPE_OF_ORDER_SEND_REFUND;
            } else {
                $desc = "打印后订单未发货，用户退款";
                $abnormal_type = AbnormalOrder::TYPE_OF_ORDER_UNSENT_REFUND;
            }
            AbnormalOrder::query()->updateOrCreate(['order_id' => $oldOrder['id'], 'type' => $abnormal_type], [
                'type' => $abnormal_type,
                'status' => AbnormalOrder::STATUS_OF_UNREAD,
                'shop_id' => $oldOrder['shop_id'],
                'desc' => $desc,
                'order_id' => $oldOrder['id']
            ]);
//                        else {
//                            $desc = "未取号用户申请退款";
//                        }

        }

//            //锁单待发货已超时
//            if (!empty($oldOrder['locked_at']) && strtotime($oldOrder['promise_ship_at']) <= strtotime(date('Y-m-d H:i:s', time()))) {
//                $desc = "锁单待发货已超时";
//                AbnormalOrder::query()->updateOrCreate(['order_id' => $oldOrder['id'], 'type' => AbnormalOrder::TYPE_OF_DELIVERY_TIMEOUT], [
//                    'type' => AbnormalOrder::TYPE_OF_DELIVERY_TIMEOUT,
//                    'status' => AbnormalOrder::STATUS_OF_UNREAD,
//                    'shop_id' => $oldOrder['shop_id'],
//                    'desc' => $desc,
//                    'order_id' => $oldOrder['id']
//                ]);
//            }


        if (!empty($abnormal_type)) {
//            $order['abnormal_type'] = $abnormal_type;
            Order::query()->where('id', $order['id'])->update(['abnormal_type' => $abnormal_type]);
        }
    }

    /**
     * 处理平台返回的已发货包裹数据
     * @param  array  $allOrderIdArr
     * @return void
     */
    public static function handleLogisticsData(array $allOrderIdArr): void
    {
        if (empty($allOrderIdArr)) {
            return;
        }
        // 全部订单
        $orderList = \App\Models\Fix\Order::query()->with(['orderItem', 'orderExtra'])->whereIn('id',
            $allOrderIdArr)->get();
        if (empty($orderList) || $orderList->isEmpty()) {
            return;
        }
        $allLogisticsDataWaybills = [];
        foreach ($orderList as $order) {
            $logisticsDataList = json_decode($order->orderExtra['logistics_data'], true);
            if (!empty($logisticsDataList)) {
                $allLogisticsDataWaybills = array_merge($allLogisticsDataWaybills,
                    array_column($logisticsDataList, 'waybill_code'));
            }
        }
        // 全部包裹
        $packageList = Package::query()->with(['sendPackageOrders'])->whereIn('waybill_code',
            $allLogisticsDataWaybills)->get();
        // 平台物流
        $ptLogisticsList = PtLogistics::query()->with(['ptLogisticsItems'])->whereIn('waybill_code',
            $allLogisticsDataWaybills)->get();
        foreach ($orderList as $order) {
            $logisticsDataList = json_decode($order->orderExtra['logistics_data'] ?? '[]', true);
            $orderItemList = $order->orderItem;
            // 存在 $ptLogistic 表的运单号
            $waybillCodeArr = collect($logisticsDataList)->pluck('waybill_code')->toArray();
            foreach ($logisticsDataList as $logisticsData) {
                $logisticsDataBo = new LogisticsDataBo($logisticsData);
                if (empty($logisticsDataBo->waybill_code)) {
                    // 跳过空单号
                    continue;
                }
                $waybill_wp_index = gen_waybill_wp_index($logisticsDataBo->waybill_code, $logisticsDataBo->wp_code);
                $ptLogistics = $ptLogisticsList->where('waybill_code', $logisticsDataBo->waybill_code)
                    ->where('order_id', $order->id)->first();
                $package = $packageList->firstWhere('waybill_code', $logisticsDataBo->waybill_code);
                if (empty($ptLogistics)) { // 如果不存在
                    $packageId = !empty($package) ? $package->id : 0;
                    if (!empty($package) && $package->delivery_type > Package::DELIVERY_TYPE_NO) {
                        $source_type = PtLogistics::SOURCE_TYPE_INSIDE;
                    } else {
                        $source_type = PtLogistics::SOURCE_TYPE_OUTSIDE;
                    }
                    // 判断是不是已经存在,cache防止并发
                    $key = 'PtLogisticsExist:'.$order->shop_id.'_'.$order->id.'_'.$waybill_wp_index;
                    $exists = Cache::remember($key, 1, function () use ($waybill_wp_index, $order) {
                        return PtLogistics::query()
                            ->where('shop_id', $order->shop_id)
                            ->where('order_id', $order->id)
                            ->where('waybill_wp_index', $waybill_wp_index)
                            ->exists();
                    });
                    if ($exists) {
                        continue;
                    }
                    $delivery_at = $logisticsDataBo->delivery_at;
                    if (empty($delivery_at) && !empty($package)) {
                        //没有发货时间
                        $delivery_at = $package->send_at;
                        if (empty($delivery_at)) {
                            $delivery_at = $package->created_at;
                        }
                    }
                    // 还是没有发货时间
                    if (empty($delivery_at)) {
                        $delivery_at = date('Y-m-d H:i:s');
                    }

                    $ptLogistics = new PtLogistics();
                    $ptLogistics->shop_id = $order->shop_id;
                    $ptLogistics->order_id = $order->id;
                    $ptLogistics->package_id = $packageId;
                    $ptLogistics->source_type = $source_type;
                    $ptLogistics->waybill_code = $logisticsDataBo->waybill_code;
                    $ptLogistics->wp_code = $logisticsDataBo->wp_code;
                    $ptLogistics->delivery_at = $delivery_at;
                    $ptLogistics->delivery_id = $logisticsDataBo->delivery_id;
                    $ptLogistics->waybill_wp_index = $waybill_wp_index;
                    $ptLogistics->save();
                    redis('cache')->setex($key, 60, 1);
                    $ptLogisticsItems = collect();
                } else {
                    // 查找当前订单的子订单
                    $ptLogisticsItems = collect($ptLogistics->ptLogisticsItems->where('order_id', $order->id));
                }
                $dbOidArr = $ptLogisticsItems->pluck('oid')->toArray();
                $dataOidArr = collect($logisticsDataBo->product_list)->pluck('oid')->toArray();
                // 如果平台数据 $dataOidArr 有，但是数据库 $dbOidArr 没有，就创建
                $createOidArr = array_diff($dataOidArr, $dbOidArr);
                if (!empty($createOidArr)) {
                    foreach ($createOidArr as $createOid) {
                        /** @var LogisticsDataProductBo $logisticsDataProductBo */
                        $logisticsDataProductBo = collect($logisticsDataBo->product_list)->firstWhere('oid',
                            $createOid);
                        $orderItem = $orderItemList->firstWhere('oid', $logisticsDataProductBo->oid);
                        // 判断是不是已经存在,cache防止并发
                        $key = 'PtLogisticsItemExist:'.$ptLogistics->id.'_'.$orderItem->id;
                        $exists = Cache::remember($key, 1, function () use ($ptLogistics, $order, $orderItem) {
                            return PtLogisticsItem::query()
                                ->where('pt_logistics_id', $ptLogistics->id)
                                ->where('order_item_id', $orderItem->id)
                                ->exists();
                        });
                        if ($exists) {
                            continue;
                        }

                        $ptLogisticsItem = new PtLogisticsItem();
                        $ptLogisticsItem->shop_id = $order->shop_id;
                        $ptLogisticsItem->pt_logistics_id = $ptLogistics->id;
                        $ptLogisticsItem->oid = $logisticsDataProductBo->oid;
                        $ptLogisticsItem->order_id = $order->id;
                        $ptLogisticsItem->order_item_id = $orderItem->id;
                        $ptLogisticsItem->sku_uuid = $logisticsDataProductBo->sku_uuid;
                        $ptLogisticsItem->sku_id = $logisticsDataProductBo->sku_id;
                        $ptLogisticsItem->outer_sku_id = $logisticsDataProductBo->outer_sku_id;
                        $ptLogisticsItem->num_iid = $logisticsDataProductBo->num_iid;
                        if (Environment::isKs() && !empty($package)) {
                            // ks 没有返回商品数量，所以取包裹的数量
                            $firstSendPackageOrder = $package->sendPackageOrders->where('oid',
                                $logisticsDataProductBo->oid)->first();
                            $ptLogisticsItem->num = $firstSendPackageOrder->num ?? $logisticsDataProductBo->num;
                        } else {
                            $ptLogisticsItem->num = $logisticsDataProductBo->num;
                        }
                        $ptLogisticsItem->waybill_wp_index = $waybill_wp_index;
//                        $ptLogisticsItem->delivery_at = $logisticsDataBo->delivery_at;
//                        $ptLogisticsItem->delivery_id = $logisticsDataBo->delivery_id;
                        $ptLogisticsItem->save();
                        redis('cache')->setex($key, 60, 1);
                    }
                }
                // 如果数据库 $dbOidArr 有，但是平台数据 $dataOidArr 没有，就删除
//                $deleteOidArr = array_diff($dbOidArr, $dataOidArr);
//                if (!empty($deleteOidArr)) {
//                    $ptLogistics->ptLogisticsItems->where('order_id', $order->id)->whereIn('oid', $deleteOidArr)
//                        ->each(function ($ptLogisticsItem) use ($deleteOidArr, $dbOidArr, $dataOidArr) {
//                            Log::info('$deleteOidArr', [$deleteOidArr, $dbOidArr, $dataOidArr]);
////                            $ptLogisticsItem->forceDelete();
//                        });
//                }
            }
            if (empty($waybillCodeArr)) {
                continue;
            }
            // 删除 不存在 $waybillCodeArr 的
            $delPtLogisticArr = PtLogistics::query()
                ->where('shop_id', $order->shop_id)
                ->where('order_id', $order->id)
                ->whereNotIn('waybill_code', $waybillCodeArr)
                ->get();
            $delPtLogisticIdArr = $delPtLogisticArr->pluck('id')->toArray();
            PtLogisticsItem::query()->whereIn('pt_logistics_id', $delPtLogisticIdArr)->forceDelete();
            PtLogistics::query()->whereIn('id', $delPtLogisticIdArr)->forceDelete();
            // 商品已发货数量数组
            $goodsSendNumList = [];
            // 修改订单商品剩余数量和发货数量
            if (Environment::isDy() || Environment::isXhs() || Environment::isWxsp()) {
                $logisticsDataList = json_decode($order->orderExtra['logistics_data'] ?? '[]', true);
                foreach ($logisticsDataList as $logisticsData) {
                    $logisticsDataBo = new LogisticsDataBo($logisticsData);
                    if (Environment::isDy() && empty($logisticsDataBo->delivery_at)) {
                        // 抖音没有发货时间的是追加的
                        continue;
                    }
                    foreach ($logisticsDataBo->product_list as $product) {
                        if (!isset($goodsSendNumList[$product->oid])) {
                            $goodsSendNumList[$product->oid] = 0;
                        }
                        $goodsSendNumList[$product->oid] += $product->num;
                    }
                }
                foreach ($goodsSendNumList as $oid => $sendNum) {
                    $orderItem = $orderItemList->firstWhere('oid', $oid);
                    $send_remain_num = $orderItem->goods_num - $sendNum;
                    OrderItem::query()->where('id', $orderItem->id)
                        ->where('send_remain_num', '!=', $send_remain_num)
                        ->update(['send_remain_num' => $send_remain_num, 'send_num' => $sendNum]);
                }
            }
        }

    }

    /**
     * 更新订单的打印状态，这个是看这个订单当前有没有已经被发掉或者预发货的订单，如果没有，而且订单是未发货或者部分发货，则修改打印状态为未打印
     * @return void
     */
    public function updateOrderPrintStatus()
    {
        $count = $this->packages()->where(function ($query) {
            // 没有发货且没有预发货的包裹
//            $query->whereNull('send_at');
            $query->where(DB::raw('IFNULL(packages.status, 0)'), '!=', Package::ORDER_STATUS_DELIVERED);
            $query->whereNull('pre_shipment_at');
        })->whereNull('recycled_at')->count();
        Log::debug('未完全发货的包裹', [$count, $this->order_status]);
        // 未完全发货的包裹，判断还有没发完的，修改打印状态为未打印
        if ($count <= 0 && in_array($this->order_status,
                [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
            $this->update(['print_status' => Order::PRINT_STATUS_NO]);
        } else {
            $this->update(['print_status' => Order::PRINT_STATUS_YES]);
        }
    }

    /**
     * 取消预发货，
     * case 一个订单拆成了两个包裹发货，只有包裹取消了预发货，所以不能把所有的订单直接改成取消预发货,要看订单下有没有预发货的包裹
     * ，如果没有，则取消订单的预发货状态
     * @return void
     */
    public function cancelPreShipment()
    {

        if ($this->packages()->where('pre_shipment_status', Package::PRE_SHIPMENT_STATUS_YES)->whereNull('recycled_at')
                ->count() == 0) {
            $this->update(['pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_NO]);
        }
    }

    /**
     * 获取平台名称
     * @throws ErrorCodeException
     */
    public function getPlatformName(): string
    {
        return PlatformConst::getPlatformName(PlatformConst::map2Platform($this->type));
    }
}
