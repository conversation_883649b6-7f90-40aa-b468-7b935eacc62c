<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PackageOrder extends Model
{
    protected $fillable = [
        'order_id',
        'package_id',
        'order_item_id',
        'num',
        'version',
        'tid',
        'oid',
        'num_iid',
        'sku_id',
        'status',
        'source_type',
        'delivery_type',
    ];

    public function orderItem()
    {
        return $this->hasOne('App\Models\Fix\OrderItem', 'id', 'order_item_id');
    }

    public function fixOrder()
    {
        return $this->belongsTo('App\Models\Fix\Order', 'order_id', 'id');
    }
    public function package(){
        return $this->belongsTo('App\Models\Package','package_id');
    }

    public function customGoodsSkus()
    {
        return $this->hasOne('App\Models\GoodsSku', 'sku_id', 'sku_id');
    }
}
