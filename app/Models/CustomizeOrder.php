<?php
namespace App\Models;
use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Http\StatusCode\StatusCode;
use App\Services\BusinessException;
use App\Services\Client\DyClient;
use App\Services\PrintDataService;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Company;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
class CustomizeOrder extends Model
{
    use SoftDeletes;
    const PRINT_STATUS_YES     = 1;         //已打印
    const PRINT_STATUS_NO      = 0;         //未打印
    const PRINT_STATUS_DELETE  = 2;         //已删除
    const PRINT_STATUS_ARR     = [
        self::PRINT_STATUS_NO,
        self::PRINT_STATUS_YES,
    ];
    const ORDER_TYPE_SINGLE    = 1; //单条
    const ORDER_TYPE_BATCH     = 2; //批量
    const PRINT_RESULT_SUCCESS = 'success'; //打印状态 成功

    const PRINT_WAYBILL_STATUS_NEW=1; //新单号打印


    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'template_id',
        'order_no',
        'order_type',
        'production_type',
        'product_remark',
        'receiver_province',
        'receiver_city',
        'receiver_district',
        'receiver_town',
        'receiver_name',
        'receiver_phone',
        'receiver_zip',
        'receiver_address',
        "sender_name",
        "sender_phone",
        "sender_tel",
        "sender_province",
        "sender_city",
        "sender_district",
        "sender_detailaddress",
        "receiver_tel",
        'sender_addressinfo',
        'goods_details',
        'goods_info',
        'num',
        'seller_memo',
        'parent_waybill_code',
        'waybill_code',
        'wp_code',
        'print_status',
        'recycled_at',
        'printed_at',
        'p_shop_id',
        'platform',
        'outer_order_no',
        'platform_type',
    ];

    protected $appends = ['request_id', 'is_child_parent_order', 'child_parent_packages_count', 'order_cipher_info'];

    public $request_id = null;
    public $is_child_parent_order = null;
    public $child_parent_packages_count = null;
    public $order_cipher_info = null;

    public static function firstById($id)
    {
        return CustomizeOrder::query()->where('id', $id)->first();
    }

    public function getRequestIdAttribute()
    {
        return $this->request_id;
    }
    public function setRequestIdAttribute($value)
    {
        $this->request_id = $value;
    }

    public function waybillHistories()
    {
        return $this->hasMany(WaybillHistory::class, 'order_id', 'id')
            ->where('order_type',WaybillHistory::ORDER_TYPE_FREE);
    }
    public function order()
    {
        return $this->hasOne(Order::class, 'tid', 'order_no');
    }
    public function shop()
    {
        return $this->hasOne(\App\Models\Fix\Shop::class, 'id', 'shop_id');
    }

    /**
     * list
     * @param array $condition
     * @param $shopIds
     * @param string $search
     * @param int $offset
     * @param int $limit
     * @param string $orderBy
     * @param int $printStatus
     * @return array
     */
    public static function search(array $condition,$shopIds, string $search, int $offset, int $limit, $orderBy, int
$printStatus)
    {
        \Log::info("手工订单查询条件", [$condition,$shopIds]);
       if ($printStatus == self::PRINT_STATUS_DELETE){
            $query = CustomizeOrder::where($condition)->onlyTrashed();
        }else{
            $query = CustomizeOrder::where($condition);
        }
        $waybillHistoriesFields = [
            'id',
            'order_id',
            'package_id',
            'template_id',
            'waybill_code',
            'wp_code',
            'waybill_status',
            'version',
            'batch_no',
            'waybill_index',
            'waybill_count',
            'created_at',
            'updated_at',
            'auth_source',
            'waybill_status',
        ];
        $query->with('waybillHistories:'.implode(',',$waybillHistoriesFields));
        $query->whereIn('shop_id',$shopIds);
        if ($search) {
            $waybillHistoryOrderIdArr = WaybillHistory::query()
//                ->where('order_type',WaybillHistory::ORDER_TYPE_FREE)
                ->where('waybill_code', $search)->get()->pluck('order_id')->toArray();
            $query = $query->where(function ($query) use ($search, $waybillHistoryOrderIdArr) {
                $query->where('order_no', $search)
                    ->orWhere('id', $search)
                    ->orWhere('receiver_name', $search)
//                    ->orWhere('waybill_code', $search)
                    ->orWhere('receiver_phone',$search);
                if (!empty($waybillHistoryOrderIdArr)){
                    $query->orWhereIn('id', $waybillHistoryOrderIdArr);
                }
            });

        }
        //$sortArr = explode(' ', $orderBy);
        $orderBy = json_decode($orderBy, true);

        if ($orderBy['column'] == 'area') {
            $query->orderBy('receiver_province', $orderBy['order'])
            ->orderBy('receiver_city', $orderBy['order'])
            ->orderBy('receiver_district', $orderBy['order']);
        } else {
            $query->orderBy($orderBy['column'], $orderBy['order']);
        }
        \Log::info("手工订单搜索SQL：".$query->toSql(),[$limit,$offset]);
        $count = $query->count();
        $ret =  $query->limit($limit)
            ->offset($offset)
            ->get();
        return [$ret,$count];
    }

	/**
	 * 地址信息
	 * @param int $userId
	 * @param int $shopId
	 * @param $company
	 * @param array $orderIds
	 * @return array
	 * @throws \Exception
	 */
    public static function getBranchAndSendAddress(int $userId, int $shopId, $company, array $orderIds)
    {
        $orders = CustomizeOrder::query()->where([
            //'user_id' => $userId,
            //'shop_id' => $shopId,
        ])->whereIn('id', $orderIds)
	        ->get();

        //网点地址
        $tempBranch = [
            'province' => $company->province,
            'city'     => $company->city,
            'district' => $company->district,
            'address'  => $company->detail,
            'street'   => $company->street ?? ''
        ];

        //发货地址 自由打印表里有发货地址，若没有，就给默认地址
        $ship = ShippingAddress::query()
            ->where('shop_id', $shopId)
            ->where('tip',ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->where('is_default', ShippingAddress::IS_DEFAULT_YES)
            ->first();
        if (empty($ship)) {
            throw new ApiException(ErrorConst::NOT_SET_DELIVERY_ADDRESS);
//            throw new \Exception('未设置默认发货地址');
        }

        $resBranchs = [];
        $resSenders = [];
	    foreach ($orders as $order) {
		    $temp                = [];
		    $temp['province']    = isset($order->sender_province) ? $order->sender_province : $ship->province;
		    $temp['city']        = isset($order->sender_city) ? $order->sender_city : $ship->city;
		    $temp['district']    = isset($order->sender_district) ? $order->sender_district : $ship->district;
		    $temp['address']     = isset($order->sender_detailaddress) ? $order->sender_detailaddress : $ship->address;
		    $temp['sender_name'] = isset($order->sender_name) ? $order->sender_name : $ship->sender_name;
		    $temp['mobile']      = isset($order->sender_phone) ? $order->sender_phone : $ship->mobile;

		    $resSenders[$order->id] = $temp;
		    $resBranchs[$order->id] = array_merge($tempBranch, [
			    'sender_name' => $order->sender_name,
			    'mobile'      => $order->sender_phone
		    ]);
	    }

        $result = [
            'branch_address' =>$resBranchs,
            'sender_address' =>$resSenders,
        ];

        return $result;
    }


	/**
	 * 多个旧单号打印
	 * @param int $userId
	 * @param int $shopId
	 * @param int $templateId
	 * @param array $orderIds
	 * @return array
	 * @throws \Exception
	 */
    public static function getWaybillHistoryData(int $userId,int $shopId, int $templateId, array $waybillHistories, array $orderIds)
    {
        $printRecords = [];
        $printData    = [];
        $templateObj = Template::query()->findOrFail($templateId);

        $company     = $templateObj->company;
        $template    = $templateObj->toArray();
        //网点地址与真实发货地址
        $sender = self::getBranchAndSendAddress($userId, $shopId, $company, $orderIds);
        if (empty($waybillHistories)) {
            return ['print_data' => $printData, 'new_order_ids' => $orderIds];
        }
        $hasWaybillIds = array_column($waybillHistories, 'order_id');
        foreach ($waybillHistories as $history) {
            //同步面单号
            $order               = self::findOrFail($history['order_id']);
            //$order->waybill_code = $history['waybill_code'];
            //$order->save();
            $printData[]    = PrintDataService::templateData($sender['sender_address'][$history['order_id']], $order, $template, $history, [], true);
            $printRecords[] = [
                'wp_code'      => $history['wp_code'],
                'waybill_code' => $history['waybill_code'],
                'order'        => $order->toArray(),
                'user_id'      => $history['user_id'],
                'package_id'   => $history['package_id'],
                'history_id'   => $history['id'],
            ];
        }

        //添加打印记录
        $records = PrintRecord::generate($printRecords, $userId, $shopId);
        if (!$records) {
            Log::error('打印记录添加失败!', ['data' => $printRecords]);
            throw new ApiException(ErrorConst::PRINTED_RECORD_ADD_FAIL);
//            throw new \Exception('打印记录添加失败');
        }

        return ['orders' => $printData, 'company' => $company];
    }

    /**
     * 取新旧单号打印
     * @param int $userId
     * @param int $shopId
     * @param int $templateId
     * @param array $orderIds
     * @return array
     * @throws \Exception
     */
    public static function getPrintHistoryDataWaybill(int $userId, int $shopId, int $templateId, array $orderIds, int $printWaybillStatus, $batchNo, int $packageNum = 1)
    {
        $templateObj = Template::query()->findOrFail($templateId);
        $company     = $templateObj->company;
        $template    = $templateObj->toArray();

        //网点地址与真实发货地址
        $addressInfo = self::getBranchAndSendAddress($userId, $shopId, $company, $orderIds);

        //判断是否为虚拟网点
        if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
            if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS,Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
                $auth = Shop::find($shopId);
                $auth->auth_source = $template['auth_source'];
            } else {
                $auth = Waybill::where([
                    'owner_id'    => $template['owner_id'],
                    'auth_source' => $template['auth_source']
                ])->orderBy('id', 'desc')->first();
            }
        } else {
            if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
                $auth = Shop::query()->where('identifier', $company->owner_id)->first();
                $auth->auth_source = $template['auth_source'];
            } else {
                $auth = Waybill::where([
                    'owner_id'    => $template['owner_id'],
                    'auth_source' => $template['auth_source']
                ])->orderBy('id', 'desc')->first();
            }
        }

        $failedData = [];

        if ($printWaybillStatus == 2) {
            //旧单号
            $hasWaybillHistories = self::hasWaybillHistories($userId, $shopId, $orderIds, $addressInfo, $template, $batchNo);
            $printData = $hasWaybillHistories['print_data'];
        } else {
            //判断是否是虚拟分享网点，且余额不为0，状态是正常
            if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
                if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_FREEZE);
//                  throw new \Exception('电子面单处于被冻结状态，请联系单号分享者!');
                }

              if ($company->source_status == Company::SOURCE_COMPANY_STATUS_OPEN && $company->quantity == Company::INIT_QUANTITY) {
                  throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//                  throw new \Exception('电子面单余额为0，请联系单号分享者!');
                }
            }
            //取新单号
            $orderIdsArr         = array_chunk($orderIds, 5);
            $newWaybillHistories = self::newWaybillHistories(
            	$userId,
                $shopId,
                $orderIdsArr,
                $addressInfo,
                $template,
                $auth->access_token,
                $batchNo,
                $packageNum
            );
            $printData           =  $newWaybillHistories['print_data'];
	       //取号失败信息
	       if (count($newWaybillHistories['failed_data']) > 0) {
		       $failedData = $newWaybillHistories['failed_data'];
	       }
       }

	    if (!$printData && !$failedData) {
            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
//            throw new \Exception('订单获取打印数据异常！');
	    }

	    return ['orders' => $printData, 'company' => $company, 'failed' => $failedData];
    }

    /**
     * 获取打印数据
     * @param int $userId
     * @param int $shopId
     * @param int $templateId
     * @param array $orderIds
     * @return array
     * @throws \Exception
     */
    public static function getPrintDataAndWaybill(int $userId, int $shopId, int $templateId, array $orderIds, $batchNo='',int $packageNum = 1)
    {
        $templateObj = Template::query()->findOrFail($templateId);
        $company     = $templateObj->company;
        $template    = $templateObj->toArray();

        //网点地址与真实发货地址
        $addressInfo = self::getBranchAndSendAddress($userId, $shopId, $company, $orderIds);
        //判断是否为虚拟网点
        if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
            if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS,Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
                $auth = Shop::find($shopId);
                $auth->auth_source = $template['auth_source'];
            } else {
                $auth = Waybill::where([
                    'owner_id'    => $template['owner_id'],
                    'auth_source' => $template['auth_source']
                ])->orderBy('id', 'desc')->first();
            }
        } else {
            if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
                $auth = Shop::query()->where('identifier', $company->owner_id)->first();
                $auth->auth_source = $template['auth_source'];
            } else {
                $auth = Waybill::where([
                    'owner_id'    => $template['owner_id'],
                    'auth_source' => $template['auth_source']
                ])->orderBy('id', 'desc')->first();
            }
        }
        if (!$auth) {
            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
//            throw new \Exception('电子面单授权信息丢失!');
        }

        //查询是否去过号
        $hasWaybillHistories = self::hasWaybillHistories($userId, $shopId, $orderIds, $addressInfo, $template, $batchNo);
        $printData = $hasWaybillHistories['print_data'];
        $failedData = [];
        if (!empty($hasWaybillHistories['new_order_ids'])) {

            //判断是否是虚拟分享网点，且余额不为0，状态是正常
            if ($company->source==Company::SOURCE_COMPANY_STATUS_YES) {
                if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_FREEZE);
//                    throw new \Exception('电子面单处于被冻结状态，请联系单号分享者!');
                }
                if ($company->source_status == Company::SOURCE_COMPANY_STATUS_OPEN && $company->quantity == Company::INIT_QUANTITY) {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//                    throw new \Exception('电子面单余额为0，请联系您的单号分享者!');
                }
            }

            //未取号取号处理
            $orderIdsArr         = array_chunk($hasWaybillHistories['new_order_ids'], 5);
            $newWaybillHistories = self::newWaybillHistories(
                $userId,
                $shopId,
                $orderIdsArr,
                $addressInfo,
                $template,
                $auth->access_token,
                $batchNo,
                $packageNum
            );
            $printData           = array_merge($hasWaybillHistories['print_data'],
                $newWaybillHistories['print_data']);

	        //取号失败信息
	        if (count($newWaybillHistories['failed_data']) > 0) {
		        $failedData = $newWaybillHistories['failed_data'];
	        }
        }

	    if (!$printData && !$failedData) {
            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
//            throw new \Exception('订单获取打印数据异常！');
	    }

        return ['orders' => $printData, 'company' => $company, 'failed' => $failedData];
    }

    /**
     * 已经取号订单打印数据
     * @param $orderIds
     * @param $sender
     * @param $template
     * @return array
     * @throws \Exception
     */
    public static function hasWaybillHistories(int $userId, int $shopId, $orderIds, $sender, $template, $batchNo)
    {
        $printRecords = [];
        $printData    = [];
        $order        = CustomizeOrder::query()->whereIn('id', $orderIds)->first();
        $oldTemplate  = Template::where('id', $order->template_id)->first();
	    if ($oldTemplate && $oldTemplate->auth_source != $template['auth_source']) {
            throw new ApiException(ErrorConst::CROSS_PLATFORM_PRINTING_NOT_SUPPORTED);
//            throw new \Exception('暂不支持跨平台模板打印！');
	    }
        $waybillHistories = WaybillHistory::where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
            ->where('package_id', 0)
            ->whereIn('order_id', $orderIds)
            ->get()
            ->toArray();
        if (empty($waybillHistories)) {
            return ['print_data' => $printData, 'new_order_ids' => $orderIds];
        }
        $batchNo = $batchNo ?? (int)(microtime(true) * 1000);
        $hasWaybillIds = array_column($waybillHistories, 'order_id');
        foreach ($waybillHistories as $history) {
            //同步面单号
            $order               = self::findOrFail($history['order_id']);
            //$order->waybill_code = $history['waybill_code'];
            //$order->save();
            $printData[]    = PrintDataService::templateData($sender['sender_address'][$history['order_id']], $order, $template, $history, [], true);
            $printRecords[] = [
                'wp_code'      => $history['wp_code'],
                'waybill_code' => $history['waybill_code'],
                'order'        => $order->toArray(),
                'user_id'      => $history['user_id'],
                'package_id'   => $history['package_id'],
                'history_id'   => $history['id'],
                'batch_no'     => $batchNo,
                'to_shop_id' => $order['shop_id'],
                'template_id' => $template['id'],
                'template_name' => $template['name'],
            ];
        }

        //添加打印记录
        $records = PrintRecord::generate($printRecords, $userId, $shopId);
        if (!$records) {
            Log::error('打印记录添加失败!', ['data' => $printRecords]);
            throw new ApiException(ErrorConst::PRINTED_RECORD_ADD_FAIL);
//            throw new \Exception('打印记录添加失败');
        }

        return ['print_data' => $printData, 'new_order_ids' => array_diff($orderIds, $hasWaybillIds)];
    }

    /**
     * 未取号订单打印
     * @param int $userId
     * @param int $shopId
     * @param array $newOrderIds
     * @param $sender
     * @param $template
     * @param $accessToken
     * @return array
     * @throws \Exception
     */
    public static function newWaybillHistories(int $userId, int $shopId, array $newOrderIds, $sender, $template, $accessToken, $batchNo, $packageNum = 1)
    {
        $company = $template['company'];
        $printData = [];
        $failedOrders = [];
        $batchNo = $batchNo ?? (int)(microtime(true) * 1000);
        foreach ($newOrderIds as $orderIds) {
            $orders = CustomizeOrder::whereIn('id', $orderIds)->get();
            Log::info('orders',[$orders]);
            if (empty($orders)) {
                throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
//                throw new \Exception('无订单数据!');
            }
            $ordersCount=collect($orders)->count();
            foreach ($orders as $order) {
                //抖音关联订单 查询加密数据
                if (isset($order->order_no)) {
                    $platformOrder = Order::query()->with('OrderCipherInfo')->where('tid', $order->order_no)->first();
                    if ($platformOrder->OrderCipherInfo) {
                        $order->order_cipher_info = $platformOrder->OrderCipherInfo;
                    }
                }
                //每次取号判断是否是虚拟网点，且正常状态为0，面单余额不为0
                if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES){
                    if($ordersCount > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
                        throw new ApiException(ErrorConst::WAYBILL_INSUFFICIENT_BALANCE);
//                        throw new \Exception('打印订单数大于可用单号余额!');
                    }
                    if($company['source_status'] == Company::SOURCE_COMPANY_STATUS_OPEN && $company['quantity'] !== Company::INIT_QUANTITY) {
                        $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
                        $packages       = $waybillService->assemWaybillPackages($sender['branch_address'][$order->id], [$order], $template ,$packageNum);
                        //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
                        if($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
                           $query = Company::where('id',$company['id']);
                           $query->decrement('quantity');
                           $query->increment('allocated_quantity');
                        }else{
                            //无限量的不用减少电子面单余额数量，已用面单数量增加
                           $query = Company::where('id',$company['id']);
                           $query->increment('allocated_quantity');
                        }
                    }else{
                        throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//                        throw new \Exception('电子面单余额为0，请联系您的单号分享者!');
                    }
                }else{
                    //非虚拟网点，正常取号
                    $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
                    $packages       = $waybillService->assemWaybillPackages($sender['branch_address'][$order->id], [$order], $template ,$packageNum);
                }

                foreach ($packages as $idStr => $package) {
                    if (isset($order->order_cipher_info)){
                        unset($order->order_cipher_info);
                    }
                    Log::info('package',[$package]);

                    //错误信息
                    if (!is_string($package[0]) && is_array($package[0]) && !empty($package[0]) && (isset($package[0]['err_no']) ? $package[0]['err_no'] == 0 : true)
                    && (Waybill::AUTH_SOURCE_DY == $template['auth_source'] ? (!isset($package[0]['data']['ebill_infos'])) : true)
                        && (Waybill::AUTH_SOURCE_KS == $template['auth_source'] ? (!isset($package[0]['result'])) : true)) {

                        foreach ($package as $key => $item) {
                            //查询一个订单是否存在旧单号
                            $tempWaybillCode = $item['parent_waybill_code'] ? $item['parent_waybill_code'] : $item['waybill_code'];
                            $waybill_code_Arr = isset($order->waybill_code) ? explode(',',$order->waybill_code) : '';
                            $expressNoArr     = isset($order->waybill_code) && $waybill_code_Arr ? array_unique(array_merge($waybill_code_Arr,[$tempWaybillCode])) : $tempWaybillCode;
                            $ret = $order->update([
                                'template_id'         => $template['id'],
                                'wp_code'             => $template['wp_code'],
                                'parent_waybill_code' => $item['parent_waybill_code'] ?? '',
                                'waybill_code'        => isset($order->waybill_code) && $expressNoArr ? implode(',', $expressNoArr) : $tempWaybillCode,
                                'recycled_at'         => null,
                            ]);
                            if (!$ret) {
                                throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
        //                        throw new \Exception('订单更新面单信息失败！');
                            }

                            $printData[] = PrintDataService::templateData($sender['sender_address'][$order->id], $order, $template,
                            $item, [], true , $key+1 ,$packageNum);

                            $printDataItems = '';
                            if ($template['auth_source'] != Waybill::AUTH_SOURCE_JD) {
                                $printDataItems = json_encode($printData[0]['contents'][1]['data']['printNextItemBeans'] ?? '');
                            }

                            $history = WaybillHistory::create([
                                'user_id'             => $userId,
                                'shop_id'             => $shopId,
                                'order_id'            => $order->id,
                                'order_no'            => $order['order_no'] ?? '',
                                'template_id'         => $template['id'],
                                'auth_source'         => $template['auth_source'],
                                'source'              => $company['source'] ?? '',
                                'source_shopid'       => $company['source_shopid'] ?? '',
                                'parent_waybill_code' => $item['parent_waybill_code'],
                                'waybill_code' => $item['waybill_code'],
                                'wp_code' => $template['wp_code'],
                                'print_data' =>  in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS]) ? json_encode($item) : array_get($item, 'print_data'),
                                'receiver_province' => $order->receiver_province,
                                'receiver_city' => $order->receiver_city,
                                'receiver_district' => $order->receiver_district,
                                'receiver_name' => $order->receiver_name,
                                'receiver_phone' => $order->receiver_phone,
                                'receiver_address' => $order->receiver_address,
                                'print_data_items' => $printDataItems,
                                'batch_no'         => $batchNo,
                            ]);
                            PrintRecord::create([
                                'user_id'           => $userId,
                                'shop_id'           => $shopId,
                                'order_id'          => $order->id,
                                'history_id'        => $history->id,
                                'order_no'          => $order['order_no'] ?? '',
                                'waybill_code'      => $item['waybill_code'],
                                'wp_code'           => $template['wp_code'],
                                'receiver_province' => $order->receiver_province,
                                'receiver_city'     => $order->receiver_city,
                                'receiver_district' => $order->receiver_district,
                                'receiver_town'     => $order->receiver_town,
                                'receiver_address'  => $order->receiver_address,
                                'receiver_name'     => $order->receiver_name,
                                'receiver_zip'      => $order->receiver_zip,
                                'receiver_phone'    => $order->receiver_phone,
                                'buyer_remark'      => $order->buyer_message,
                                'batch_no'          => $batchNo,
                                'to_shop_id' => $order['shop_id'],
                                'template_id' => $template['id'],
                                'template_name' => $template['name'],
                            ]);


                        }
                    }else {
                        //$failedOrders[] = $packages[$order['id']][0];
                        //取号错误信息
                        $obj       = new \stdClass();
                        $obj->text = '取号错误';
                        $obj->tid  = $order['tid'] ?? null;
                        $obj->name  = $order['receiver_name'] ?? null;
                        if (Waybill::AUTH_SOURCE_DY == $template['auth_source']) {
                            $obj->info = $package[0]['data']['err_infos'][0]['err_msg'] ?? "未知错误";
                        } else {
                            $obj->info = $package[0];
                        }
                        $failedOrders[] = json_encode($obj, 320); //失败数据
                    }
                }
            }
        }

	    return ['print_data' => $printData, 'failed_data' => $failedOrders];
    }



    /**
     * 运单回收
     * @param array $ids
     * @param int   $userId
     * @return bool
     * @throws \Exception
     */
    public static function recovery(array $ids, int $userId, int $shopId)
    {
        $orders = self::where([
            //'user_id'     => $userId,
            //'shop_id'     => $shopId,
//            'recycled_at' => null
        ])->whereIn('id', $ids)->get();
        if ($orders->isEmpty()) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
//            throw new \Exception('暂无可回收运单号订单!');
        }

        foreach ($orders as $order) {
            $template = Template::where('id', $order->template_id)->first();
            if (!$template) {
                throw new ApiException(ErrorConst::WAYBILL_TEMPLATE_NOT_FOUND);
//                throw new \Exception('订单' . $order->id . '电子面单模板不存在!');
            }
            $company  = $template->company;
            //判断是否为虚拟网点
            if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
                if (!Environment::isWxOrWxsp()&&in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY,
                        Waybill::AUTH_SOURCE_KS,
                    Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
                    $auth = Shop::find($shopId);
                    $auth->auth_source = $template['auth_source'];
                } else {
                    $auth = Waybill::where([
                        'owner_id'    => $template['owner_id'],
                        'auth_source' => $template['auth_source']
                    ])->orderBy('id', 'desc')->first();
                }
            } else {
                if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_TAOBAO,
                    Waybill::AUTH_SOURCE_JD, Waybill::AUTH_SOURCE_KS])) {
                    $auth = Shop::query()->where('identifier', $company->owner_id)->first();
                    $auth->auth_source = $template['auth_source'];
                } else {
                    $auth = Waybill::where([
                        'owner_id'    => $template['owner_id'],
                        'auth_source' => $template['auth_source']
                    ])->orderBy('id', 'desc')->first();
                }
            }
            if (!$auth) {
                throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
//                throw new \Exception('电子面单授权信息丢失!');
            }

	        if (empty($order->waybill_code)) {
		        continue;
            }
            $waybillCodes = explode(',',$order->waybill_code);
            foreach($waybillCodes as $waybill_code){
                $waybillService = WaybillServiceManager::init($template['auth_source'], $auth->access_token);
                $ret            = $waybillService->wayBillCancelDiscard($order->wp_code, $waybill_code);
                if (!$ret) {
                    Log::error('waybill recovery failed !', ['order' => $order->toArray()]);
                    throw new BusinessException('取消失败！');
                }
                $waybillHistory = WaybillHistory::where(['order_id' =>$order->id])
                    ->where(function ($query) use ($waybill_code) {
                        $query->orWhere('waybill_code', $waybill_code)
                            ->orWhere('parent_waybill_code', $waybill_code);
                    })
                    ->update(['waybill_status' => WaybillHistory::WAYBILL_RECOVERY_YES]);
                if (!$waybillHistory) {
                    Log::error('取号记录回收状态更新失败!', ['order_id' => $order->id]);
                }
            }
            $order->recycled_at  = date('Y-m-d H:i:s');
            $order->waybill_code = null;
            $order->printed_at = null;
            $order->print_status = 0;
            $order->wp_code = null;
            $order->save();
        }

        return true;
    }


    /**
     * 批量指定单号回收
     *
    */
    public static function batchRecovery(array $codes, int $userId, int $shopId)
    {
        try{
            $waybills = WaybillHistory::query()->where([
                'shop_id'        => $shopId,
                'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO
			])->whereIn('waybill_code', $codes)->get();

            if ($waybills->isEmpty()) {
                throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
//                throw new \Exception('暂无可回收运单号订单!');
            }
            foreach ($waybills as $waybill) {
                $template = Template::withTrashed()->where('id', $waybill->template_id)->first();
                if (!$template) {
                    throw new ApiException(ErrorConst::WAYBILL_TEMPLATE_NOT_FOUND);
//                    throw new \Exception('取号记录ID为' . $waybill->id . '电子面单模板不存在!');
                }
                $company  = $template->company;
                //判断是否为虚拟网点
                if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
                    if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                        $auth = Shop::find($shopId);
                        $auth->auth_source = Waybill::AUTH_SOURCE_DY;
                    } else {
                        $auth = Waybill::where([
                            'owner_id'    => $template['owner_id'],
                            'auth_source' => $template['auth_source']
                        ])->orderBy('id', 'desc')->first();
                    }
                } else {
                    if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                        $auth = Shop::query()->where('identifier', $company->owner_id)->first();
                        $auth->auth_source = Waybill::AUTH_SOURCE_DY;
                    } else {
                        $auth = Waybill::where([
                            'owner_id'    => $template['owner_id'],
                            'auth_source' => $template['auth_source']
                        ])->orderBy('id', 'desc')->first();
                    }
                }
                if (!$auth) {
                    throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
//                    throw new \Exception('电子面单授权信息丢失!');
                }
                $waybillService = WaybillServiceManager::init($template['auth_source'], $auth->access_token);
                $ret            = $waybillService->wayBillCancelDiscard($waybill->wp_code, $waybill->waybill_code,
                    $waybill->platform_waybill_id??'');
                if (!$ret) {
                    Log::error('waybill recovery failed !', [$waybill]);
                    throw new ApiException(ErrorConst::WAYBILL_INVALID_FAIL);
//                    throw new \Exception('取消失败！');
				}

                $orders = CustomizeOrder::where('id',$waybill->order_id)->get();
				foreach ($orders as $order){
                     //多个单号
                    $noArr = explode(',', $order->waybill_code);
                    if(in_array($waybill->waybill_code,$noArr)){
                        $key = array_search($waybill->waybill_code,$noArr);
                        unset($noArr[$key]);
                    }
                    $order->recycled_at  = date('Y-m-d H:i:s');
                    $order->waybill_code = !empty($noArr) ? implode(',',$noArr) : null;
                    $order->printed_at   = !empty($noArr)  ? $order->printed_at : null;
                    $order->print_status = !empty($noArr) ? Order::PRINT_STATUS_YES : Order::PRINT_STATUS_NO;
                    $order->wp_code = !empty($noArr) ? $order->wp_code : null;
                    $order->save();
                }
                $waybill->waybill_status = WaybillHistory::WAYBILL_RECOVERY_YES;
                if (!$waybill->save()) {
                    throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
//                    throw new \Exception('回收状态修改失败');
                }
			}
        }catch (\Exception $e) {
            Log::error($e->getMessage(), ['codes' => $codes, 'errorInfo' => $e->getTraceAsString()]);
            return false;
        }
        return true;
    }
    /**
     * 订单导入
     * @param string $fileName
     * @param string $path
     * @return mixed
     */
    public static function import(string $fileName, string $path)
    {
        $ext  = explode('.', $fileName);
        $ext  = $ext[count($ext) - 1];
        $file = self::saveInLocal($path, $ext);
        try {
            $excel = Excel::toArray(null, $file);
        } catch (\Exception $e) {
            Log::error('Excel load failed !', ['file_name' => $file, 'file' => $file]);
        }
        if (empty($excel)) {
            return [];
        }
        self::delInLocal($file);
        //删除表头
        unset($excel[0][0]);

        return $excel[0];
    }

    /**
     * 将文件上传到本地
     *
     * @param $url
     * @param $ext
     * @return string
     */
    private static function saveInLocal($url, $ext)
    {
        $fileName = substr(md5(rand(100, 999) . time()), 10) . '.' . $ext;
        $path     = '/tmp/excel_files';
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }
        $fullName = $path . '/' . $fileName;
        $file     = file_get_contents($url);
        $fp       = fopen($fullName, 'x');
        fwrite($fp, $file);
        fclose($fp);

        return $fullName;
    }

    /**
     * 删除本地文件
     *
     * @param $path
     * @return bool
     */
    private static function delInLocal($path)
    {
        return unlink($path);
    }

//    /**
//     * 顺丰地址识别
//     * @param string $address
//     * @return mixed
//     * @throws BusinessException
//     */
//    public static function sfAddress(string $address)
//    {
//        try {
//            $sfUrl    = 'https://www.sf-express.com/sf-service-owf-web/service/order/orderAddressSplit?lang=sc&region=cn&translate=';
//            $client   = new Client();
//            $response = $client->post($sfUrl, [
//                'json'    => [
//                    'address' => $address
//                ],
//                'verify'  => false,
//                'headers' => [
//                    'Content-type' => 'application/json',
//                    "Accept"       => "application/json"
//                ],
//                'timeout' => 0.5
//            ]);
//            $result   = json_decode($response->getBody()->getContents(), true);
//            if (!isset($result['result'])) {
//                throw new BusinessException('自动识别地址有误!');
//            }
//            return $result['result'];
//        } catch (\Exception $e) {
//            return "";
//        }
//    }

    /**
     * 省市区提取
     * @param string $address
     * @return array
     */
    public static function handleAddress($address = '广东省深圳市龙华新区大浪街道同胜科技大厦')
    {
        preg_match('/(.*?(省|自治区|北京市|天津市|上海市|重庆市))/', $address, $matches);
        if (count($matches) > 1) {
            $province = $matches[count($matches) - 2];
            $address  = str_replace($province, '', $address);
        }
        preg_match('/(.*?(市|自治州|地区|区划|县))/', $address, $matches);
        if (count($matches) > 1) {
            $city    = $matches[count($matches) - 2];
            $address = str_replace($city, '', $address);
        }
        if (empty($city)) {
            $city = isset($province) ? $province : '';
        }
        preg_match('/(.*?(区|县|镇|乡|街道))/', $address, $matches);
        if (count($matches) > 1) {
            $area    = $matches[count($matches) - 2];
            $address = str_replace($area, '', $address);
        }

        return [
            'receiver_province' => isset($province) ? $province : '',
            'receiver_city'     => isset($city) ? $city : '',
            'receiver_district' => isset($area) ? $area : '',
            'receiver_address'  => isset($address) ? $address : '',
        ];
    }

    public static function getOpenApiAddressInfo($platform, $waybillData, $waybill, $branchCode, $wpCode){
        $addressInfo = [];
        // 判断传的是branchCode还是地址拼接
        $tempBranch = explode(',', $branchCode);
        foreach ($waybillData as $key => $val) {
            if ($val['wp_code'] == $wpCode) {
                foreach ($val['branch_account_cols'] as $v=>$k) {
                    if (count($tempBranch) <= 1) {
                        if ($branchCode == $k['branch_code']) {
                            foreach ($k['shipp_addresses'] as $shipp) {
                                if ($waybill->auth_source == 1) {
                                    $addressInfo = [
                                        'province' => $shipp->province,
                                        'city' => $shipp->city,
                                        'district' => $shipp->district,
                                        'address' => $shipp->detail,
                                    ];
                                    break;
                                } else {
                                    $addressInfo = [
                                        'province' => $shipp['province'],
                                        'city' => $shipp['city'],
                                        'district' => $shipp['district'],
                                        'address' => $shipp['detail'],
                                    ];
                                    break;
                                }
                            }

                        }
                    } else {
                        foreach ($k['shipp_addresses'] as $shipp) {
                            if ($waybill->auth_source == 1 && $shipp->province == $tempBranch[0] && $shipp->city == $tempBranch[1] && $shipp->district == $tempBranch[2] && $shipp->detail == $tempBranch[3]) {
                                $addressInfo = [
                                    'province' => $shipp->province,
                                    'city'     => $shipp->city,
                                    'district' => $shipp->district,
                                    'address'  => $shipp->detail,
                                ];
                                break;
                            } elseif ($waybill->auth_source != 1 && $shipp['province'] == $tempBranch[0] && $shipp['city'] == $tempBranch[1] && $shipp['district'] == $tempBranch[2] && $shipp['detail'] == $tempBranch[3]) {
                                $addressInfo = [
                                    'province' => $shipp['province'],
                                    'city'     => $shipp['city'],
                                    'district' => $shipp['district'],
                                    'address'  => $shipp['detail'],
                                ];
                                break;
                            }
                        }

                    }
                }
            }
        }

        return $addressInfo;
    }

    public static function getPrintDataAndWaybillForOpenApi(int $userId, int $shopId, $order, $waybill, $waybillType, $platform, $appId, $branchCode)
    {
        // 发货地址 默认取订单里的
        $addressInfo = [
            'province' => $order['sender_province'],
            'city'     => $order['sender_city'],
            'district' => $order['sender_district'],
            'address'  => $order['sender_detailaddress']
        ];
        // 查询网点信息 传了网点地址取网点里的
        if (!empty($branchCode)) {
//            $authSource = $platform == Waybill::OPEN_API_DEFAULT ? Waybill::AUTH_SOURCE_DY : $waybill->auth_source;
            $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $waybillData    = $waybillService->waybillSubscriptionQuery();
//            $addressInfo = self::getOpenApiAddressInfo($platform, $waybillData, $waybill, $branchCode, $order->wp_code);
            $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $order->wp_code);
            if (empty($addressInfo)) {
                return throw_error_code_exception(StatusCode::COMPANY_ERROR);
            }
        }

        $addressInfo['mobile'] = $order['sender_phone'];
        $addressInfo['sender_name'] = $order['sender_name'];
        //是否有外部订单号并已经取过号了 有取过号返回老的单号
        if ($order->order_no) {
            $waybillHistory = WaybillHistory::query()->where(['shop_id'=>$shopId,'order_no'=>$order->order_no])->first();
            if (!empty($waybillHistory)) {
                //需更新params参数 否则无法打印
                $shop = Shop::query()->where('id', $shopId)->first();
                $appKey = config('socialite.dy.client_id');
                $secretKey = config('socialite.dy.client_secret');
                $client = (new DyClient($appKey, $secretKey))->setAccessToken($shop->access_token);
                $requestData = $client->buildRequestData([], 'logistics/getShopKey');
                $paramsStr = urldecode(http_build_query($requestData));
                $printData = json_decode($waybillHistory->print_data, true);
                if ($paramsStr) {
                    $printData['params'] = $paramsStr;
                }

                $result = [
                    'orderSn'           => $order->id,
                    'orderNo'           => $order->order_no ?? "",
                    'waybillCode'       => $waybillHistory->waybill_code,
                    'parentWaybillCode' => "",
                    'printData'         => json_encode($printData),
                    'code'              => 1,
                    'errorMsg'          => '',
                ];

                return $result;
            }
        }

        $result = self::newWaybillHistoriesForOpenApi(
            $userId,
            $shopId,
            $order,
            $addressInfo,
            $order['wp_code'],
            $waybill,
            $waybillType,
            $platform,
            $appId
        );

        return $result;
    }

    public static function newWaybillHistoriesForOpenApi($userId, $shopId, $order, $sender, $wpCode, $waybill, $waybillType, $platform, $appId)
    {
        // 查询标准模板
        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        $redisKey = $authSource . $wpCode;
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $waybillTemp    = Cache::remember($redisKey, 10, function () use ($waybillService,$wpCode,$waybill){
            return $waybillService->getCloudPrintStdTemplates($wpCode);
        });
        // 获取运单号
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $packages       = $waybillService->assemWaybillPackagesForOpenApi($platform, $sender, [$order], $wpCode, $waybillType, $waybillTemp, $packageNum = 1);

//        Log::info('packages', [$packages]);
        //错误信息
        if (!is_string($packages[$order['id']][0]) && is_array($packages[$order['id']][0]) && (isset($packages[$order['id']][0]['err_no']) ? $packages[$order['id']][0]['err_no'] == 0 : true)
            && (($platform == Waybill::OPEN_API_DEFAULT && config('app.platform') == 'dy') ? (!isset($packages[$order['id']][0]['data']['ebill_infos'])) : true)) {
            $ret = $order->update([
                'wp_code'             => $wpCode,
                'parent_waybill_code' => $packages[$order['id']][0]['parent_waybill_code'] ?? "",
                'waybill_code'        => $packages[$order['id']][0]['waybill_code'],
                'print_status'        => Order::PRINT_STATUS_YES,
                'printed_at'          => date('Y-m-d H:i:s', time())
            ]);
            if (!$ret) {
                //throw new \Exception('订单更新面单信息失败！');
                Log::error('[对外接口-自由打印] 订单更新面单信息失败',[
                    'order_id' => $order->id,
                    'wp_code'  => $wpCode,
                    'parent_waybill_code' => $packages[$order['id']][0]['parent_waybill_code'],
                    'waybill_code'        => $packages[$order['id']][0]['waybill_code'],
                ]);
            }

            $history = WaybillHistory::create([
                'user_id'             => $userId,
                'shop_id'             => $shopId,
                'order_id'            => $order->id,
                'order_no'            => $order['order_no'] ?? '',
                'auth_source'         => $authSource,
                'parent_waybill_code' => $packages[$order['id']][0]['parent_waybill_code'] ?? "",
                'waybill_code'        => $packages[$order['id']][0]['waybill_code'],
                'wp_code'             => $wpCode,
                'print_data'          => "{}",//   $packages[$order['id']][0]['print_data'],
                'receiver_province'   => $order->receiver_province,
                'receiver_city'       => $order->receiver_city,
                'receiver_district'   => $order->receiver_district,
                'receiver_name'       => $order->receiver_name,
                'receiver_phone'      => $order->receiver_phone,
                'receiver_address'    => $order->receiver_address,
                'app_id'              => $appId,
            ]);
//            PrintRecord::create([
//                'user_id'           => $userId,
//                'shop_id'           => $shopId,
//                'order_id'          => $order->id,
//                'history_id'        => $history->id,
//                'order_no'          => $order['order_no'] ?? '',
//                'waybill_code'      => $packages[$order['id']][0]['waybill_code'],
//                'wp_code'           => $wpCode,
//                'receiver_province' => $order->receiver_province,
//                'receiver_city'     => $order->receiver_city,
//                'receiver_district' => $order->receiver_district,
//                'receiver_town'     => $order->receiver_town,
//                'receiver_address'  => $order->receiver_address,
//                'receiver_name'     => $order->receiver_name,
//                'receiver_zip'      => $order->receiver_zip,
//                'receiver_phone'    => $order->receiver_phone,
//                'buyer_remark'      => $order->buyer_message,
//                'app_id'            => $appId,
//            ]);

            $printData = [
                'orderSn'           => $order->id,
                'orderNo'           => $order->order_no ?? "",
                'waybillCode'       => $packages[$order['id']][0]['waybill_code'],
                'parentWaybillCode' => $packages[$order['id']][0]['parent_waybill_code'] ?? "",
                'printData'         => $packages[$order['id']][0]['print_data'],
                'code'              => 1,
                'errorMsg'          => '',
            ];
        } else {
            //取号错误信息
            $printData = [
                'orderSn'           => $order->id,
                'orderNo'           => $order->order_no ?? "",
                'waybillCode'       => '',
                'parentWaybillCode' => '',
                'printData'         => '',
                'code'              => 0,
                'errorMsg'          => $packages[$order['id']][0] ?? "取号错误",
            ];
        }

        return $printData;
    }

    /**
     * 根据条件查询返回ID
     * @param $shopIds
     * @param $receiverPhone
     * @param $receiverName
     * @return void
     */
    public static function selectByReturnIdList($shopIds, $receiverPhone = null, $receiverName = null)
    {
        $query = CustomizeOrder::query()->whereIn('shop_id', $shopIds)->where(function($query) use($receiverName,$receiverPhone){
            if(isset($receiverPhone)){
                $query->orWhere('receiver_phone',$receiverPhone);
            }
            if(isset($receiverName)){
                $query->orWhere('receiver_name',$receiverName);
            }
        });
        return $query->get(['id'])->map(function($value){
            return $value['id'];
        })->toArray();

    }
    /**
     * @return null
     */
    public function getIsChildParentOrderAttribute()
    {
        return $this->is_child_parent_order;
    }


    /**
     * @param null $is_child_parent_order
     */
    public function setIsChildParentOrderAttribute($is_child_parent_order): void
    {
        $this->is_child_parent_order = $is_child_parent_order;
    }

    /**
     * @return null
     */
    public function getChildParentPackagesCountAttribute()
    {
        return $this->child_parent_packages_count;
    }

    /**
     * @param null $child_parent_packages_count
     */
    public function setChildParentPackagesCountAttribute($child_parent_packages_count): void
    {
        $this->child_parent_packages_count = $child_parent_packages_count;
    }

    /**
     * @return null
     */
    public function getOrderCipherInfoAttribute()
    {
        return $this->order_cipher_info;
    }

    /**
     * @param null $order_cipher_info
     */
    public function setOrderCipherInfoAttribute($order_cipher_info): void
    {
        $this->order_cipher_info = $order_cipher_info;
    }
}
