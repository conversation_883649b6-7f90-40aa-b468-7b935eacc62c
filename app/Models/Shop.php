<?php

namespace App\Models;

use App\Constants\PlatformConst;
use App\Constants\RedisKeyConst;
use App\Jobs\Orders\SyncOrdersJob;
use App\Services\Order\OrderServiceManager;
use App\Utils\WaybillUtil;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;

/**
 * @property  int $type 平台类型
 * @property  string $shop_name 店铺名称
 * @meethod static  query()
 */
class Shop extends Model implements WaybillAuth
{
    use SoftDeletes;
    const BRIEF_COLUMNS=['id','type','shop_name','name','identifier'];
    const AUTH_PUSH_ORDER_DAYS = 5;  //授权后订单同步的天数

    const CRITICAL_FIELDS = ['access_token', 'refresh_token', 'auth_user_id'];

    //授权状态
    const AUTH_STATUS_PENDING = 1;//未授权
    const AUTH_STATUS_SUCCESS = 2;//已授权
    const AUTH_STATUS_EXPIRE = 3;//已过期
    const AUTH_STATUS_ABNORMAL_EXPIRE = 4;//授权终止
    const AUTH_STATUS_CLEAN = 5;//授权清理

    const AUTH_STATUS_MAP = [
        self::AUTH_STATUS_PENDING,
        self::AUTH_STATUS_SUCCESS,
        self::AUTH_STATUS_EXPIRE,
        self::AUTH_STATUS_ABNORMAL_EXPIRE,
        self::AUTH_STATUS_CLEAN,
    ];

    // 同步开关
    const SYNC_SWITCH_OPEN = 1;
    const SYNC_SWITCH_CLOSE = 0;

    /**
     * 未知
     */
    const ROLE_TYPE_UNKNOWN = 0;

    /**
     * 商家
     */
    const ROLE_TYPE_MERCHANT = 1;

    /**
     * 厂家
     */
    const ROLE_TYPE_FACTORY = 2;

    /**
     * api商家
     */
    const ROLE_TYPE_API_MERCHANT = 3;

    /**
     * 平台代打商家
     */
    const ROLE_TYPE_PLATFORM_MERCHANT = 4;


    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'type',
        'identifier',
        'access_token',
        'refresh_token',
        'expire_at',
        'auth_at',
        'auth_status',
        'shop_name',
        'shop_identifier',
        'shop_logo',
        'name',
        'auth_user_id',
        'sync_switch',
        'login_count',
        'last_sync_page',
        'last_sync_at',
        'last_refund_sync_at',
        'last_goods_sync_at',
        'original_user_id',
        'inviter',
        'service_id',  //微信小店使用
        'specification_id', //微信小店使用
        'shop_code',
        'group_id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
    protected $hidden = [
        'refresh_token',
        'open_id',
    ];
    protected $appends = [
        'expire_days',
        'version',
        'version_desc',
    ];


    public static function firstById($id)
    {
//        $info = Cache::remember(sprintf(RedisKeyConst::SHOP_INFO_BY_ID, $id), 0.3, function () use ($id) {
        // 缓存 12 秒
        return self::where('id', $id)->first();
//        });
//        return $info;
    }

    public static function firstByUserId($userId)
    {
//        $info = Cache::remember(sprintf(RedisKeyConst::SHOP_INFO_BY_USER_ID, $userId), 0.3, function () use ($userId) {
        // 缓存 12 秒
        return self::query()->where('user_id', $userId)->first();
//        });
//        return $info;
    }

    public static function updateSyncSwitch($id, int $sync_switch)
    {
        return self::where('id', $id)->update(['sync_switch' => $sync_switch]);
    }

    public static function updateAuthStatus($id, int $authStatus)
    {
        return self::where('id', $id)->update(['auth_status' => $authStatus]);
    }

    public static function getListByIdentifiers(?array $ownerIdList)
    {
        $shops = null;
        if ($ownerIdList && sizeof($ownerIdList) > 0) {
            foreach ($ownerIdList as $index => $item) {
                $ownerIdList[$index] = (string)$item;
            }
            $shops = self::query()->whereIn('identifier', $ownerIdList)->get();
        }
        return $shops;
    }

    public function getExpireDaysAttribute()
    {
        return $this->userExtra ? $this->userExtra->expire_days : null;
    }

    public function getVersionAttribute()
    {
        return $this->userExtra ? $this->userExtra->version : null;
    }

    public function getVersionDescAttribute()
    {
        return $this->userExtra ? $this->userExtra->version_desc : null;
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function inviteUser()
    {
        return $this->belongsTo(User::class, 'inviter', 'id');
    }

    /**
     * 获取用户版本信息
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function userExtra()
    {
        return $this->hasOne('App\Models\UserExtra', 'shop_id', 'id');
    }

    public function shopExtra()
    {
        return $this->hasOne('App\Models\ShopExtra', 'shop_id', 'id');
    }

    public function userShopStatistic()
    {
        return $this->hasMany('App\Models\UserShopStatistic', 'shop_id', 'id');
    }


    /*
     *意见反馈
     *
     */
    public function feedback()
    {
        return $this->hasMany('App\Models\FeedBack', 'shop_id', 'id');
    }

    public function shopGroup()
    {
        return $this->hasOne('App\Models\ShopGroup', 'id', 'group_id');
    }


    /**
     * 今日统计数据
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function todayStatistic()
    {
        return $this->hasOne('App\Models\UserShopStatistic', 'shop_id', 'id')
            ->where('stat_at', date('Y-m-d'))
            ->where('interval', UserShopStatistic::INTERVAL_DAY);
    }

    /***
     * 汇总数据
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function totalStatistic()
    {
        return $this->hasOne('App\Models\UserShopStatistic', 'shop_id', 'id')
            ->where('stat_at', null)
            ->where('interval', UserShopStatistic::INTERVAL_IGNORE);
    }

    /**
     * 获取店铺关联列表
     * @param int $shopId
     * @return array
     */
    public static function getAllRelationShop(int $shopId)
    {
        $shopList = [];
        if (!$shopId) {
            return $shopList;
        }

        $shop = Shop::query()->findOrFail($shopId);
        //自己的店铺
        if ($shop->user) {
            $shopList = array_merge($shopList, collect($shop->user->shops())->toArray(), collect($shop->user->inviteShops)->toArray());
        }
        //邀请者的店铺
        if ($shop->inviteUser) {
            $shopList = array_merge($shopList, collect($shop->inviteUser->shops())->toArray(), collect($shop->inviteUser->inviteShops)->toArray());
        }

        //去重返回
        return collect($shopList)->unique()->values()->all();
    }

    /**
     * 手动同步拉取订单
     * @param int $userId
     * @param int $shopId
     * @param array $noList
     * @return bool
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Throwable
     */
    public static function syncPullOrders(int $userId, int $shopId, array $noList = array())
    {
        $shop = Shop::find($shopId);
        if ($shop->auth_status != Shop::AUTH_STATUS_SUCCESS) {
            return false;
        }
        //根据订单号同步
        if ($noList) {
            foreach ($noList as $no) {
                $orderService = OrderServiceManager::create(PlatformConst::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($shop->user_id);
                $orderService->setShop($shop);
                $order = $orderService->getOrderInfo($no);
                if ($order) {
                    Order::batchSave([$order], $userId, $shopId);
                }
            }

            return true;
        }

        //同步历史订单
        $beforeDay = strtotime("-10 day");
        $beginTime = $shop->last_sync_at ?: date("Y-m-d H:i:s", $beforeDay);
        if (strtotime($beginTime) < $beforeDay) {
            $beginTime = date("Y-m-d H:i:s", $beforeDay);
        }
        $endTime = date("Y-m-d H:i:s");
        $redis = redis('cache');

        $redisKey = 'sync_order_run:' . $shop->user_id;
        $bool = $redis->exists($redisKey);
        if ($bool) {
            return false;
        }
        dispatch((new SyncOrdersJob(
            $shop,
            $beginTime,
            $endTime
        )));

        return true;
    }

    /**
     * 更新最后一次授权时间 如果是最新的
     * @param $id
     * @param $time
     * @param string $field
     * <AUTHOR>
     */
    public static function updateLastSyncIfNewest($id, $time, string $field = 'last_sync_at')
    {
        $saveData = [
            $field => $time,
//            'last_sync_page' => $page,
        ];
        self::query()
            ->where('id', $id)
            ->where(function ($query) use ($time, $field) {
                $query->where($field, '<', $time);
                $query->orWhereNull($field);
            })
            ->update($saveData);
    }

    /**
     * 更新最后一次授权时间
     * @param $id
     * @param $time
     * @param bool $ifNewest 是否判断是最新的授权时间，则更新最新授权时间
     * <AUTHOR>
     */
    public static function updateLastSync($id, $time, bool $ifNewest = true)
    {
        $saveData = [
            'last_sync_at' => $time,
//            'last_sync_page' => $page,
        ];
        $query = self::query()->where('id', $id);
        if ($ifNewest) {
            $query->where(function ($query) use ($time) {
                $query->where('last_sync_at', '<', $time);
                $query->orWhereNull('last_sync_at');
            });
        }
        $res = $query->update($saveData);
        \Log::info('updateLastSync:' . $id, ['id' => $id, 'time' => $time, 'ifNewest' => $ifNewest, 'res' => $res]);
    }

    /**
     * 更新最后操作时间 如果是最新的
     * @param $id
     * @param $time
     * @param string $field
     * <AUTHOR>
     */
    public static function updateLastOperatedIfNewest($id, $time, string $field = 'last_operated_at')
    {
        $saveData = [
            $field => $time,
//            'last_sync_page' => $page,
        ];
        self::query()
            ->where('id', $id)
            ->where(function ($query) use ($time, $field) {
                $query->where($field, '<', $time);
                $query->orWhereNull($field);
            })
            ->update($saveData);
    }

    /**
     * 更新最后商品同步时间
     * @param $id
     * @param $time
     * <AUTHOR>
     */
    public static function updateLastGoodsSync($id, $time)
    {
        $saveData = [
            'last_goods_sync_at' => $time,
//            'last_sync_page' => $page,
        ];
        self::query()
            ->where('id', $id)
            ->where(function ($query) use ($time) {
                $query->where('last_goods_sync_at', '<', $time);
                $query->orWhereNull('last_goods_sync_at');
            })
            ->update($saveData);
    }

    /**
     * 更新最后一次授权时间 第一次
     * @param $id
     * @param $time
     * <AUTHOR>
     */
    public static function updateLastSyncByFirst($id, $time)
    {
        $saveData = [
            'last_sync_at' => $time,
//            'last_sync_page' => $page,
        ];
        self::query()
            ->where('id', $id)
            ->whereNull('last_sync_at')
            ->update($saveData);
    }

    public static function updateLastRefundSync($id, $time)
    {
        self::query()
            ->where('id', $id)
            ->where(function ($query) use ($time) {
                $query->where('last_refund_sync_at', '<', $time);
                $query->orWhereNull('last_refund_sync_at');
            })
            ->update(['last_refund_sync_at' => $time]);
    }

    /**
     * 换绑授权
     * @param $auth
     * @param $inviter
     * <AUTHOR>
     */
    public static function changeBind($auth, $inviter)
    {
        self::query()
            ->where('id', $auth->id)
            ->update([
                'inviter' => $inviter,
            ]);
        // todo 绑定记录
    }


    public static function shopsByIdentifier(array $ownerIdList)
    {
        return static::getListByIdentifiers($ownerIdList);
//        $shops = null;
//        if ($ownerIdList && sizeof($ownerIdList) > 0) {
//            $shops = self::query()->whereIn('identifier', $ownerIdList)->get();
//        }
//        return $shops;

    }

    public static function shopIdsByidentifier(array $ownerIdList, $defaultShopId = null)
    {
        $shopIds = [];
        if ($ownerIdList && sizeof($ownerIdList) > 0) {
            foreach ($ownerIdList as $index => $item) {
                $ownerIdList[$index] = (string)$item;
            }
            $shops = self::query()->whereIn('identifier', $ownerIdList)->get();
            $shopIds = collect($shops)->pluck('id')->toArray();
        } else {
            if ($defaultShopId) {
                $shopIds[] = $defaultShopId;
            }
        }
        return $shopIds;

    }

    public static function firstByShopName(string $shopName,?int $type=null)
    {
        if (empty($shopName)) {
            return null;
        }
        $query = Shop::query()->where('shop_name', $shopName);
        if ($type) {
            $query->where('type', $type);
        }

        return $query->first();
    }

    public static function firstByShopCode($shopCode)
    {
        if (empty($shopCode)) {
            return null;
        }
        return Shop::query()->where('shop_code', $shopCode)->first();
    }

    /**
     * 获取店铺当前选中的关联店铺
     * @param $shop_id
     * @return array
     */
    public static function getShopIdsByShopIdentifier($shop_id)
    {
        $shop_identifier = static::query()->where(['id' => $shop_id])->value('shop_identifier');
        $shop_identifier_arr = explode(',', $shop_identifier);
//        $shopArr = static::query()->whereIn('identifier', $shop_identifier_arr)->get()->toArray();
        $shopArr = Shop::getListByIdentifiers($shop_identifier_arr)->toArray();
        return array_pluck($shopArr, 'id');
    }

    /**
     * 根据关键字查找店铺
     * 如果指定了店铺ID，就在范围内查找
     * @param $keyword
     * @param $shopIds
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function searchByKeyword($keyword, $shopIds, $offset = null, $limit = null)
    {
        $query = static::query()->where('shop_name', 'like', '%' . $keyword . '%');

        if ($shopIds) {
            $query->whereIn('id', $shopIds);
        }
        if ($offset) {
            $query->offset($offset);
        }
        if ($limit) {
            $query->limit($limit);
        }
        $result = $query->get();
        return $result;

    }

    public static function firstByIdentifier(string $identifier,?int $type=null)
    {
        $query = static::query()->where('identifier', (string) $identifier);
        if ($type) {
            $query->where('type', $type);
        }
        return $query->first();
    }

    public static function firstByServiceId($serviceId)
    {
        return static::query()->where('service_id', $serviceId)->first();
    }

    /**
     * 授权是否OK
     * @return bool
     */
    public function isAuthOk(): bool
    {
        return $this->auth_status == Shop::AUTH_STATUS_SUCCESS && strtotime($this->expire_at) > time() && !empty($this->access_token);
    }

    public function getServiceId(): string
    {
        return $this->service_id;
    }

    public function getIdentifier(): string
    {
        return $this->identifier;
    }

    public function getShopName(): string
    {
        return $this->shop_name;
    }

    public function getSpecificationId(): string
    {
        return $this->specification_id;
    }

    public function getAccessToken(): string
    {
        return $this->access_token;
    }

    /**
     * 获取平台
     * @return string
     */
    public function getPlatform():string{
        return PlatformConst::PLATFORM_TYPE_MAP_REVERT[$this->type];
    }

    public function getPlatformType():int{
        return $this->type;
    }

    /**
     * 获取店铺对应的面单渠道来源
     * @return int
     */
    public function getAuthSource(): int
    {
        return PlatformConst::mapPlatformType2AuthSource($this->type);
    }

    public function getAuthSourceName():string{
        return WaybillUtil::getWaybillAuthName($this->getAuthSource());
    }


    /**
     * 是否是拼多多
     * @return bool
     */
    public function isPdd():bool{
        return $this->type == PlatformConst::PLATFORM_TYPE_PDD;
    }

    /**
     * @return bool 是否是抖音
     */
    public function isDy():bool{
        return $this->type == PlatformConst::PLATFORM_TYPE_DY;
    }

    public function isJd():bool{
        return $this->type == PlatformConst::PLATFORM_TYPE_JD;
    }

    public function isTaobao():bool{
        return $this->type == PlatformConst::PLATFORM_TYPE_TAOBAO;
    }

    /**
     * 店铺名称
     * @return string
     */
    public function getShopDescription(): string
    {
        //返回店铺描述 平台名称+店铺名
        return PlatformConst::getPlatformName($this->getPlatform()) . ':' . $this->shop_name;

    }

    public function isUser():bool{
        return $this->type == PlatformConst::PLATFORM_TYPE_USER;

    }

    public function isKs(): bool
    {
        return $this->type == PlatformConst::PLATFORM_TYPE_KS;
    }

    /**
     * 获取通过shop_binds表关联的店铺，其中当前店铺是子级店铺(o_shop_id)
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function parentShops()
    {
        return $this->belongsToMany(
            Shop::class,
            'shop_binds',
            'o_shop_id',
            'f_shop_id'
        );
    }
}
