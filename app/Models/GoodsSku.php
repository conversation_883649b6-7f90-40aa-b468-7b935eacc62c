<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodsSku extends Model
{
    const IS_ONSALE_NO  = 1; //下架
    const IS_ONSALE_YES = 2; //上架

    protected $fillable = [
        'goods_id',
        'user_id',
        'shop_id',
        'type',
        'sku_id',
        'sku_value',
        'outer_id',
        'outer_goods_id',
        'custom_sku_value',
        'sku_pic',
        'is_onsale',
    ];

    public function goods(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
       return $this->belongsTo(Goods::class, 'goods_id', 'id');
    }


    /**
     * 返回满足条件的SKU的GoodsId
     * @param array $shopIds
     * @param array $condition
     * @param string $keyword
     * @param bool $customTitleConfigured
     * @return array
     */
    public static function searchGoodsIds(array $shopIds, array $condition, string $keyword = '',bool $customTitleConfigured=false): array
    {
        $query = self::query()->whereIn('shop_id', $shopIds);
        if (!empty($condition)) {
            $query->where($condition);
        }
        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
                $query->where('sku_value', 'like', '%' . $keyword . '%')
                    ->orWhere('custom_sku_value', 'like', '%' . $keyword . '%')
                    ->orWhere('sku_id', $keyword)
                    ->orWhere('outer_id', $keyword);
            });
        }
        if($customTitleConfigured){
            $query->whereNotNull('custom_sku_value');
        }
        return $query->get(['goods_id'])->map(function ($sku) {
            return $sku['goods_id'];
        })->unique()->toArray();

    }
}
