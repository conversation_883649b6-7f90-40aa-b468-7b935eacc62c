<?php

namespace App\Models;
use App\Services\BusinessException;
use App\Services\Waybill\WaybillServiceManager;
use App\Traits\AuthSourceTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @property int $id
 * @property int $user_id
 * @property int $shop_id
 * @property string $shop_name
 * @property string $name
 * @property string $branch_name
 * @property int $company_id
 * @property string $identifier
 * @property string $wp_code
 * @property string $wp_name
 * @property int $balanceLimit
 * @property string $province
 * @property string $city
 * @property string $district
 * @property string $detail
 * @property string $action
 * @property string $street
 * @property ?int $auth_source
 * @property ?int $dest_shop_id
 * @property string $created_at
 * @property string $updated_at
 * @property string $deleted_at
 *
 */
class WaybillShareAction extends Model
{
    use AuthSourceTrait;
    const ACTION_STATUS_DEL    = 2; //删除
    const ACTION_STATUS_CLOSE  = 1; //冻结,停用
    const ACTION_STATUS_REGAIN = 0; //恢复
    const ACTION_STATUS_ADD    = 3; //追加
    const ACTION_STATUS_CREATE = 4; //新建
    const ACTION_STATUS_REMOVE = 5; //减少
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'shop_name',
        'name',
        'branch_name',
        'company_id',
        'identifier',
        'wp_code',
        'wp_name',
        'balanceLimit',
        'province',
        'city',
        'district',
        'detail',
        'action',
        'street',
        'auth_source',
        'dest_shop_id'
    ];

    protected $appends = [ 'auth_source_name'];



    public static function getShareCount($companyId)
    {
        //新建初始化数量
        $initCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_CREATE])->get(['balanceLimit']);
        //-1按不限量处理
        $balanceLimit = $initCount[0]['balanceLimit'];
        if($balanceLimit==-1){
            return -1;
        }
        //追加总数量
        $addShareCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_ADD])->get([DB::raw('sum(balanceLimit) as count')]);
        //减少总数量
        $removeCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_REMOVE])->get([DB::raw('sum(balanceLimit) as count')]);

        return $balanceLimit + $addShareCount[0]->count - $removeCount[0]->count;
    }

    public function sourceShop(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Shop::class,'shop_id','id');
    }
}
