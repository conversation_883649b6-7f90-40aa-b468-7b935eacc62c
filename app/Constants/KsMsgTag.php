<?php

namespace App\Constants;

class  KsMsgTag
{
    //订购消息
    const TAG_SERVICE_MARKET_ADD = 'kwaishop_service_market_addPaidOrder'; //服务市场新增已支付订单消息

    //订单消息类型
    const TAG_TRADE_ADDRESS_CHANGED = 'kwaishop_order_addressChange'; //买家收货信息变更消息

    //退款消息
    const TAG_REFUND_CREATED = 'kwaishop_refund_addRefund'; //买家发起售后申请消息

    const  TAG_ORDER_PAY_SUCCESS = "kwaishop_order_paySuccess";  //订单支付成功消息

    const TAG_ORDER_SUCCESS = "kwaishop_order_orderSuccess";

    const TAG_ORDER_FAIL = "kwaishop_order_orderFail";

    const TAG_FINISH_DELIVERY =  "kwaishop_order_finishDelivery";

    const TAG_ORDER_DELIVERING = "kwaishop_order_delivering";

    const TAG_REFUND_UPDATE = "kwaishop_aftersales_updateRefund";

    const TAG_REFUND_CREATE = "kwaishop_aftersales_addRefund";

    //需要处理的消息
    const NEED_MSG_TAG_ARR = [
        self::TAG_SERVICE_MARKET_ADD,
        self::TAG_TRADE_ADDRESS_CHANGED,
        self::TAG_REFUND_CREATED,
        self::TAG_ORDER_PAY_SUCCESS,
        self::TAG_ORDER_SUCCESS,
        self::TAG_REFUND_UPDATE,
        self::TAG_REFUND_CREATE,
        self::TAG_ORDER_FAIL,
        self::TAG_FINISH_DELIVERY,
        self::TAG_ORDER_DELIVERING,
    ];
}
