<?php

namespace App\Constants;

class  TbMsgTag
{
//退款标记创建消息
    const  TAOBAO_REFUND_REFUNDABLE_MARKED = "taobao_refund_RefundableMarked";
//退款标记取消消息
    const TAOBAO_REFUND_REFUNDABLE_CANCELED = "taobao_refund_RefundableCanceled";
//退款创建消息
    const TAOBAO_REFUND_CREATED = "taobao_refund_RefundCreated";
//退款关闭消息
    const TAOBAO_REFUND_CLOSED = "taobao_refund_RefundClosed";
//退款成功消息
    const TAOBAO_REFUND_SUCCESS = "taobao_refund_RefundSuccess";
//卖家同意退款协议消息
    const TAOBAO_REFUND_SELLER_AGREE_AGREEMENT = "taobao_refund_RefundSellerAgreeAgreement";
//买家退货给卖家消息
    const TAOBAO_REFUND_BUYER_RETURN_GOODS = "taobao_refund_RefundBuyerReturnGoods";

//淘宝收货信息更新
    const  TAOBAO_TRADE_TRADELOGISTICSADDRESSCHANGED = "taobao_trade_TradeLogisticsAddressChanged";
//交易成功
    const  TAOBAO_TRADE_TRADESUCCESS = "taobao_trade_TradeSuccess";
//交易的部分子订单发货消息
    const  TAOBAO_TRADE_TRADEPARTLYSELLERSHIP = "taobao_trade_TradePartlySellerShip";
//卖家发货
    const TAOBAO_TRADE_SELLERSHIP = "taobao_trade_TradeSellerShip";
//交易部分关闭
    const TAOBAO_TRADE_TRADEPARTLYCLOSE = "taobao_trade_TradeCloseAndModifyDetailOrder";
//交易关闭
    const TAOBAO_TRADE_TRADE_CLOSE = "taobao_trade_TradeClose";
//买家付完款，或万人团买家付完尾款
    const TAOBAO_TRADE_TRADEBUYERPAY = "taobao_trade_TradeBuyerPay";

    //需要处理的消息

}
