<?php

namespace App\Exceptions;

use App\Services\BusinessException;
use App\Utils\Environment;
use Exception;
use http\Env\Response;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Overtrue\Socialite\AuthorizeFailedException;
use Symfony\Component\Debug\Exception\FatalThrowableError;
use Symfony\Component\Debug\Exception\FlattenException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Laravel\Lumen\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpFoundation;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        HttpException::class,
        ValidationException::class,
        BusinessException::class,
        ApiException::class,
        OrderException::class,
        ModelNotFoundException::class,
        ErrorCodeException::class,
        AuthorizeFailedException::class,
        \GuzzleHttp\Exception\ConnectException::class,
        PrintException::class,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param \Exception $e
     * @return void
     * @throws Exception
     */
    public function report(Exception $e)
    {
        if (app()->bound('sentry') && $this->shouldReport($e)) {
            try {
                app('sentry')->captureException($e);
            } catch (\Exception $e) {
                if (app()->runningInConsole() && Environment::isJd()) {
                    throw $e;
                }
                Log::error('sentry error', ['error' => $e->getMessage()]);
            }
        }
        parent::report($e);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Exception $e
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function render($request, Exception $e)
    {
        // 是否记录错误日志
        $isLogError = true;
        $data = [];
        $code    = HttpFoundation\Response::HTTP_BAD_REQUEST;
        $error_code = 0;
        $message = '服务器内部错误';
        if ($e instanceof BusinessException) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } elseif ($e instanceof ApiException) {
            $code = $e->getErrorCode();
            $error_code = $e->getErrorCode();
            $message = $e->getMessage();
            $isLogError = false;
        }elseif ($e instanceof OrderException) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }elseif ($e instanceof PrintException) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }elseif ($e instanceof ModelNotFoundException) {
            //$code    = HttpFoundation\Response::HTTP_NOT_FOUND;
            switch ($e->getModel()){
                case 'App\Models\Fix\OrderItem':
                case 'App\Models\OrderItem':
                    $title = '子订单';
                    break;
                case 'App\Models\Fix\Order':
                case 'App\Models\Order':
                    $title = '订单';
                    break;
                case 'App\Models\Shop':
                    $title = '店铺';
                    break;
                default:
                    $title = $e->getModel();
                    break;
            }
            $message =  $title.'数据不存在';
        }elseif ($e instanceof NotFoundHttpException) {
            Log::error('NotFoundHttpException', ["uri"=>$request->getUri()]);
            $code    = HttpFoundation\Response::HTTP_NOT_FOUND;
            $message = '请求路由不存在';
        }elseif ($e instanceof MethodNotAllowedHttpException) {
            $code    = HttpFoundation\Response::HTTP_NOT_FOUND;
            $message = '请求的方法(method)不对';
        } elseif ($e instanceof ValidationException) {
            $code    = HttpFoundation\Response::HTTP_BAD_REQUEST;
            $message = $e->getMessage();
            if (method_exists($e, "errors")) {
                foreach ($e->errors() as $field => $error) {
                    $message = array_get($error, '0');
                    break;
                }
            }
        }elseif ($e instanceof NotFoundHttpException) {
            $code    = $e->getStatusCode();
            $message = 'Route Not Found!';
        }elseif ($e instanceof MethodNotAllowedHttpException) {
            $code    = $e->getStatusCode();
            $message = 'Method Not Allowed!';
        }elseif ($e instanceof ErrorCodeException) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $data = $e->getData();
        }  elseif ($e instanceof FatalThrowableError) {
            $code    = HttpFoundation\Response::HTTP_BAD_REQUEST;
            $message = $e->getMessage();
        }elseif ($e instanceof QueryException) {
            $code    = HttpFoundation\Response::HTTP_BAD_REQUEST;
            $message = '数据查询出错：'.REQ_ID;
        }elseif ($e instanceof Exception) {
            $code    = HttpFoundation\Response::HTTP_BAD_REQUEST;
            $message = $e->getMessage();
        }
//        if ($isLogError) {
//            \Log::error('Exception', [$e]);
//        }
        $response = [
            'meta' => [
                'req_id' => REQ_ID,
                'platform' => config('app.platform'),
                'app_name' => config('app.name'),
                'code' => $code,
                'message' => $message,
                'error_code' => $error_code,
            ]];
        if(!empty($data)) {
            $response['data'] = $data;
        }
        // 如果是 v3 的开放接口
        if (strpos($request->getPathInfo(),'/v3/') === 0) {
            $response = [
                'meta' => [
                    'traceId' => REQ_ID,
                    'req_id' => REQ_ID,
                    'code' => $code,
                    'message' => $message,
                    'platform' => config('app.platform'),
                    'app_name' => config('app.name'),
                ]];
        }
        if (strpos($request->getPathInfo(),'/v4/') === 0) {
            $response = [
                'meta' => [
                    'traceId' => REQ_ID,
                    'req_id' => REQ_ID,
                    'code' => $code,
                    'message' => $message,
                    'platform' => config('app.platform'),
                    'app_name' => config('app.name'),
                    'time' => date('Y-m-d H:i:s'),
                ]];
        }
        if (config('app.debug')) {
            $response['debug'] = [
                'message' => $e->getMessage(),
                'class' => get_class($e),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'trace' => explode(PHP_EOL, $e->getTraceAsString()),
            ];
        }
        return response()->json($response, 400);
    }
}
