<?php

namespace App\Jobs\PddMsg;

use App\Constants\PddMsgTag;

class PddMsgServiceManagerJob
{
    protected $msg;

    public function __construct(array $msg = [])
    {
        $this->msg = $msg;
    }

    protected $initMsgMap = [

        PddMsgTag::PDD_REFUND_CLOSED => PddOrderUpdateJob::class,
        PddMsgTag::PDD_REFUND_CREATED => PddOrderUpdateJob::class,
        PddMsgTag::PDD_REFUND_AGREE_AGREEMENT => PddOrderUpdateJob::class,
        PddMsgTag::PDD_REFUND_BUYER_RETURN_GOODS => PddOrderUpdateJob::class,
        PddMsgTag::PDD_TRAD_LOGISTICS_ADDRESS_CHANGED => PddOrderUpdateJob::class,
        PddMsgTag::PDD_TRAD_SUCCESS => PddOrderUpdateJob::class,
        PddMsgTag::PDD_TRAD_SELLER_SHIP => PddOrderUpdateJob::class,
        PddMsgTag::PDD_TRAD_CONFIRMED => PddOrderUpdateJob::class,


    ];

    protected function getInstance($msg)
    {
        if (isset($this->initMsgMap[$msg['event']])) {
            dispatch(new $this->initMsgMap[$msg['event']]($msg));
        }
    }

    public function handle()
    {
        $this->getInstance($this->msg);
    }
}
