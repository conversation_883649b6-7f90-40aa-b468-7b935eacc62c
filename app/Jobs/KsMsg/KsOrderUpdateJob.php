<?php


namespace App\Jobs\KsMsg;


use App\Constants\PlatformConst;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use App\Utils\ShopUtil;
use Illuminate\Support\Facades\Log;

/**
 * 快手订单更新消息,订阅订单的支付、退款等消息，收到消息以后获取订单详情，然后保存到数据库。
 */
class KsOrderUpdateJob
{
    protected $msg;

    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {
//        Log::info('订单更新', $this->msg);

        $msg = $this->msg;
        $data = json_decode($msg['info'], true);

//        $shop       = Shop::query()->where('identifier', $msg['userId'])->firstOrFail();
        if(Environment::isSyw()) {
            $identifier = $msg['openId'];
        }else{
            $identifier = $msg['userId'];
        }
        $shop = ShopUtil::firstByIdentifierWithCache($identifier);
        if (!$shop) {
            Log::error('店铺不存在', ['identifier' => $identifier]);
            return;
        }
        /**
         * @var UserExtra $userExtra
         */
        $userExtra = $shop->userExtra;
        if(!Environment::isSyw()&&(!$userExtra->isSenior()&&!$userExtra->isProfessional())){
            Log::info('非高级用户不处理订单更新');
            return;
        }

        if ($shop->auth_status == Shop::AUTH_STATUS_SUCCESS) {
            $orderService = OrderServiceManager::create(PlatformConst::KS);
            $orderService->setUserId($shop->user_id);
            $orderService->setShop($shop);

            $tid = $data['oid'];
            $order = $orderService->getOrderInfo($tid);
            if ($order) {
                Log::info('KS订单更新', ["tid"=>$tid]);
                Order::batchSave([$order], $shop->user_id, $shop->id);
            }else{
                Log::info('KS订单更新失败', ["tid"=>$tid]);
            }

        }else{
            Log::info('店铺未授权', ['identifier' => $identifier]);
        }
    }
}
