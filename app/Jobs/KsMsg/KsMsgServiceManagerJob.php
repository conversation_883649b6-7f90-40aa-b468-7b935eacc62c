<?php

namespace App\Jobs\KsMsg;

use App\Constants\KsMsgTag;
use App\Jobs\Job;
use Illuminate\Support\Facades\Log;

class KsMsgServiceManagerJob
{
    protected $msg;

    public function __construct(array $msg = [])
    {
        $this->msg = $msg;
    }

    protected $initMsgMap = [
        KsMsgTag::TAG_SERVICE_MARKET_ADD => KsServiceOrderJob::class,
        KsMsgTag::TAG_ORDER_SUCCESS  => KsOrderUpdateJob::class,
        KsMsgTag::TAG_TRADE_ADDRESS_CHANGED    => KsOrderUpdateJob::class,
        KsMsgTag::TAG_ORDER_FAIL    => KsOrderUpdateJob::class,
        KsMsgTag::TAG_FINISH_DELIVERY  => KsOrderUpdateJob::class,
        KsMsgTag::TAG_ORDER_DELIVERING  => KsOrderUpdateJob::class,
        KsMsgTag::TAG_REFUND_CREATE  => KsOrderUpdateJob::class,
        KsMsgTag::TAG_REFUND_CREATED => KsRefundCreateJob::class,
        KsMsgTag::TAG_ORDER_PAY_SUCCESS => KsOrderUpdateJob::class,
        KsMsgTag::TAG_REFUND_UPDATE => KsOrderUpdateJob::class,

    ];

    protected function getInstance($msg)
    {
        if (isset($this->initMsgMap[$msg['event']])) {
            dispatch(new $this->initMsgMap[$msg['event']]($msg));
        }else{
            Log::info('ks消息未处理', $msg);
        }
    }

    public function handle()
    {
        $this->getInstance($this->msg);
    }
}
