<?php

namespace App\Jobs\JdMsg;

use App\Constants\PlatformConst;
use App\Jobs\DoudianMsg\ShopMessageJob;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use App\Utils\LogUtil;
use Illuminate\Support\Facades\Log;

/**
 * JD更新订单的消息任务
 */
class JdSaveOrUpdateOrderMessageJob
{
    protected $msg;


    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {
        try {
            Log::info("JD更新订单消息任务开始", $this->msg);
            $orderService = OrderServiceManager::create(PlatformConst::JD);
            $identifier = array_get($this->msg, 'serviceId');
            $tid = self::getTid($this->msg);
            $shop = $this->getShop();
            if(!$shop){
                Log::warning("JD更新订单消息任务异常：店铺不存在或授权过期,serviceId=" .$identifier);
                return ;
            }
            $orderService->setShop($shop);
            $orders = $orderService->batchGetOrderInfo([['tid' => $tid]]);

            Log::info("获取JD订单", ["shopId" => $shop->id, "tid" => $tid,"size" => count($orders)]);
            if (!empty($orders)) {
                $shopId = $shop->id;
                Order::batchSave($orders, $shop->user_id, $shopId);
                Log::info("更新JD订单", ["shopId" => $shopId, "tid" => $tid]);
            } else {
                Log::warning("订单没获取到订单", ["shopId" => $shop->id, "tid" => $tid]);
            }
        }catch (\Throwable $e) {
            Log::error("获取订单失败", ["shopId" => $this->getShop()->id, "tid" => self::getTid($this->msg), "message"=>$e->getMessage(), "trace"=>$e->getTraceAsString()]);
        }
    }

    public function getShop(){
        $serviceId = array_get($this->msg, 'serviceId');
        return Shop::firstByServiceId($serviceId);
    }
    public static function getTid($data): string
    {
       return array_get($data, 'tid');
    }



}
