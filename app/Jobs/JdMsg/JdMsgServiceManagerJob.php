<?php

namespace App\Jobs\JdMsg;

use App\Constants\DoudianMsgTag;
use Illuminate\Support\Facades\Log;

/**
 * DY的消息JDB
 */
class JdMsgServiceManagerJob
{
    protected $msg;

    public function __construct(array $msg = [])
    {
        $this->msg = $msg;
    }

    protected $initMsgMap = [
        JdMsgTag::TAG_TRADE_CREATE => JdSaveOrUpdateOrderMessageJob::class,
        JdMsgTag::TAG_ORDER_CANCEL => JdSaveOrUpdateOrderMessageJob::class,
        JdMsgTag::TAG_ORDER_OUT => JdSaveOrUpdateOrderMessageJob::class,
        JdMsgTag::TAG_ORDER_FINISH => JdSaveOrUpdateOrderMessageJob::class,
        JdMsgTag::TAG_POP_ORDER_CHANGE => JdSaveOrUpdateOrderMessageJob::class,
        JdMsgTag::TAG_POP_ORDER_REMK_CHG => JdSaveOrUpdateOrderMessageJob::class,




    ];

    protected function getInstance($msg)
    {
        Log::info("JD消息处理", [$msg]);
        $tag = $msg['tag'] ?? 0;
        if (isset($this->initMsgMap[$tag])) {
            dispatch(new $this->initMsgMap[$tag]($msg));
        } else {
            Log::info("无需JD处理的消息", [$msg]);
        }
    }

    public function handle()
    {
        $this->getInstance($this->msg);
    }
}
