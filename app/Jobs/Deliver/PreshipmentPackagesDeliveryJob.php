<?php

namespace App\Jobs\Deliver;

use App\Services\Order\Request\DeliveryOrderItem;
use App\Services\Order\Request\PreshipmentDeliveryRequest;
use App\Services\Order\Request\PreshipmentWaybillItem;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Services\Order\OrderPreshipmentService;
use App\Utils\StrUtil;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * 店铺发货任务,对满足以下条件的包裹进行发货
 * 1. 预发货状态（预发货状态）为"1：已预约未发货",这个值是在预约发货的时候设置的
 * 2. 物流状态（logistic_status）是 "1：已发货无流转信息"，这个值是通过获取物流信息后设置的
 * 3. 包裹的来源是平台订单
 *
 */
class PreshipmentPackagesDeliveryJob extends Job
{

    /**
     * @var string $taskName 任务名称
     */
    public $taskName;
    /**
     * 操作店铺ID
     * @var int $operationShopId
     */
    public $operationShopId;

    /**
     * @var array 查询条件数组，每个里面是一个条件
     */
    public $wheres;

    /**
     * @param string $taskName
     * @param int $operationShopId
     * @param array $wheres
     */
    public function __construct(string $taskName,int $operationShopId, array  $wheres )
    {
        $this->operationShopId = $operationShopId;
        $this->wheres= $wheres;
        $this->taskName=$taskName;
    }


    public function handle(OrderPreshipmentService $orderPreshipmentService)
    {
        try {

//            $where[] = ['logistic_status', '=', Package::LOGISTIC_STATUS_NOT_DELIVERED];
            $builder = Package::query()->with(['packageOrders']);
            if(!empty($this->wheres)){
                foreach ($this->wheres as $where){
                    $builder->where($where);
                }
            }else{
                Log::error(StrUtil::format('{}执行失败 shopId={},查询条件为空', $this->taskName, $this->operationShopId));
                return ;
            }

            Log::info(StrUtil::format('{}开始执行 shopId={},查询语句={} 查询条件={}',$this->taskName, $this->operationShopId,$builder->toSql(),$builder->getBindings()));
            //找出来所有需要预发货的包裹
            $builder->chunkById(
                20,
                function (Collection $packages) use ($orderPreshipmentService) {
                    Log::info(StrUtil::format('{}开始执行 shopId={} 找到包裹数量={}',$this->taskName, $this->operationShopId, $packages->count()));
                    //对批量的$packages按to_shop_id进行分组
                    $groupedPackages = $packages->groupBy('shop_id');
                    foreach ($groupedPackages as $shopId => $shopPackages) {
                        $preshipmentDeliveryRequest = new PreshipmentDeliveryRequest();
                        $preshipmentDeliveryRequest->shopId = $shopId;
                        /**
                         * @var Package $package
                         */
                        foreach ($shopPackages as $package) {
                            $preshipmentWaybillItem=new PreshipmentWaybillItem();
                            $preshipmentWaybillItem->wpCode=$package->wp_code;
                            $waybillCode = $package->waybill_code;
                            $preshipmentWaybillItem->waybillCode= $waybillCode;

                            /**
                             * @var  PackageOrder $packageOrder
                             */
                            foreach ($package->packageOrders as $packageOrder) {
                                $fixOrder = $packageOrder->fixOrder;
                                //把关联的订单取出来，看看订单状态是否是待发货，如果不是，就不发货
                                if($fixOrder->order_status != Order::ORDER_STATUS_PAYMENT){
                                    Log::warning('订单状态不对，不发货', [$fixOrder->tid]);

                                    continue;
                                }
                                $deliveryOrderItem=new DeliveryOrderItem();
                                $deliveryOrderItem->orderId=$packageOrder->order_id;
                                $deliveryOrderItem->orderItemId=$packageOrder->order_item_id;
                                $deliveryOrderItem->num=$packageOrder->num;
                                $preshipmentWaybillItem->addItem($deliveryOrderItem);


                            }
                            if($preshipmentWaybillItem->isEmpty()){
                                Log::info(StrUtil::format('{}  没有需要发货的包裹',$waybillCode));
                                continue;
                            }
                            $preshipmentDeliveryRequest->addItem($preshipmentWaybillItem);
                        }
                        if($preshipmentDeliveryRequest->isEmpty()) {
                            Log::info(StrUtil::format('{}执行完成 shopId={}  没有需要发货的包裹',$this->taskName, $shopId));
                            continue;
                        }

                        $batchDeliveryResult = $orderPreshipmentService->preshipmentDeliver($preshipmentDeliveryRequest);
                        Log::info("发货结果", $batchDeliveryResult);
                    }
                }

            );
            \Log::info(StrUtil::format("{}执行完成 shopId={}",$this->taskName, $this->operationShopId));
            //找到满足条件的包裹，分页进行发货
//            $packageDao->buildQueryByCond($where)->chunkById(100, function (Collection $packages) use (
//                $tenant,
//                $deliverService, $appId
//            ) {
//                $packageIds = $packages->pluck('id')->toArray();
//                try {
//
//                    $packageDeliverRequest = new PackageDeliverRequest();
//                    $packageDeliverRequest->appId = $appId;
//                    $packageDeliverRequest->packageIds = $packageIds;
//
//                    $deliverService->packageDeliver($tenant, $packageDeliverRequest);
//                } catch (\Throwable $e) {
//                    Log::errorException('店铺发货任务失败', $e, $packageIds);
//                }
//            });
        } catch (Throwable $e) {
            \Log::error('店铺发货任务失败', [$e, $e->getTraceAsString()]);
        }
    }

    private function PreshipmentDeliveryRequest()
    {
    }
}
