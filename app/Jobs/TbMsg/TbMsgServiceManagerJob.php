<?php

namespace App\Jobs\TbMsg;

use App\Constants\KsMsgTag;
use App\Constants\TbMsgTag;

class TbMsgServiceManagerJob
{
    protected $msg;

    public function __construct(array $msg = [])
    {
        $this->msg = $msg;
    }

    protected $initMsgMap = [

        TbMsgTag::TAOBAO_REFUND_BUYER_RETURN_GOODS=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_CLOSED=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_CREATED=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_SELLER_AGREE_AGREEMENT=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_SUCCESS=>  TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADE_CLOSE=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_SELLERSHIP=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADESUCCESS=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADEPARTLYCLOSE=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADEPARTLYSELLERSHIP=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADELOGISTICSADDRESSCHANGED=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_TRADE_TRADEBUYERPAY=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_REFUNDABLE_CANCELED=>TbOrderUpdateJob::class,
        TbMsgTag::TAOBAO_REFUND_REFUNDABLE_MARKED=>TbOrderUpdateJob::class,



    ];

    protected function getInstance($msg)
    {
        if (isset($this->initMsgMap[$msg['event']])) {
            dispatch(new $this->initMsgMap[$msg['event']]($msg));
        }
    }

    public function handle()
    {
        $this->getInstance($this->msg);
    }
}
