<?php

namespace App\Jobs\WxNsg;

use App\Jobs\DoudianMsg\ShopMessageJob;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

/**
 * 抖音更新订单的消息任务
 */
class WxSaveOrUpdateOrderMessageJob extends  Job
{
    protected $msg;

    public function __construct(string $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {

        try {
            /**
             * {
             * "ToUserName": "gh_*",
             * "FromUserName": "OPENID",
             * "CreateTime": 1662480000,
             * "MsgType": "event",
             * "Event": "channels_ec_order_ext_info_update",
             * "order_info": {
             * "order_id": 3705115058471208928,
             * "type": 1
             * }
             * }
             */
            $simpleXMLElement = simplexml_load_string($this->msg,null,LIBXML_NOCDATA);
            $con = json_encode($simpleXMLElement);
            $orderArr = json_decode($con, true);
            Log::info("更新订单", ["orderArr" => $orderArr]);
            //小店UserName
            $authUserId = Arr::get($orderArr, 'ToUserName');
            $tid = Arr::get($orderArr, 'order_info.order_id');
            $shop = Shop::query()->where('auth_user_id', $authUserId)->first();
            if ($shop == null) {
                Log::error('订单消息异常：店铺不存在,authUserId=' . $authUserId);
                return;
            }
            if (!$shop->isAuthOk()) {
                Log::error('订单消息异常：店铺授权不正常,shop.id=' . $shop->id);
                return;
            }
            $orderService = OrderServiceManager::create(config('app.platform'));
            $orderService->setShop($shop);
            $order = $orderService->getOrderInfo($tid);
            if ($order) {
                $shopId = $shop->id;
                Order::batchSave([$order], $shop->user_id, $shopId);
                Log::info("更新订单成功", ["shopId" => $shopId, "tid" => $tid, "tag" => $this->msg['tag'] ?? '']);
            }
        }catch(\Throwable $throwable){
            Log::error("更新订单失败".$throwable->getMessage() );
        }

    }


}
