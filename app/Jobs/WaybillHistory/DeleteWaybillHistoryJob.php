<?php
namespace App\Jobs\WaybillHistory;

use App\Jobs\Job;
use App\Models\PrintRecord;
use App\Models\WaybillHistory;
use Illuminate\Support\Facades\Log;

/**
 * 删除
 */
class DeleteWaybillHistoryJob extends Job
{
	protected $waybillHistory;

	public function __construct($waybillHistory)
	{
		$this->waybillHistory = $waybillHistory;
	}

	public function handle()
	{
        $waybillHistory = $this->waybillHistory;

		$idArr = collect($waybillHistory)->pluck('id')->toArray();
        WaybillHistory::query()->whereIn('id', $idArr)->forceDelete();
//        PrintRecord::query()->whereIn('waybill_id', $idArr)->forceDelete();
        Log::info("删除取号记录:", $idArr);
    }
}
