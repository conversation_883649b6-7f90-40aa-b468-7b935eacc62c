<?php

namespace App\Jobs\Orders;

use App\Constants\PlatformConst;
use App\Jobs\Job;
use App\Models\AftersaleOrder;
use App\Models\Order;
use App\Models\OrderItem;

/**
 *
 */
class SyncSaveRefundOrders extends Job
{
    public $timeout = 240;
    private $orders;
    /**
     * @var int
     */
    private $shopId;

    /**
     * @param int $shopId
     * @param array $orders
     */
    public function __construct(int $shopId, array $orders)
    {
        $this->shopId = $shopId;
        $this->orders = $orders;
    }

    /**
     * 授权后订单处理
     * @return bool
     * @throws \Exception
     * @throws \Throwable
     */
    public function handle()
    {
        foreach ($this->orders as $index => $order) {
            \DB::transaction(function () use ($order) {
                // 创建售后记录
                AftersaleOrder::query()->updateOrCreate(['refund_id' => $order['refund_id']], [
                    'tid'       => $order['tid'],
                    'shop_id'   => $this->shopId,
                    'refund_id' => $order['refund_id'],
                    'refund_status' => $order['refund_status'],
                    'refund_reason' => $order['refund_reason'],
                    'refund_price'  => $order['refund_price'],
                    'refund_created_at' => $order['refund_created_at'],
                    'refund_updated_at' => $order['refund_updated_at']
                ]);

                // 设置主订单为退款订单
                if (config('app.platform') == PlatformConst::JD) {
                    //京东比较特殊 退款列表不返回oid
                    $orderItem = OrderItem::query()->where('tid', $order['tid'])->get();
                    foreach ($orderItem as $item) {
                        OrderItem::query()
                            ->where('id', $item['id'])
                            ->update(['refund_id' => $order['refund_id']]);
                        \Log::info('退款订单入库  id:' . $item['order_id']);
                    }
                } else {
                    $orderItem = OrderItem::query()->where('oid', $order['oid'])->first();
                    if (!empty($orderItem)) {
                        $id = $orderItem->order_id;
                        // 设置子订单为具体状态
                        OrderItem::query()
                            ->where('oid', $order['oid'] . '')
                            ->update(['refund_id' => $order['refund_id']]);

                        \Log::info('退款订单入库  id:' . $id);
                    }
                }

                //订单表退款状态同步修改
                //Order::query()->where('tid', $order['tid'])->update(['refund_status' => Order::REFUND_STATUS_YES]);
            });
        }
        return true;
    }
}
