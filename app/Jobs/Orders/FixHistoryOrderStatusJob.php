<?php

namespace App\Jobs\Orders;

use App\Jobs\Job;
use App\Models\Order;
use App\Services\Order\OrderServiceManager;

class FixHistoryOrderStatusJob extends Job
{
	protected $order;

	public function __construct(Order $order)
	{
		$this->order = $order;
	}

	/**
	 * @throws \Psr\SimpleCache\InvalidArgumentException
	 * @throws \Throwable
	 */
	public function handle()
	{
		$order = $this->order;

		$orderService = OrderServiceManager::create(config('app.platform'));
		$orderService->setUserId($order->user_id);
		$orderService->setShop($order->shop);
		$info = $orderService->getOrderInfo($order->tid);

//		Order::batchSave([$info], $order->user_id, $order->shop_id, true);
	}
}
