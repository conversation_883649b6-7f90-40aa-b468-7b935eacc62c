<?php

namespace App\Jobs\DoudianMsg;

use App\Jobs\Job;
use App\Models\Order;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 店铺消息
 */
abstract class ShopMessageJob extends Job
{
    /**
     * 具体的执行由各个类自己去实现
     * @param $shop
     * @param $data
     * @return mixed
     */
    abstract function  execute($shop,$data);

    public function handle()
    {
        $msg = $this->msg;

        $data = $msg['data'];
        if(is_string($data)){
            $data = json_decode($data);
        }

        try {
            $shop = $this->getShop($data);
            if ($shop==null){
                Log::warning('订单消息异常：店铺不存在或授权过期,identifier=' .self::getIdentifier($data));
                return ;
            }
            $this->execute($shop,$data);
        } catch (\Throwable $ex) {
            Log::error("订单消息异常:" . $ex->getMessage(), ["trace"=>$ex->getTraceAsString(),"msg"=>$this->msg]);
        }
    }

    /**
     * 获取店铺
     * @param $data
     * @return Shop
     * @throws \Exception
     */
    public function getShop($data):?Shop{
        $identifier = self::getIdentifier($data);
        $shop = Shop::firstByIdentifier($identifier);
        if($shop==null){
            Log::warning('订单消息异常：店铺不存在,identifier=' .$identifier);
            return null;
//           throw new \Exception('店铺不存在');
        }
        if(!$shop->isAuthOk()){
            Log::warning('订单消息异常：店铺授权不正常,shop.id=' . $shop->id);
            return null;
        }
        return $shop;
    }
    /**
     * 从消息里面获取订单TID
     * @param $data
     * @return string
     */
    public static  function getTid($data): string
    {
        $pId = $data->p_id;
        if (isset($pId)){
            return $pId;
        }elseif (isset($data->shop_order_id)){
            return $data->shop_order_id;
        }
        return  '';
    }

    /**
     * 从消息里获取店铺identifier
     * @param $data
     * @return void
     */
    public static function getIdentifier($data){

        return  $data->shop_id;
    }
}
