<?php

namespace App\Jobs\DoudianMsg;

use App\Constants\DoudianMsgTag;
use Illuminate\Support\Facades\Log;

/**
 * DY的消息JDB
 */
class DyMsgServiceManagerJob
{
    protected $msg;

    public function __construct(array $msg = [])
    {
        $this->msg = $msg;
    }

    protected $initMsgMap = [
        DoudianMsgTag::TAG_TRADE_CREATE => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_PAID => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_SELLER_SHIP => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_SUCCESS => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_CANCELED => OrderCancelJob::class,
        DoudianMsgTag::TAG_TRADE_ADDRESS_CHANGED => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_PARTLY_SELLER_SHIP => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_TRADE_LOGISTICS_CHANGED => OrderLogisticsChangedJob::class,
        DoudianMsgTag::TAG_REFUND_AGREED => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_REFUND_SUCCESS => DySaveOrUpdateOrderMessageJob::class,
        DoudianMsgTag::TAG_REFUND_CLOSED => RefundCloseJob::class,
        DoudianMsgTag::TAG_LOGISTICS_ORDER_TAG_PUSH => DySaveOrUpdateOrderMessageJob::class,
    ];

    protected function getInstance($msg)
    {
        $tag = $msg['tag'] ?? 0;
        if (isset($this->initMsgMap[$tag])) {
            dispatch(new $this->initMsgMap[$tag]($msg));
        } else {
            Log::info("无需抖音处理的消息", [$msg]);
        }
    }

    public function handle()
    {
        $this->getInstance($this->msg);
    }
}
