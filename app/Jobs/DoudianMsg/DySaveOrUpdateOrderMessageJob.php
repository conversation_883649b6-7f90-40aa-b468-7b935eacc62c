<?php

namespace App\Jobs\DoudianMsg;

use App\Constants\PlatformConst;
use App\Models\Order;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 抖音更新订单的消息任务
 */
class DySaveOrUpdateOrderMessageJob extends ShopMessageJob
{
    protected $msg;

    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function execute($shop, $data)
    {
//        return ;
        $tid =self::getTid($data);
        $orderService = OrderServiceManager::create(PlatformConst::DY);
        $orderService->setUserId($shop->user_id);
        $orderService->setShop($shop);
        $order = $orderService->getOrderInfo($tid);
        if ($order) {
            // @doc https://op.jinritemai.com/docs/message-docs/30/4168
            if (isset($data->order_tag_list)){
                foreach ($data->order_tag_list as $item) {
                    if ($item->tag_key == 'PriorityDelivery'){ // 用户催发货
                        $order['urge_shipment_at'] = date('Y-m-d H:i:s');
                    }
                }
            }
            $shopId = $shop->id;
            Order::batchSave([$order], $shop->user_id, $shopId);
            Log::info("更新DY订单",["shopId"=>$shopId,"tid"=>$tid,"tag"=>$this->msg['tag']??'']);
        }else{
            Log::info("DY订单不存在",["tid"=>$tid,"tag"=>$this->msg['tag']??'']);
        }
    }



}
