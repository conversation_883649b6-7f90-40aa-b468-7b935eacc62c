<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/3/15
 * Time: 16:10
 */
namespace App\Services\Export;

use App\Models\DeliveryRecord;
use App\Models\OrderItem;

class DeliveryTaskService extends AbstractExportTaskService
{
    public function __construct($userId, $shopId, $data)
    {
        parent::__construct($userId, $shopId, $data);
    }

    public function getHead()
    {
        return ['序号', '订单编号', '面单号', '物流公司', '商品', '发货时间', '发货结果'];
    }

    public function getExportData($fp)
    {
        $where = json_decode($this->data['condition'], true);
        $condition   = [];
        $offset      = 0;
        $limit       = $this->limit;
        $wpCode      = $where['wp_code'] ?? '';
        $keyword     = $where['keyword'] ?? '';
        $orderBy     = $where['order_by'] ?? '';
        $shopIds     = $where['shop_ids'];
        $condition[] = ['delivery_records.created_at', '>=', $where['begin_at']];
        $condition[] = ['delivery_records.created_at', '<=', $where['end_at']];

        do {
            list($ret, $count) = DeliveryRecord::search($condition, $shopIds,$keyword, $offset, $limit, $orderBy,$wpCode);

            //运单号相同合并
            $ret = collect($ret)->groupBy(['waybill_code']);
            foreach ($ret as $index => $item) {
                $orderNoArr = collect($item)->pluck('order_no')->toArray();
                $result = json_decode($item[0]['result'], true);
                $company = collect(config('express_company'))->where('wpCode', $item[0]['wp_code'])->values()->first();
                $companyName = !empty($company) ? $company['name'] : ($item['wp_code'] ?? '');

                // 拼接商品
                $goodsInfo = "";
                $orderItems = OrderItem::query()->whereIn('tid', $orderNoArr)->get(['goods_title','sku_value', 'goods_num']);
                foreach ($orderItems as $value) {
                    $goodsInfo .= $value['goods_title'] . ' ' . $value['sku_value'] . ' x' . $value['goods_num'] . "\n";
                }

                $list = [
                    'index'            => $offset + 1,
                    'order_no'         => implode(',', $orderNoArr) ?? '',
                    'waybill_code'     => $item[0]['waybill_code'] ?? '',
                    'wp_code'          => $companyName,
                    'goods_info'       => $goodsInfo,
                    'created_at'       => $item[0]['created_at'] ?? '',
                    'result'           => $result == 1 ? '发货成功' : '发货失败',
                ];
                $list = self::handleNumberNotFormat($list);
                fputcsv($fp, $list);
            }

            $offset += count($ret);
        } while($offset != $count);
    }
}
