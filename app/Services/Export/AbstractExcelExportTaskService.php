<?php
/**
 * Created by PhpStorm.
 * User: xu<PERSON><PERSON><PERSON>
 * Date: 2022/3/15
 * Time: 15:05
 */

namespace App\Services\Export;

use App\Constants\ExportConst;
use App\Models\ExportTask;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

abstract class AbstractExcelExportTaskService
{
    protected $userId;
    protected $shopId;
    protected $data;
    protected $limit = 1000;
    protected $name;
    /**
     * @var
     */
    protected $exportType;

    protected function __construct($userId, $shopId, $data, $name = null, $exportType=ExportConst::ASYNC_EXPORT)
    {
        $this->userId = $userId;
        $this->shopId = $shopId;
        $this->data = $data;
        $this->name = $name;
        $this->exportType = $exportType;
    }

    abstract public function getHeadMap($line);

    abstract public function getExportData();

    public function export()
    {
        try {
            Log::info('导出任务：' . $this->data['shop_id'],["exportType"=>$this->exportType]);
            // 防止内存溢出
            ini_set('memory_limit', '1024M');
            $fileName = time() . '.xlsx';
            if (ExportConst::isBrowserDownload($this->exportType)) {
                $downloadFileName = empty($this->name) ? $fileName : $this->name . '.xlsx';
                Log::info("浏览器直接导出",["downloadFileName"=>$downloadFileName] );
                return (new FastExcel($this->getExportData()))->download($downloadFileName,
                [$this, 'getHeadMap']);
            } else {
                // 更改任务状态
                ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_DOING]);
                // 打开文件流
                $path = base_path() . '/storage/app/export/';
                // 创建目录
                if (!file_exists($path)) {
                    mkdir($path, 0777, true);
                }
                $fullFileName=$path.$fileName;
                (new FastExcel($this->getExportData()))->export($fullFileName,[$this,'getHeadMap']);
                ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_SUCCESS, 'file_path' => $path . $fileName]);
                // 清理数据
                self::clean();
                return true;
            }
        } catch (\Throwable $ex) {
            Log::error("导出文件失败".$ex->getMessage(), $ex->getTrace());

            if(array_key_exists("id",$this->data)) {
                ExportTask::where('id', $this->data['id'])->update(['status' => ExportTask::STATUS_ERROR, 'memo' =>
                    $ex->getMessage()]);
            }
            return false;
        }
    }

    public static function clean()
    {
        //只保留7天数据
        $time = date("Y-m-d H:i:s", strtotime("-7 day"));
        $historyList = ExportTask::where([['created_at', '<=', $time]])->get();
        if (!empty($historyList)) {
            //删除文件
            foreach ($historyList as $item) {
                @unlink($item['file_path']);
            }
            //删除数据
            ExportTask::where([['created_at', '<=', $time]])->delete();
        }
    }
}
