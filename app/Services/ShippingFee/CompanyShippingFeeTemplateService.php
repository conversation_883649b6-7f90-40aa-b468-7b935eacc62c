<?php

namespace App\Services\ShippingFee;

use App\Constants\ErrorConst;
use App\Models\CompanyShippingFeeTemplate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class CompanyShippingFeeTemplateService
{

    /**
     * 获取网点和运费的绑定关系
     * @param int $shopId
     * @return Builder[]|Collection
     */
    public function findManyByShopId(int $shopId){
        return CompanyShippingFeeTemplate::query()->with(['shippingFeeTemplate'])->where('shop_id', $shopId)->get();
    }

    /**
     * 获取网点和运费的绑定关系
     * @param int $id
     * @return Builder|Builder[]|Collection|Model|null
     */
    public function findById(int $id){
        return CompanyShippingFeeTemplate::query()->with(['shippingFeeTemplate'])->find($id);
    }

    /**
     * 创建网点和运费的绑定关系
     *
     * @param CompanyShippingFeeTemplate $companyShippingFeeTemplate
     * @return bool
     */
    public function create(CompanyShippingFeeTemplate $companyShippingFeeTemplate): bool
    {
        return $companyShippingFeeTemplate->save();
    }

    /**
     * 删除网点和运费的绑定关系
     * @param $currentShopId
     * @param $companyShippingFeeTemplateId
     * @return bool
     * @throws \Exception
     */
    public function deleteById($currentShopId, $companyShippingFeeTemplateId): bool
    {
        /**
         * @var CompanyShippingFeeTemplate $companyShippingFeeTemplate
         */
        $companyShippingFeeTemplate = CompanyShippingFeeTemplate::query()->where('id', $companyShippingFeeTemplateId)->where('shop_id', $currentShopId)->first();
        if (empty($companyShippingFeeTemplate)) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,"未找到网点和运费的绑定关系");
        }
        $companyShippingFeeTemplate->shippingFeeTemplate()->delete();
        return $companyShippingFeeTemplate->delete()>0;
    }
}
