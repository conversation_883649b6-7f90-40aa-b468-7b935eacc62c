<?php

namespace App\Services\WaybillHistory;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\WaybillHistory;
use App\Traits\QueryHelperTrait;
use Illuminate\Support\Facades\DB;

/**
 * 取号记录面单对账
 */
class WaybillHistoryCompareService
{
    use QueryHelperTrait;
    /**
     * 比较面单数据生成总数
     * @param array $condition
     * @param array $allData
     * @return array
     * @throws ApiException
     */
    public  function compareWaybillCodeCount(array $condition, array $allData): array
    {

        \Log::info("面单对账", $condition);
        if (empty($allData)) {
            throw new ApiException(ErrorConst::FILE_ERROR_OR_DATA_EMPTY);
        }
        $waybillCodeArr = [];
        foreach ($allData as $item) {
            $waybillCodeArr[] = (string)$item[0];
        }
        $dataArr = array_chunk($waybillCodeArr, 1000);

        $matchTemp = []; //匹配到的数组
        $noMatchTemp = []; //未匹配到的数组
        $matchCancelTemp = []; //匹配到已回收
        $matchAvailableTemp = []; //匹配到未回收

        $repeatArr = []; //包含匹配和未匹配重复的单号

        $lineNum = 1;
        foreach ($dataArr as $data) {
            $query = WaybillHistory::query()->whereIn('waybill_code', $data);
            if (!empty($condition)) {
                $this->smartWhere( $query, $condition);
            }
            \Log::info("面对对账SQL=" . $query->toSql());
            $waybillCodeList = $query->get([
                'waybill_code',
                'waybill_status',
                'wp_code',
                'created_at',])
                ->toArray();

            $newWaybillCodeList = array_column($waybillCodeList, null, 'waybill_code');

            $num = 0;
            foreach ($data as $key => $item) {
                if (array_key_exists($item, $newWaybillCodeList)) {
                    //匹配到了
                    $matchTemp[] = array(
                        'lineNumber' => $key + 1 + $lineNum,
                        'waybillCode' => $newWaybillCodeList[$item]['waybill_code'],
                        'waybillStatus' => $newWaybillCodeList[$item]['waybill_status'],
                        'wpCode' => $newWaybillCodeList[$item]['wp_code'],
                        'createTime' => $newWaybillCodeList[$item]['created_at'],
                    );
                } else {
                    //未匹配到
                    $noMatchTemp[] = array(
                        'lineNumber' => $key + 1 + $lineNum,
                        'waybillCode' => $item,
                        'waybillStatus' => null,
                        'wpCode' => null,
                        'createTime' => null,
                    );
                }
                $num = $key + 1;
            }
            $lineNum += $num;
        }

        //得到重复的单号
        $arrTemp2 = array_unique($waybillCodeArr);
        $repeatTemp = array_unique(array_diff_assoc($waybillCodeArr, $arrTemp2));

        //从匹配到数据的数组里拿到重复的单号 以及未回收和已回收的数组
        foreach ($matchTemp as $key => $va) {
            //重复的单号
            foreach ($repeatTemp as $k => $v) {
                if ($v == $va['waybillCode']) {
                    $repeatArr[] = array(
                        'lineNumber' => $va['lineNumber'],
                        'waybillCode' => $va['waybillCode'],
                        'waybillStatus' => $va['waybillStatus'],
                        'wpCode' => $va['wpCode'],
                        'createTime' => $va['createTime'],
                    );
                }
            }

            //从匹配到数据的数组里拿到未回收和已回收的数组
            if ($va['waybillStatus'] == 1) {
                $matchCancelTemp[] = array(
                    'lineNumber' => $va['lineNumber'],
                    'waybillCode' => $va['waybillCode'],
                    'waybillStatus' => $va['waybillStatus'],
                    'wpCode' => $va['wpCode'],
                    'createTime' => $va['createTime'],
                );

            } else {
                $matchAvailableTemp[] = array(
                    'lineNumber' => $va['lineNumber'],
                    'waybillCode' => $va['waybillCode'],
                    'waybillStatus' => $va['waybillStatus'],
                    'wpCode' => $va['wpCode'],
                    'createTime' => $va['createTime'],
                );
            }
        }

        foreach ($noMatchTemp as $key => $va) {
            foreach ($repeatTemp as $k => $v) {
                if ($v == $va['waybillCode']) {
                    $repeatArr[] = array(
                        'lineNumber' => $va['lineNumber'],
                        'waybillCode' => $va['waybillCode'],
                        'waybillStatus' => $va['waybillStatus'],
                        'wpCode' => $va['wpCode'],
                        'createTime' => $va['createTime'],
                    );
                }
            }
        }

        $totalCount = count($allData);
        $repeatCount = count($repeatArr);
        $matchCount = count($matchTemp);
        $cancelCount = count($matchCancelTemp);
        //总数，重复数，总匹配，总回收
        return [$totalCount, $repeatCount, $matchCount, $cancelCount];
    }

    /**
     * 面单对账详细数据
     * @throws ApiException
     */
    public function compareWaybillData(array $condition, array $allData, string $typeId): array
    {
        \Log::info("面单对账", $condition);
        if (empty($allData)) {
            throw new ApiException(ErrorConst::FILE_ERROR_OR_DATA_EMPTY);
        }
        $waybillCodeArr = [];
        foreach ($allData as $item) {
            if (strlen(trim($item[0])) > 0) {
                $waybillCodeArr[] = (string)$item[0];
            }
        }
        \Log::info("共读取出" . count($waybillCodeArr) . "条数据");
        $dataArr = array_chunk($waybillCodeArr, 1000);

        $matchTemp = []; //匹配到的数组
        $noMatchTemp = []; //未匹配到的数组
        $matchCancelTemp = []; //匹配到已回收
        $matchAvailableTemp = []; //匹配到未回收

        $repeatArr = []; //包含匹配和未匹配重复的单号

        $lineNum = 1;
        foreach ($dataArr as $data) {
            $query = WaybillHistory::query()->whereIn('waybill_code', $data);
            if (!empty($condition)) {
              $this->smartWhere( $query, $condition);
            }
            \Log::info("面对对账SQL=" . $query->toSql());
            $query->from(DB::raw('`waybill_histories` FORCE INDEX (`waybill_histories_waybill_code_index`)'));
            $waybillCodeList = $query->get([
                'waybill_code',
                'waybill_status',
                'wp_code',
                'created_at',])
                ->toArray();

            $newWaybillCodeList = array_column($waybillCodeList, null, 'waybill_code');

            $num = 0;
            foreach ($data as $key => $item) {
                if (array_key_exists($item, $newWaybillCodeList)) {
                    //匹配到了
                    $matchTemp[] = array(
                        'lineNumber' => $key + 1 + $lineNum,
                        'waybillCode' => $newWaybillCodeList[$item]['waybill_code'],
                        'waybillStatus' => $newWaybillCodeList[$item]['waybill_status'],
                        'wpCode' => $newWaybillCodeList[$item]['wp_code'],
                        'createTime' => $newWaybillCodeList[$item]['created_at'],
                    );
                } else {
                    //未匹配到
                    $noMatchTemp[] = array(
                        'lineNumber' => $key + 1 + $lineNum,
                        'waybillCode' => $item,
                        'waybillStatus' => null,
                        'wpCode' => null,
                        'createTime' => null,
                    );
                }
                $num = $key + 1;
            }
            $lineNum += $num;
        }

        //得到重复的单号
        $arrTemp2 = array_unique($waybillCodeArr);
        $repeatTemp = array_unique(array_diff_assoc($waybillCodeArr, $arrTemp2));

        //从匹配到数据的数组里拿到重复的单号 以及未回收和已回收的数组
        foreach ($matchTemp as $key => $va) {
            //重复的单号
            foreach ($repeatTemp as $k => $v) {
                if ($v == $va['waybillCode']) {
                    $repeatArr[] = array(
                        'lineNumber' => $va['lineNumber'],
                        'waybillCode' => $va['waybillCode'],
                        'waybillStatus' => $va['waybillStatus'],
                        'wpCode' => $va['wpCode'],
                        'createTime' => $va['createTime'],
                    );
                }
            }

            //从匹配到数据的数组里拿到未回收和已回收的数组
            if ($va['waybillStatus'] == 1) {
                $matchCancelTemp[] = array(
                    'lineNumber' => $va['lineNumber'],
                    'waybillCode' => $va['waybillCode'],
                    'waybillStatus' => $va['waybillStatus'],
                    'wpCode' => $va['wpCode'],
                    'createTime' => $va['createTime'],
                );

            } else {
                $matchAvailableTemp[] = array(
                    'lineNumber' => $va['lineNumber'],
                    'waybillCode' => $va['waybillCode'],
                    'waybillStatus' => $va['waybillStatus'],
                    'wpCode' => $va['wpCode'],
                    'createTime' => $va['createTime'],
                );
            }
        }

        foreach ($noMatchTemp as $key => $va) {
            foreach ($repeatTemp as $k => $v) {
                if ($v == $va['waybillCode']) {
                    $repeatArr[] = array(
                        'lineNumber' => $va['lineNumber'],
                        'waybillCode' => $va['waybillCode'],
                        'waybillStatus' => $va['waybillStatus'],
                        'wpCode' => $va['wpCode'],
                        'createTime' => $va['createTime'],
                    );
                }
            }
        }

        switch ($typeId) {
            case '0':
                $matchMergeArr = array_merge($matchTemp, $noMatchTemp);
                $lineNumber = array_column($matchMergeArr, 'lineNumber');
                array_multisort($lineNumber, SORT_ASC, $matchMergeArr);
                $matchArr = $matchMergeArr;
                break;
            case '1':
                $matchArr = $matchAvailableTemp;
                break;
            case '2':
                $matchArr = $matchCancelTemp;
                break;
            case '3':
                $matchArr = $noMatchTemp;
                break;
            case '4':
                $lineNumber = array_column($repeatArr, 'lineNumber');
                array_multisort($lineNumber, SORT_ASC, $repeatArr);
                $matchArr = $repeatArr;
                break;
            default:
                $matchMergeArr = array_merge($matchTemp, $noMatchTemp);
                $lineNumber = array_column($matchMergeArr, 'lineNumber');
                array_multisort($lineNumber, SORT_ASC, $matchMergeArr);
                $matchArr = $matchMergeArr;
                break;
        }

        return $matchArr;
    }
}
