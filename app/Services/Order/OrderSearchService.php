<?php

namespace App\Services\Order;

use App\Models\Order;
use App\Models\Package;
use App\Models\QueryArea;
use App\Models\Shop;
use App\Utils\OrderUtil;

class OrderSearchService
{

    /**
     * 一键查询处理
     * @param        $query
     * @param  string  $search
     * @param $selectItem
     * @param $areaId
     * @param $sellerMemo
     * @param $checkedMsg
     * @param $goodsInclude
     * @param $goods
     * @param $userIds
     * @param $shopIds
     * @param  array  $accurateWhere
     * @param  int  $includeOrNot
     * @param  string  $customPrintContent
     * @param  string  $customGroup
     * @param  array  $tidList
     * @return mixed
     */
    public  function handleSearch(
        $query,
        $search,
        $selectItem,
        $areaId,
        $sellerMemo,
        $checkedMsg,
        $goodsInclude,
        $goods,
        $userIds,
        $shopIds,
        &$accurateWhere = [],
        $includeOrNot = 1,
        $customPrintContent = '',
        $customGroup = '',
        $tidList = []
    ) {
        $goods_include = \request()->get('goods_include', '');
        $sku_include = \request()->get('sku_include', '');

        $shops = Shop::query()->whereIn('id', $shopIds)->get();

        if ($selectItem > 0 && ($search || ($goods_include || $sku_include))) {
            switch ($selectItem) {
                case '1':
                    $tid = $search;
                    $accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.tid', $tid]
                    ];
                    break;
                case '2':
                    $query->where('express_no', $search);
                    break;
                case '3':
                    $tidArr = [];
                    foreach ($shops as $shop) {
                        $orderService = OrderServiceManager::create($shop->getPlatform());
                        $orderService->setShop($shop);
                        $orderService->setAccessToken($shop->access_token);
                        $tidArr = array_merge($tidArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $tidArr);
                    //}
                    break;
                case '4':
                    $tidArr = [];
                    foreach ($shops as $shop) {
                        $orderService = OrderServiceManager::create($shop->getPlatform());
                        $orderService->setShop($shop);
                        $orderService->setAccessToken($shop->access_token);
                        $tidArr = array_merge($tidArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $tidArr);
                    //}
                    break;
                case '5': // 商品包含
                    if (!empty($goods_include)) {
                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $shopIds, $accurateWhere,
                            $isInclude);
                    }
                    if (!empty($sku_include)) {
                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $shopIds, $accurateWhere,
                            $isInclude);
                    }
                    break;
                case '6': // 商品不包含
                    if (!empty($goods_include)) {
                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $shopIds, $accurateWhere,
                            $isInclude);
                    }
                    if (!empty($sku_include)) {
                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $shopIds, $accurateWhere,
                            $isInclude);
                    }

                    break;
                case '7':
                    $query->whereHas('orderItem', function ($query) use ($search) {
                        $query->where('outer_sku_iid', $search);
                    });
                    break;
                case '8':
                    //规格名称，颜色尺寸
                    $sortArr = explode(',', $search);
                    $search1 = $sortArr[0];
                    $search2 = $sortArr[1];
                    $condition = $includeOrNot == 1 ? 'like' : 'not like';

                    $query->whereHas('orderItem', function ($query) use ($search1, $condition) {
                        $query->where('sku_value', $condition, '%'.$search1.'%');
                    });

                    $query->whereHas('orderItem', function ($query) use ($search2, $condition) {
                        $query->where('sku_value', $condition, '%'.$search2.'%');
                    });
                    break;
                case '9':
                    $query->where('seller_memo', 'like', "%{$search}%");
                    break;
                case '13':
                    $query->where('buyer_message', 'like', "%{$search}%");
                    break;
                case '14':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.num_iid', $arr]
                    ];
                    break;
                case '15':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.sku_id', $arr]
                    ];
                    break;
                case '18':
                    // 批次号
                    $arr = explode('-', $search);
                    $tidsArray = Package::query()->where('batch_no', 'like', $arr[0].'%')
                        ->select('tids')->get()->pluck('tids')->toArray();
                    $tidArr = [];
                    foreach ($tidsArray as $index => $item) {
                        $tidArr = array_merge($tidArr, explode(',', $item));
                    }
                    $query->whereIn('orders.tid', $tidArr);
                    break;
                case '19':
                    // 昵称
                    $query->where('orders.buyer_nick', $search);
                    break;
                default:
                    break;
            }
        } else {
            $tidArr = [];
            foreach ($shops as $shop) {
                $orderService = OrderServiceManager::create($shop->getPlatform());
                $orderService->setShop($shop);
                $orderService->setAccessToken($shop->access_token);
                $tidArr = array_merge($tidArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                if (isPhoneNumber($search)) {
                    $tidArr = array_merge($tidArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                }
            }
            //关键词查询
            if ($search) {
                $packageOrderIdArr = Package::query()
                    ->leftJoin('package_orders', 'package_id', '=', 'packages.id')
                    ->where('waybill_code', $search)
                    ->get(['order_id'])
                    ->pluck('order_id')
                    ->toArray();

                $function = function ($query) use ($search, $packageOrderIdArr, $tidArr) {
                    $tid = $search;

                    $query->where('orders.tid', $tid)
                        ->orWhere('express_no', $search);

                    if (in_array(config('app.platform'), [PlatformConst::KS, PlatformConst::JD])) {
                        $query->orWhere('buyer_nick', $search);
                    }
                    if (!empty($packageOrderIdArr)) {
                        $query->orWhereIn('orders.id', $packageOrderIdArr);
                    }
                    if (!empty($tidArr)) {
                        $query->orWhereIn('orders.id', $tidArr);
                    }
                };
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => $function,
                ];
            }
        }

        //地域限制
        if ($areaId) {
            $area = QueryArea::query()->findOrFail($areaId);
            $districtArr = explode(',', $area->district_str);
            $customDistrictArr = explode(',', $area->custom_district_str);
            $districtArr = array_merge($districtArr, $customDistrictArr);
            if ($area->include == '1') {
                $query->whereIn('district_code', $districtArr);
            } else {
                $query->whereNotIn('district_code', $districtArr);
            }
        }
        //卖家备注
        if ($sellerMemo) {
            //			$query->where('seller_memo', json_encode([$sellerMemo]));  //todo bug need fix
        }

        //留言、备注
        if ((int) $checkedMsg > 0) {
            switch ($checkedMsg) {
                case '1':
                    $query->where('buyer_message', '<>', '')->where('seller_memo', '[]');
                    break;
                case '2':
                    $query->where('buyer_message', '')->where('seller_memo', '<>', '[]');
                    break;
                case '3':
                    $query->where('buyer_message', '<>', '')->where('seller_memo', '<>', '[]');
                    break;
                case '4':
                    $query->where('buyer_message', '')->where('seller_memo', '[]');
                    break;
                case '5':
                    $query->where(function ($query) {
                        $query->orWhere('buyer_message', '<>', '')
                            ->orWhere('seller_memo', '<>', '[]');
                    });
                    break;
                default:
                    break;
            }
        }

        //商品筛选
        foreach ($goods as $k => $g) {
            if (!$g) {
                unset($goods[$k]); //去除空值
            }
        }
        $goods_id = collect($goods)->pluck('goods_id')->toArray();
        $goods_sku = collect($goods)->pluck('goods_sku')->toArray();
        if (count($goods) > 0 && (array_filter($goods_id) || array_filter($goods_sku))) {
            $query->whereHas('orderItem', function ($query) use ($goodsInclude, $goods, $goods_id, $goods_sku) {
                if ($goodsInclude == '1') { //包含
                    $query->whereIn('goods_title', $goods_id)
                        ->orWhereIn('sku_value', $goods_sku)
                        ->orWhereIn('num_iid', $goods_id);
                } else {
                    if ($goodsInclude == '3') { //精确
                        $query->whereIn('goods_title', $goods_id)
                            ->orWhereIn('sku_value', $goods_sku)
                            ->orWhereIn('num_iid', $goods_id);
                    } else { //不包含
                        $query->whereNotIn('goods_title', $goods_id)->orWhereNotIn('num_iid',
                            $goods_id)->orWhereNotIn('sku_value', $goods_sku);
                    }
                }
            });
        }
        if (!empty($customPrintContent)) {
            $query->where('custom_print_content', $customPrintContent);
        }
        if (!empty($customGroup)) {
            $query->where('custom_group', $customGroup);
        }
        if (!empty($tidList)) {
            $tidList = OrderUtil::batchAppendOrderSuffix($tidList);
            $accurateWhere[] = [
                'func' => 'whereIn',
                'args' => ['orders.tid', $tidList]
            ];
        }

        return $query;
    }
}
