<?php

namespace App\Services\Order;

use App\Constants\ErrorConst;
use App\Constants\OrderIndexTabConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\AbnormalOrder;
use App\Models\Fix\Order;
use App\Models\Fix\Shop;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\OrderItemExtra;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\PtLogistics;
use App\Models\QueryArea;
use App\Models\ShopBind;
use App\Models\User;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Order\Request\OrderSearchRequest;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\StrUtil;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * 订单查询的构造器
 */
class OrderQueryBuilder
{
    /**
     * 只显示合并订单
     * @var bool
     */
    private $onlyShowMergeOrder = false;
    /**
     * 只显示非合并订单
     * @var bool
     */
    private $notShowMergeOrder = false;
    /**
     * 同一人多个地址
     * @var bool
     */
    private $handToShowMergeOrder = false;
    /**
     * 精确查询条件
     * @var array
     */
    private $accurateWhere = [];
    /**
     * @var array
     */
    private $groupWhere = [];
    /**
     * @var array
     */
    private $finalWhere = [];

    private $amountArr = [];
    /**
     * @var array|string|int
     */
    private $goodsNum = null;
    /**
     * @var array|string|int
     */
    private $ordersKind = null;
    /**
     * @var int
     */
    private $ordersNum = null;
    /**
     * @var array
     */
    private $factoryShopIds = [];
    private $sortArr = [];
    /**
     * @var string
     */
    private $sql = '';
    private $count = 0;
    /**
     * @var int
     */
    private $orderCount = 0;
    private $buyerCount = 0;

    /**
     * 用于构建普通的订单查询
     */
    public function buildPlainOrderQuery(OrderSearchRequest $orderSearchRequest, $relation = [], $joinOrderItem = false):
    Builder
    {
        $query = $this->newQuery($relation);
        if ($joinOrderItem) {
            $query->join('order_items', 'orders.id', 'order_items.order_id');
        }
        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        // 时间
        $this->buildByTime($query, $orderSearchRequest);
        // 打印状态
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        return $query;
    }

    /**
     * 用于构建普通的订单查询
     */
    public function buildShippedListOrderQuery(OrderSearchRequest $orderSearchRequest, $relation = [], $joinOrderItem = false): Builder
    {
        $query = $this->newQuery($relation);
        if ($joinOrderItem) {
            $query->join('order_items', 'orders.id', 'order_items.order_id');
        }
        $shops = Shop::query()->where('id', $orderSearchRequest->shopIds)->get();
        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        // 时间
        $this->buildByTime($query, $orderSearchRequest);
        // 打印状态
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        // 下拉查询
        $this->buildBySelectItem($query, $orderSearchRequest, $shops);
        // 留言备注
        $this->buildByRemarkType($query, $orderSearchRequest);

        $this->handleAccurateWhere($query, $this->accurateWhere);
        return $query;
    }

    /**
     * 用于构建已发货订单查询
     */
    public function buildShippedListV2OrderQuery(Builder $query, OrderSearchRequest $orderSearchRequest, $relation = [], $joinOrderItem = false): Builder
    {
        if ($joinOrderItem) {
            $query->join('order_items', 'orders.id', 'order_items.order_id');
        }
        $shops = Shop::query()->where('id', $orderSearchRequest->shopIds)->get();
        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        // 时间
        $this->buildByTime($query, $orderSearchRequest);
        // 打印状态
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        // 下拉查询
        $this->buildBySelectItem($query, $orderSearchRequest, $shops);
        // 留言备注
        $this->buildByRemarkType($query, $orderSearchRequest);
        // 区域筛选
        $this->buildAddressGroupId($query, $orderSearchRequest->addressGroupId);
        // 退款状态
        $this->buildByRefund($query, $orderSearchRequest);
        // 商品规格查询
        $this->buildSkuProperties($query, $orderSearchRequest);

        $this->handleAccurateWhere($query, $this->accurateWhere);

//        $finalWhereArr = $this->getWhereArrByGroup($orderSearchRequest);
//        $this->handleFinalWhere($queryFinally, $finalWhereArr);

//        $finalWhereArr = $this->getWhereArrByGroup($orderSearchRequest);
//        $count = $this->getCountByGroup('id', 'desc', $queryBase, $groupColumn, $finalWhereArr, $orderSearchRequest);
//        $orderIdArr = $this->getQueryByDisplayMerge($query, $orderSearchRequest, '$groupColumn', $orderSearchRequest->limit, $orderSearchRequest->offset);

        if (!empty($this->ordersNum)) {
            $whereArr = [];
//            $this->count = $this->ordersNum;
//            $orderIdArr = $this->getQueryByDisplayMerge($query, $orderSearchRequest, 'packages.waybill_code', 5000, 0);

            if (is_array($this->ordersNum)) {
//                    $whereArr[] = ['merge_orders_num', '>=', $this->ordersNum[0]];
//                    $whereArr[] = ['merge_orders_num', '<=', $this->ordersNum[1]];
                $query->having('merge_orders_num', '>=', $this->ordersNum[0]);
                $query->having('merge_orders_num', '<=', $this->ordersNum[1]);

            } else {
                $query->having('merge_orders_num', '=', $this->ordersNum);
//                    $whereArr[] = ['merge_orders_num', '=', $this->ordersNum];
            }
        }


        return $query;
    }

    public function buildDeliveryStatisticsQuery(OrderSearchRequest $orderSearchRequest): Builder{
        $condition=1;
        $orderSearchRequest->isShippedList=1;
        $ownerIdList = ShopBind::getValidIdentifierByRelation($orderSearchRequest->authShopId, $orderSearchRequest->ownerIdList, 1);
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $orderSearchRequest->shopIds = $shops->pluck('id')->toArray();
        Log::info('已发货统计查询条件', [$orderSearchRequest]);
        $query=OrderItem::query()->with(['customGoodsSkus']);
        $query->select('order_items.id');
        $query->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->leftJoin('order_extras', 'order_extras.order_id', '=', 'orders.id');

        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);
        // 时间
//        $this->buildByTime($query, $orderSearchRequest);
//        Log::info('订单查询条件2', [$query->toSql()]);
        // 下拉查询
        if(!empty($orderSearchRequest->search)){
            $query->join('package_orders', 'package_orders.order_item_id', '=', 'order_items.id');
            $query->where('package_orders.source_type',1);
            $query->join('packages', 'package_orders.package_id', '=', 'packages.id');
            $query->where('packages.source_type',1);
        }
        $this->buildBySelectItem($query, $orderSearchRequest, $shops);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);
        // 退款状态
//        $this->buildByRefund($query, $orderSearchRequest);
//        Log::info('订单查询条件4', [$query->toSql()]);
        // 区域筛选
        $this->buildAddressGroupId($query, $orderSearchRequest->addressGroupId);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);
        // tab标记
//        $this->buildByTabFlag($query, $orderSearchRequest);
//        Log::info('订单查询条件6', [$query->toSql()]);
        // 打印状态
//        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
//        Log::info('订单查询条件7', [$query->toSql()]);
        // 订单状态
//        $this->buildByOrderStatus($query, $orderSearchRequest->orderStatus, $orderSearchRequest->quickFilterValue);
//        Log::info('订单查询条件8', [$query->toSql()]);
        // 排序
        $orderSearchRequest->sortArr = $this->buildBySort($query, $orderSearchRequest->sort);
        // 留言备注
        $this->buildByRemarkType($query, $orderSearchRequest);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);
        $this->buildByRefundAndSubRefund($query, $orderSearchRequest);
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        $this->buildOrderTotalFee($query, $orderSearchRequest);
        $this->buildPayment($query, $orderSearchRequest);
        $this->buildGoodsNum($query, $orderSearchRequest);
        $this->buildSkuProperties($query, $orderSearchRequest);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);

        // 快速筛选
        $this->buildByQuickFilter($query, $orderSearchRequest->quickFilterValue,$orderSearchRequest->orderBizType);
        Log::info('订单查询条件'.$condition++, [$query->toSql()]);

//        $this->buildGoodsCondition($query, $orderSearchRequest);
        $this->handleAccurateWhere($query, $this->accurateWhere);

        $this->buildLockType($query, $orderSearchRequest);
        return $query;
    }

    /**
     * 用于备货单发货查询
     * @param OrderSearchRequest $orderSearchRequest
     * @return Builder
     */
    public function buildOrderStockingQuery(OrderSearchRequest $orderSearchRequest): Builder{
        Log::info('备货单查询条件', [$orderSearchRequest]);
        $ownerIdList = ShopBind::getValidIdentifierByRelation($orderSearchRequest->authShopId, $orderSearchRequest->ownerIdList, 1);
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $orderSearchRequest->shopIds = $shops->pluck('id')->toArray();
        $query=OrderItem::query()->with(['customGoodsSkus','customGoods'])->whereIn('order_items.status',[30,35]);
        $query->select(['order_items.*','orders.buyer_message','orders.seller_flag','orders.seller_memo','orders.pay_at']);
        $query->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->leftJoin('order_extras', 'order_extras.order_id', '=', 'orders.id');
        $query->orderBy('orders.'.$orderSearchRequest->timeField);

        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        Log::info('订单查询条件1', [$query->toSql()]);
        // 时间
        $this->buildByTime($query, $orderSearchRequest);
        Log::info('订单查询条件2', [$query->toSql()]);
        // 下拉查询
        $this->buildBySelectItem($query, $orderSearchRequest, $shops);
        Log::info('订单查询条件3', [$query->toSql()]);
        // 退款状态
        $this->buildByRefundAndSubRefund($query, $orderSearchRequest);
        Log::info('订单查询条件4', [$query->toSql()]);
        // 区域筛选
        $this->buildAddressGroupId($query, $orderSearchRequest->addressGroupId);
        $this->buildPreSaleStatus($query, $orderSearchRequest);
        $this->buildOrderBizType($query, $orderSearchRequest);
        Log::info('订单查询条件5', [$query->toSql()]);
        // tab标记
//        $this->buildByTabFlag($query, $orderSearchRequest);
        Log::info('订单查询条件6', [$query->toSql()]);
        // 打印状态
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        Log::info('订单查询条件7', [$query->toSql()]);
        // 订单状态
        $this->buildByOrderStatus($query, $orderSearchRequest);
        // 订单金额
        $this->buildOrderTotalFee($query, $orderSearchRequest);
        $this->buildPayment($query, $orderSearchRequest);
        //商品数量
        $this->buildGoodsNum($query, $orderSearchRequest);

        Log::info('订单查询条件8', [$query->toSql()]);
        // 排序
        $orderSearchRequest->sortArr = $this->buildBySort($query, $orderSearchRequest->sort);
        // 留言备注
        $this->buildByRemarkType($query, $orderSearchRequest);
        Log::info('订单查询条件9', [$query->toSql()]);


        // 快速筛选
//        $this->buildByQuickFilter($query, $orderSearchRequest->quickFilterValue,$orderSearchRequest->orderBizType);
        Log::info('订单查询条件10', [$query->toSql()]);

        $this->buildLockType($query, $orderSearchRequest);

        $this->handleAccurateWhere($query, $this->accurateWhere);
        return $query;
    }


    /**
     * 用于构建未打印和已打印订单查询
     * @param OrderSearchRequest $orderSearchRequest
     * @return Builder
     * @throws ApiException
     * @throws ErrorCodeException
     */
    public function buildQuery(OrderSearchRequest $orderSearchRequest): Builder
    {
        Log::info('订单查询条件', [$orderSearchRequest]);
        if (Environment::isUni()){
            $shops = User::firstById($orderSearchRequest->authUserId)->shops();
            if(!empty($orderSearchRequest->ownerIdList)){
                $shops=$shops->whereIn('identifier',$orderSearchRequest->ownerIdList);
            }
        }else{
            $ownerIdList = ShopBind::getValidIdentifierByRelation($orderSearchRequest->authShopId, $orderSearchRequest->ownerIdList, 1);
            $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        }
        $orderSearchRequest->shopIds = $shops->pluck('id')->toArray();
        $shopIds = $orderSearchRequest->shopIds;
        $factoryShops = [];
        if (!empty($orderSearchRequest->factoryOwnerIdList)) {
            $factoryShops = Shop::query()->whereIn('identifier', $orderSearchRequest->factoryOwnerIdList)->get();
            $this->factoryShopIds = $factoryShops->pluck('id')->toArray();
        }
        if ($orderSearchRequest->printMode == 2) {
            $shopIds = $this->factoryShopIds;
            $shops = $factoryShops;
        }
//        if ($orderSearchRequest->isShippedList == 1) {
//            $orderSearchRequest->printStatus = Order::PRINT_STATUS_YES;
//        }
        $orderSearchRequest->tabFlag = (string)$orderSearchRequest->tabFlag;
        $relation = ['orderItem'];
        $query = $this->newQuery($relation);

        // 简单查询字段
        $this->buildBySimpleCondition($query, $orderSearchRequest);
        // 时间
        $this->buildByTime($query, $orderSearchRequest);
        // 下拉查询
        $this->buildBySelectItem($query, $orderSearchRequest, $shops);
        // 退款状态
        $this->buildByRefund($query, $orderSearchRequest);
        // 区域筛选
        $this->buildAddressGroupId($query, $orderSearchRequest->addressGroupId);
        // tab标记
        $this->buildByTabFlag($query, $orderSearchRequest);
        $this->buildGoodsSearch($query, $orderSearchRequest);
        $this->buildSkuSearch($query, $orderSearchRequest);
        $this->buildSkuProperties($query,  $orderSearchRequest);
        $this->buildCutoffTime($query, $orderSearchRequest);
//        $this->buildPayment($query, $orderSearchRequest);
        // 打印状态
        $this->buildByPrintStatus($query, $orderSearchRequest->printStatus, $orderSearchRequest->orderStatus);
        // 订单状态
        $this->buildByOrderStatus($query, $orderSearchRequest);
        // 排序
        $orderSearchRequest->sortArr = $this->buildBySort($query, $orderSearchRequest->sort);
        // 留言备注
        $this->buildByRemarkType($query, $orderSearchRequest);
        $this->buildBuyerAndSellerMemo($query, $orderSearchRequest);

        // 快速筛选
        $this->buildByQuickFilter($query, $orderSearchRequest->quickFilterValue);

        // 分组字段
        $groupColumn = $this->getGroupColumn($orderSearchRequest);

        $offset = (int)$orderSearchRequest->offset;
        $limit = (int)$orderSearchRequest->limit;

//        $rows_found = 0;
        // 临时增加运行内存防止溢出
//        ini_set('memory_limit', '1024M');
//        $this->handleAccurateWhere($query, $this->accurateWhere);
        $this->getQueryByDisplayMerge($query, $orderSearchRequest, $groupColumn, $limit, $offset);

        // 分组后的 count 总数
//        $rows_found = $this->count;
        return $query;
    }

    /**
     * 订单区域筛选
     * @param $addressGroupId
     * @param $query
     * <AUTHOR>
     */
    private function buildAddressGroupId($query, $addressGroupId): void
    {
        if ($addressGroupId <= 0) {
            return;
        }
        $area = QueryArea::query()->findOrFail($addressGroupId);
        if ($area->version == 2){
            $provinceArr = explode(',', $area->province_str);
            $customDistrictArr = explode(',', $area->custom_district_str);

            if ($area->include == '1') {
                if (!empty($provinceArr) && !empty($provinceArr[0])) {
                    $query->whereIn('receiver_state', $provinceArr);
                }
                $query->where(function ($query) use ($customDistrictArr) {
                    foreach ($customDistrictArr as $index => $customDistrict) {
                        if (empty($customDistrict)) {
                            continue;
                        }
                        if ($index == 0) {
                            $query->where('receiver_city', 'like', $customDistrict . '%');
                            $query->orWhere('receiver_district', 'like', $customDistrict . '%');
                        } else {
                            $query->orWhere('receiver_city', 'like', $customDistrict . '%');
                            $query->orWhere('receiver_district', 'like', $customDistrict . '%');
                        }
                    }
                });
            } else {
                if (!empty($provinceArr) && !empty($provinceArr[0])) {
                    $query->whereNotIn('receiver_state', $provinceArr);
                }
                $query->where(function (Builder $query) use ($customDistrictArr) {
                    foreach ($customDistrictArr as $index => $customDistrict) {
                        if (empty($customDistrict)) {
                            continue;
                        }
                        $query->where('receiver_city', 'not like', $customDistrict . '%');
                        $query->where('receiver_district', 'not like', $customDistrict . '%');
                    }
                });
            }

        }else{
            $provinceArr = explode(',', $area->province_str);
            $cityArr = explode(',', $area->city_str);
            $districtArr = explode(',', $area->district_str);
            $customDistrictArr = explode(',', $area->custom_district_str);
            $districtArr = array_merge($districtArr, $customDistrictArr);
            $districtArr = array_filter($districtArr);
            if ($area->include == '1') {
                $query->whereIn('district_code', $districtArr);
            } else {
                $query->whereNotIn('district_code', $districtArr);
            }
        }

    }

    /**
     * @param $query
     * @param $print_status
     * @param $order_status
     * <AUTHOR>
     */
    private function buildByPrintStatus($query, $print_status, $order_status): void
    {
        if (!is_null($print_status) && $print_status >= 0) {
            //已打印
            if ($print_status == \App\Models\Order::PRINT_STATUS_YES) {
                // 已发货需要显示部分发货的订单，改成判断 printed_at
                if (Order::ORDER_STATUS_DELIVERED == $order_status) {
                    $query->whereNotNull('printed_at');
                } else {
                    $query->whereIn('orders.print_status', [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART]);
                }
            }
            //未打印
            if ($print_status == Order::PRINT_STATUS_NO) {
                $query->whereIn('orders.print_status', [Order::PRINT_STATUS_NO]);
//                $query->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED, Order::ORDER_STATUS_CLOSE]); // 未打印不显示部分发货的订单
//                $query->whereIn('orders.print_status', [$print_status, Order::PRINT_STATUS_PART]);
//                $query->where('order_items.print_status', [$print_status, Order::PRINT_STATUS_PART]);
//                $query->whereIn('orders.print_status', [$print_status, Order::PRINT_STATUS_PART]);

//                $this->groupWhere[] = ['item-print_status', OrderItem::PRINT_STATUS_NO];
//                $this->groupWhere[] = ['item-refund_status', OrderItem::REFUND_STATUS_NO];

//                $query->whereHas('orderItem',function ($query2){
//                    $query2->where('print_status',OrderItem::PRINT_STATUS_NO);
//                    $query2->where('refund_status',OrderItem::REFUND_STATUS_NO);
//                });
            }

            //打印一次
            if ($print_status == Order::PRINT_STATUS_ONE) {
                $query->whereNotNull('printed_at')->where('orders.print_num', 1);
            }

            //打印多次
            if ($print_status == Order::PRINT_STATUS_MORE) {
                $query->whereNotNull('printed_at')->where('orders.print_num', '>', 1);
            }

            //已打印发货单
            if ($print_status == Order::PRINT_STATUS_SHIPPING) {
                $query->whereNotNull('print_shipping_at');
            }

        } else {
            //全部
//            $query->whereIn('orders.print_status', [Order::PRINT_STATUS_NO, Order::PRINT_STATUS_YES,
//                Order::PRINT_STATUS_PART, Order::PRINT_STATUS_PRINTING]);
        }
    }

    /**
     * 构建截单时间的查询条件
     * @param  Builder  $query
     * @param  OrderSearchRequest  $orderSearchRequest
     * @return void
     */
    public function buildCutoffTime(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        //[截单时间]一般指快递网点每天揽收订单的最后时间。这个时间点之前的订单通常会在当天处理并尽量发出，而超过这个时间点的订单则可能会推迟到下一个工作日处理。此筛选条件是为帮助商家根据自己的发货规则筛选出需要当天打单发货的订单
        //参考淘宝官方打单工具
        if(!empty($orderSearchRequest->cutoffBeginRelativeDays)&&!empty($orderSearchRequest->cutoffBeginHourMinuteSecond)){
            // relativeDays: 相对天数，正数表示相对当前日期的几天后，负数表示相对当前日期的几天前
            //先用当前时间加上天数，再把时间设定成时分秒
            $payAtBegin = Carbon::now()->addDays($orderSearchRequest->cutoffBeginRelativeDays)->format('Y-m-d').' '.$orderSearchRequest->cutoffBeginHourMinuteSecond;
            $query->where('orders.pay_at', '>=', $payAtBegin);
        }

        if(!empty($orderSearchRequest->cutoffEndRelativeDays)&&!empty($orderSearchRequest->cutoffEndHourMinuteSecond)){
            // relativeDays: 相对天数，正数表示相对当前日期的几天后，负数表示相对当前日期的几天前
            //先用当前时间加上天数，再把时间设定成时分秒
            $payAtEnd = Carbon::now()->addDays($orderSearchRequest->cutoffEndRelativeDays)->format('Y-m-d').' '.$orderSearchRequest->cutoffEndHourMinuteSecond;
            $query->where('orders.pay_at', '<=', $payAtEnd);
        }


    }

    public function buildSkuProperties(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        if(!empty($orderSearchRequest->skuValue1)){
//            $query->whereHas('orderItem',function ($query2) use ($orderSearchRequest){
//                if($orderSearchRequest->skuInclude=='1') {
//                    $query2->where('sku_value1','like', '%'.$orderSearchRequest->skuValue1."%");
//                }
//                if($orderSearchRequest->skuInclude=='2') {
//                    $query2->where('sku_value1', $orderSearchRequest->skuValue1);
//                }
//                if($orderSearchRequest->skuInclude=='3') {
//                    $query2->where('sku_value1','not like', '%'.$orderSearchRequest->skuValue1."%");
//                }
//            });
            $query->where(function($query2) use ($orderSearchRequest) {
                    if($orderSearchRequest->skuInclude=='1') {
                        $query2->where('order_items.sku_value1','like', '%'.$orderSearchRequest->skuValue1."%");
                    }
                    if($orderSearchRequest->skuInclude=='2') {
                        $query2->where('order_items.sku_value1', $orderSearchRequest->skuValue1);
                    }
                    if($orderSearchRequest->skuInclude=='3') {
                        $query2->where('order_items.sku_value1','not like', '%'.$orderSearchRequest->skuValue1."%");
                    }
                });
        }
        if(!empty($orderSearchRequest->skuValue2)){
//            $query->whereHas('orderItem',function ($query2) use ($orderSearchRequest){
//                $query2->where('sku_value2',$orderSearchRequest->skuValue2);
//            });
            $query->where('order_items.sku_value2',$orderSearchRequest->skuValue2);
        }
    }

    /**
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * <AUTHOR>
     */
    private function buildByOrderStatus(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        $order_status = $orderSearchRequest->orderStatus;
        $quickFilterValue = $orderSearchRequest->quickFilterValue;
        if ($orderSearchRequest->isShippedList){
            $query->whereNotNull('orders.send_at');
            $query->whereIn('order_status', Order::ORDER_STATUS_DELIVERED2_ARRAY);
        }else{
            if ($order_status > 0) {
                switch ($order_status) {
                    case Order::ORDER_STATUS_PAYMENT:
                        if ($quickFilterValue == '12') {
                            $query->whereNull('orders.send_at');
                            $query->whereIn('order_status', Order::ORDER_STATUS_ALL_ARRAY);
                        } else {
                            if (($orderSearchRequest->refundStatus == 1 || in_array(1, $orderSearchRequest->refundStatusArr))
                                && in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::PRINTED, OrderIndexTabConst::ALL])) { // 有售后 打印tab 才查询交易关闭
                                $query->whereIn('order_status', [
                                    Order::ORDER_STATUS_PAYMENT,
                                    Order::ORDER_STATUS_PART_DELIVERED,
                                    Order::ORDER_STATUS_CLOSE,
                                ]);
                                $query->whereNull('orders.send_at'); // 未发货
                            } else{
                                $query->whereIn('order_status', [
                                    Order::ORDER_STATUS_PAYMENT,
                                    Order::ORDER_STATUS_PART_DELIVERED,
                                ]);
                            }

                        }
                        break;
                    case Order::ORDER_STATUS_DELIVERED:
                        $query->whereNotNull('orders.send_at');
//                    $query->whereIn('order_status', Order::ORDER_STATUS_DELIVERED_ARRAY);
                        break;
                    default:
                        if ($quickFilterValue != '12') {
                            $query->where('order_status', $order_status);
                        }
                        break;
                }
            } else {
                //全部订单状态
                $query->whereIn('order_status', Order::ORDER_STATUS_ALL_ARRAY);
            }
        }

    }

    /**
     * @param Builder $query
     * @param $quickFilterValue
     * @param null $orderBizType
     * @return bool[]
     * <AUTHOR>
     */
    private function buildByQuickFilter(Builder $query, $quickFilterValue,$orderBizType=null)
    {
        //快捷筛选
        if ($quickFilterValue) {
            switch ($quickFilterValue) {
                case '1': //合并订单
                    $this->onlyShowMergeOrder = true;
                    break;
                case '2': //非合单
                    $this->notShowMergeOrder = true;
                    break;
                case '3': //同一人多个地址
                    $this->handToShowMergeOrder = true;
                    break;
                case '4': //未锁定订单
                    //$query->whereNull('locked_at');
                    break;
                case '5': //已锁定订单
                    //$query->whereNotNull('locked_at');
                    break;
                case '6': //乡镇
//                    $query->where('village_flag', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['village_flag', 1]
                    ];

                    break;
                case '7': //非乡镇
//                    $query->where('village_flag', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['village_flag', 0]
                    ];
                    break;
                case '20': //超时订单
//                    $query->where('order_items.promise_ship_at', '<=', date('Y-m-d H:i:s', time()))
//                        ->whereIn('order_status', [\App\Models\Order::ORDER_STATUS_PART_DELIVERED, Order::ORDER_STATUS_PAYMENT]);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['order_items.promise_ship_at', '<=', date('Y-m-d H:i:s', time())]
                    ];
                    $this->accurateWhere[] = [
                        'func' => 'whereIn',
                        'args' => ['order_status', [\App\Models\Order::ORDER_STATUS_PART_DELIVERED, Order::ORDER_STATUS_PAYMENT]]
                    ];
                    break;
                case '21': //有退款待发货
//                    $query->whereNull('orders.send_at');
                    $this->accurateWhere[] = [
                        'func' => 'whereNull',
                        'args' => ['orders.send_at']
                    ];

                    break;
                case '22': //虚假地址
//                    $query->where('orders.address_flag', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.address_flag', 1]
                    ];
                    break;
                case '23': //已分配厂家订单
                    $query->where('orders.factory_id', '>', 0);
                    break;
                case '24': //已代发订单
                    $query->where('order_extras.is_factory_shipped', 1);
                    break;
                case '25': //质检订单
                    $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_QUALITY_INSPECTION);
                    break;
                case '26': //质检通过
                    $query->where('order_item_extras.quality_status', OrderItemExtra::QUALITY_STATUS_CHECKED_PASS);
                    break;
                case '27': //质检未发货
                    $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_QUALITY_INSPECTION);
                    $query->where('order_item_extras.quality_delivery_status', OrderItemExtra::QUALITY_DELIVERY_STATUS_NOT_DELIVERY);
                    break;
                case '28': //是否预售
//                    $query->where('orders.is_pre_sale', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_pre_sale', 1]
                    ];
                    break;
                case '28-1': //非预售
//                    $query->where('orders.is_pre_sale', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_pre_sale', 0]
                    ];
                    break;
                case '29': //是否拆分
//                    $query->where('orders.is_split', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_split', 1]
                    ];
                    break;
                case 'noSplit': //未拆单
//                    $query->where('orders.is_split', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_split', 0]
                    ];
                    break;
                case 'homeDelivery': // 送货上门订单
//                    $query->where('orders.is_home_delivery', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_home_delivery', 1]
                    ];
                    break;
                case 'notHomeDelivery': // 非送货上门订单
//                    $query->where('orders.is_home_delivery', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_home_delivery', 0]
                    ];
                    break;
                case 'liveOrder': // 直播订单
//                    $query->where('orders.is_live_order', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_live_order', 1]
                    ];
                    break;
                case 'giftOrder': //赠品订单
//                    $query->where('orders.is_gift_order', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_gift_order', 1]
                    ];
                    break;
                case 'nonGiftOrder': //非赠品订单
//                    $query->where('orders.is_gift_order', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_gift_order', 0]
                    ];
                    break;
                case 'urgeShipment': //催发货订单
//                    $query->whereNotNull('orders.urge_shipment_at');
                    $this->accurateWhere[] = [
                        'func' => 'whereNotNull',
                        'args' => ['orders.urge_shipment_at']
                    ];
                    break;
                //部分发货订单
                case 'partShipment':
//                    $query->where('orders.order_status', Order::ORDER_STATUS_PART_DELIVERED);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.order_status', Order::ORDER_STATUS_PART_DELIVERED]
                    ];
                    break;
                case 'isRemoteTransit': // 偏远中转
//                    $query->where('orders.is_remote_transit', Order::REMOTE_TRANSIT_YES);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_remote_transit', Order::REMOTE_TRANSIT_YES]
                    ];
                    break;
                case 'isGiveGift':
//                    $query->where('orders.is_give_gift', 1);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_give_gift', 1]
                    ];
                    break;
                case 'notGiveGift':
//                    $query->where('orders.is_give_gift', 0);
                    $this->accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.is_give_gift', 0]
                    ];
                    break;
                case 'assignTypeSelf':
                    $query->where('orders.assign_type', Order::ASSIGN_TYPE_SELF);
                    break;
                default:
                    break;
            }
            if (!in_array($quickFilterValue, ['25', '26', '27'])) {
                if (Environment::isDy()) {
                    $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_GENERAL);
                }
            }
        } else {
            if (Environment::isDy()&&!isset($orderBizType)) {
                $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_GENERAL);
            }
        }
    }


    /**
     * 构建买家留言和卖家备注的查询条件
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * @return void
     */
    public function buildBuyerAndSellerMemo(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        if($orderSearchRequest->buyerMessage){
            $query->where(function ($query) use ($orderSearchRequest) {
                $query->where('buyer_message', 'like', "%{$orderSearchRequest->buyerMessage}%");
            });
        }
        if($orderSearchRequest->sellerMemo){
            $query->where(function ($query) use ($orderSearchRequest) {
                $query->where('seller_memo', 'like', "%{$orderSearchRequest->sellerMemo}%");
            });
        }
    }
    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @param Builder $query
     * @return void
     */
    private function buildByRemarkType(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        //处理留言备注
        switch ($orderSearchRequest->remarkType) {
            case '4'://无留言和备注
                $query->where('buyer_message', '')->where('seller_memo', '[]');
                break;
            case '1'://仅有留言
                $query->where('buyer_message', '<>', '')->where('seller_memo', '[]');
                break;
            case '2'://仅有备注
                $query->where('buyer_message', '')->where('seller_memo', '<>', '[]');
                break;
            case '3'://有留言和备注
                $query->where('buyer_message', '<>', '')->where('seller_memo', '<>', '[]');
                break;
            case '5'://有留言或备注
                $query->where(function ($query) {
                    $query->orWhere('buyer_message', '<>', '')
                        ->orWhere('seller_memo', '<>', '[]');
                });
                break;
            default:
                break;
        }
    }

    /**
     * @param $sort
     * @param $query
     * @return
     */
    private function buildBySort($query, $sort)
    {
        $sortArea = explode(',', $sort);
        //根据省份城市排序
        if ($sortArea[0] === 'area') {
            $orderSort = $sortArea[1] ? 'asc' : 'desc';
            $query->orderByRaw('CONVERT(orders.receiver_state USING gbk) COLLATE gbk_chinese_ci ' . $orderSort)
                ->orderByRaw('CONVERT(orders.receiver_city USING gbk) COLLATE gbk_chinese_ci ' . $orderSort)
                ->orderByRaw('CONVERT(orders.receiver_district USING gbk) COLLATE gbk_chinese_ci ' . $orderSort);
        }

        Order::handleOrderBy($query, $sort);
        $this->sortArr = $sort;
        return $this->sortArr;
    }


    public function buildLockType($query, OrderSearchRequest $orderSearchRequest){
        switch ($orderSearchRequest->lockType) {
            case 0: //未锁单
                $query->whereNull('locked_at');
                break;
            case 1: //已锁单
                $query->whereNotNull('locked_at');
                break;
        }
        Log::info('buildLockType', [$orderSearchRequest->lockType]);
    }
    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @param Builder $query
     * @return void
     */
    private function buildByTabFlag($query, OrderSearchRequest $orderSearchRequest): void
    {
        switch ($orderSearchRequest->tabFlag) {
            case OrderIndexTabConst::UNPRINT: //未打印
            case OrderIndexTabConst::PRINTED: //已打印
            case OrderIndexTabConst::PRINTED_REFUND: //已打印有退款
            case OrderIndexTabConst::SHIPPED: //已发货
            case OrderIndexTabConst::ALL: //全部
                $query->whereNull('locked_at');
//                $query->whereIn('orders.order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED]);
                break;
            case OrderIndexTabConst::LOCKED: //已锁单
                $query->whereNotNull('locked_at');
                break;
            case OrderIndexTabConst::ONLY_TAKE_WAYBILL: //仅取号
                // 已取号 未打印
                $query->whereNotNull('take_waybill_at');
                $query->whereNull('printed_at');
                break;
            case OrderIndexTabConst::TRADE_CLOSED: //交易关闭
                break;
        }

        // 未打印要排除已打印的订单
        if ($orderSearchRequest->tabFlag == OrderIndexTabConst::UNPRINT && $orderSearchRequest->displayMerge) { // 合单的时候
            $beginAt = $orderSearchRequest->beginAt;
            $endAt = $orderSearchRequest->endAt;
            if (empty($beginAt) && empty($endAt)) { // 默认时间
                $beginAt = date('Y-m-d 00:00:00', strtotime('-180 day'));
                $endAt = date('Y-m-d 23:59:59');
            }
            $addressMd5Arr = Order::query()
                ->whereIn('shop_id', $orderSearchRequest->shopIds)
                ->whereIn('print_status', [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART])
                ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
                ->where('orders.pre_shipment_status', Order::PRE_SHIPMENT_STATUS_NO)
                ->where('orders.refund_status', Order::REFUND_STATUS_NO)
                ->whereBetween('orders.pay_at', [$beginAt, $endAt])
                ->get(['address_md5'])->pluck('address_md5')->unique()->toArray();
            $query->whereNotIn('orders.address_md5', $addressMd5Arr);
        }
    }

    private function buildByRefundAndSubRefund($query, OrderSearchRequest $orderSearchRequest): void{
        $refundStatusArr = $orderSearchRequest->refundStatusArr;
        if (!empty($refundStatusArr)){
            $ordersRefundStatusArr = [];
            $orderItemsRefundStatusArr = [];
            $orderItemsRefundSubStatusArr = [];
            if (in_array(0,$refundStatusArr)){ // 无退款
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [\App\Models\Order::REFUND_STATUS_PART, Order::REFUND_STATUS_NO]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_NO]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, [RefundSubStatusConst::NONE]);;
            }
            if (in_array(1, $refundStatusArr)){ // 退款中
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_YES]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, RefundSubStatusConst::REFUND_PROCESSING_ARRAY);;
            }
            if (in_array(2, $refundStatusArr)){ // 退款完成
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_YES]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, RefundSubStatusConst::REFUND_COMPLETE_ARRAY);;
            }
            $query->whereIn('orders.refund_status', $ordersRefundStatusArr);
            $query->whereIn('order_items.refund_status', $orderItemsRefundStatusArr);
            $query->whereIn('order_items.refund_sub_status', $orderItemsRefundSubStatusArr);

        }
    }
    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @param $query
     * @return void
     */
    private function buildByRefund($query, OrderSearchRequest $orderSearchRequest): void
    {
        if (!empty($orderSearchRequest->refundStatus)) { // 空, 1 有售后和部分售后  2 无售后(售后关闭)和部分售后
            if ($orderSearchRequest->refundStatus == 1){
                $query->whereIn('orders.refund_status', [\App\Models\Order::REFUND_STATUS_YES, \App\Models\Order::REFUND_STATUS_PART]);
            }elseif($orderSearchRequest->refundStatus == 2){
                $query->whereIn('orders.refund_status', [\App\Models\Order::REFUND_STATUS_NO, \App\Models\Order::REFUND_STATUS_PART]);
            }
        }
        if (!empty($orderSearchRequest->refundStatusArr)) { // 空, 0 无售后 1 有售后 2部分售后
            $arr = [];
            foreach ($orderSearchRequest->refundStatusArr as $item) {
                switch ($item){
                    case \App\Models\Order::REFUND_STATUS_YES;
                        $arr[] = \App\Models\Order::REFUND_STATUS_YES;
                        break;
                    case \App\Models\Order::REFUND_STATUS_PART;
                        $arr[] = \App\Models\Order::REFUND_STATUS_PART;
                        break;
                    case \App\Models\Order::REFUND_STATUS_NO;
                        $arr[] = \App\Models\Order::REFUND_STATUS_NO;
                        break;
                }
            }
            $query->whereIn('orders.refund_status', $arr);
        }
        //快捷筛选有退款
        if (in_array($orderSearchRequest->quickFilterValue, ['12', '21'])) {
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART]);
        } elseif (in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::SHIPPED, OrderIndexTabConst::ALL, OrderIndexTabConst::LOCKED])) {
            //全部 订单状态包含退款和无退款
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART]);
        } elseif (in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::TRADE_CLOSED])) {
            // 交易关闭
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_YES]);
        } else {
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART]);
        }
        if (in_array($orderSearchRequest->quickFilterValue, ['13'])) {
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO]);
        }
    }

    private function getGroupColumn(OrderSearchRequest $orderSearchRequest)
    {
        $groupColumn = $orderSearchRequest->getGroupColumn();
//        $groupColumn = 'address_md5';
        return $groupColumn;
    }

    private function buildBySelectItem(Builder $query, OrderSearchRequest $orderSearchRequest, $shops)
    {
//        if(empty($orderSearchRequest->search)){
//            return;
//        }
        $goods_include = $orderSearchRequest->goodsInclude;
        $sku_include = $orderSearchRequest->skuInclude;
        $selectItem = $orderSearchRequest->selectItem;

        $search = $orderSearchRequest->search;
        //规格名称，颜色，尺寸搜索
//        if ($orderSearchRequest->selectItem == 8) {
//            if ($orderSearchRequest->goodsColor || $orderSearchRequest->goodsSize) {
//                $search = $orderSearchRequest->goodsColor . "," . $orderSearchRequest->goodsSize;
//            }
//        }

        $includeOrNot = $orderSearchRequest->includeOrNot;
        $isInclude =
        $accurateWhere = [];

//        $orderService = OrderServiceManager::create(config('app.platform'));
//        $shops = \App\Models\Shop::query()->whereIn('id', $orderSearchRequest->shopIds)->get();

        if ($selectItem > 0 && ($search || ($goods_include || $sku_include || $orderSearchRequest->skuValue1 || $orderSearchRequest->skuValue2))) {
            switch ($selectItem) {
                case '1':
                    $tid = $search;
                    $accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.tid', $tid]
                    ];
                    break;
                case '2':
                    $query->where('express_no', $search);
                    break;
                case '3':
                    $orderIdArr = [];
                    foreach ($shops as $shop) {
                        $orderService = OrderServiceManager::create($shop->getPlatform());
                        $orderService->setShop($shop);
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $orderIdArr);
                    //}
                    break;
                case '4':
                    $orderIdArr = [];
                    foreach ($shops as $shop) {
                        $orderService = OrderServiceManager::create($shop->getPlatform());
                        $orderService->setShop($shop);
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $orderIdArr);
                    //}
                    break;
                case '5': // 商品包含
                    if (!empty($goods_include)) {
//                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $orderSearchRequest->shopIds, $accurateWhere, true);
                    }
                    if (!empty($sku_include)) {
//                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $orderSearchRequest->shopIds, $accurateWhere, true);
                    }
                    if (!empty($orderSearchRequest->skuValue1)) {
                        $accurateWhere = Order::getAccurateWhereByGoodsSku12('sku_value1', $orderSearchRequest->skuValue1, $accurateWhere, true);
                    }
                    if (!empty($orderSearchRequest->skuValue2)) {
                        $accurateWhere = Order::getAccurateWhereByGoodsSku12('sku_value2', $orderSearchRequest->skuValue2, $accurateWhere, true);
                    }
                    break;
                case '6': // 商品不包含
                    if (!empty($goods_include)) {
//                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $orderSearchRequest->shopIds, $accurateWhere, false);
                    }
                    if (!empty($sku_include)) {
//                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $orderSearchRequest->shopIds, $accurateWhere, false);
                    }
                    if (!empty($orderSearchRequest->skuValue1)) {
                        $accurateWhere = Order::getAccurateWhereByGoodsSku12('sku_value1', $orderSearchRequest->skuValue1, $accurateWhere, false);
                    }
                    if (!empty($orderSearchRequest->skuValue2)) {
                        $accurateWhere = Order::getAccurateWhereByGoodsSku12('sku_value2', $orderSearchRequest->skuValue2, $accurateWhere, false);
                    }
                    break;
                case '7':
                    $query->whereHas('orderItem', function ($query) use ($search) {
                        $query->where('outer_sku_iid', $search);
                    });
                    break;
                case '8':
                    if ($orderSearchRequest->goodsColor || $orderSearchRequest->goodsSize) {
                        $search = $orderSearchRequest->goodsColor . "," . $orderSearchRequest->goodsSize;
                    }
                    //规格名称，颜色尺寸
                    $sortArr = explode(',', $search);
                    $search1 = $sortArr[0];
                    $search2 = $sortArr[1];
                    $condition = $includeOrNot == 1 ? 'like' : 'not like';

                    $query->whereHas('orderItem', function ($query) use ($search1, $condition) {
                        $query->where('sku_value', $condition, '%' . $search1 . '%');
                    });

                    $query->whereHas('orderItem', function ($query) use ($search2, $condition) {
                        $query->where('sku_value', $condition, '%' . $search2 . '%');
                    });
                    break;
                case '9':
                    $query->where('seller_memo', 'like', "%{$search}%");
                    break;
                case '13':
                    $query->where('buyer_message', 'like', "%{$search}%");
                    break;
                case '14':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.num_iid', $arr]
                    ];
                    break;
                case '15':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.sku_id', $arr]
                    ];
                    break;
                case '16': // 商品种类
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isPreShipmentList != 1) { //
                        break;
                    }
                    $range = StrUtil::extractedNumOrNumArr($search);
                    if (is_array($range)) {
                        $query->whereBetween('orders.sku_num', $range);
                    } else {
                        $query->where('orders.sku_num', $range);
                    }
                    break;
                case '17': // 订单数量
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isPreShipmentList != 1) { // 合并单走 merge_orders_num 逻辑
                        break;
                    }
                    $range = StrUtil::extractedNumOrNumArr($search);
                    if (is_array($range)) {
                        $query->whereBetween('orders.num', $range);
                    } else {
                        $query->where('orders.num', $range);
                    }
                    break;
                case '18':
                    // 批次号
                    $arr = explode('-', $search);
                    $tidsArray = Package::query()->where('batch_no', 'like', $arr[0] . '%')
                        ->select('tids')->get()->pluck('tids')->toArray();
                    $orderIdArr = [];
                    foreach ($tidsArray as $index => $item) {
                        $orderIdArr = array_merge($orderIdArr, explode(',', $item));
                    }
                    $query->whereIn('orders.tid', $orderIdArr);
                    break;
                case '19':
                    // 昵称
                    $query->where('orders.buyer_nick', $search);
                    break;
                case 'isRemoteTransit': // 偏远中转
                    $query->where('orders.is_remote_transit', Order::REMOTE_TRANSIT_YES);
                    break;
                default:
                    break;
            }
        } else {
            $orderIdArr = [];
            //关键词查询
            if ($search) {
                foreach ($shops as $shop) {
                    $orderService = OrderServiceManager::create($shop->getPlatform());
                    $orderService->setShop($shop);
                    $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                    if (isPhoneNumber($search)) {
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                    }
                }
                $packageBuilder = Package::query()
                    ->leftJoin('package_orders', 'package_id', '=', 'packages.id')
                    ->where('waybill_code', $search);
                if ($orderSearchRequest->isUnshippedList){ // 未发货
                    $packageBuilder->where(DB::raw('IFNULL(packages.status, 0)'), '!=', Package::ORDER_STATUS_DELIVERED);
                }elseif ($orderSearchRequest->isShippedList){ // 已发货明细
//                    $query->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED, Package::ORDER_STATUS_PART_DELIVERED]);

//                    $query->where('packages.waybill_code', $search);
//                    $packageBuilder->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED, Package::ORDER_STATUS_PART_DELIVERED]);
//                    $packageBuilder->orWhere('packages.waybill_code',$search);

                }elseif ($orderSearchRequest->isResendList){ // 重新发货
                    $waybillCodeList = [$search];
                    $packageList = Package::query()->whereIn('waybill_code', $waybillCodeList)->get(['id','waybill_wp_index','multi_package_main_waybill_code']);
                    $multiPackageMainWaybillCodeArr = $packageList->pluck('multi_package_main_waybill_code')->toArray();
                    $waybillWpIndexArr = $packageList->pluck('waybill_wp_index')->toArray();  // 老单号
                    $packageIdArr = $packageList->pluck('id')->toArray();
                    $multiPackageList = Package::query()
                        ->whereIn('waybill_code', $multiPackageMainWaybillCodeArr)
                        ->get(['id']); // 一单多包

                    $packageIdArr = array_merge($packageIdArr, $multiPackageList->pluck('id')->toArray());

                    $query->where(function ($query) use ($waybillCodeList, $packageIdArr,$waybillWpIndexArr,$search) {
                        $query->whereIn('pt_logistics.waybill_code', $waybillCodeList);
                        $query->orWhereIn('pt_logistics.waybill_wp_index', $waybillWpIndexArr);
                        $query->orWhereIn('packages.id', $packageIdArr);
                        $query->orWhereIn('orders.tid', [$search]);
                    });
                }
                $packageOrderIdArr = $packageBuilder
                    ->get(['order_id'])
                    ->pluck('order_id')
                    ->toArray();
                Log::info('packageOrderIdArr',[ $packageOrderIdArr]);
                $function = function ($query) use ($search, $packageOrderIdArr, $orderIdArr, $orderSearchRequest) {
                    $tid = $search;

//                    $query->whereRaw('2=2');
                    // 查询订单号和已打印逻辑冲突
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isUnshippedList
                        && $orderSearchRequest->tabFlag == OrderIndexTabConst::PRINTED) {
                        $tempOrder = Order::query()->where('tid', $tid)->first(['address_md5']);

                        if (!empty($tempOrder)){
                            $query->orWhere('orders.address_md5',  $tempOrder->address_md5);
                        }else{
                            $query->orWhere('orders.tid', $tid); // 有可能查不到值，这个防止订单号查不到值
                        }
                    } else {
                        $query->orWhere('orders.tid', $tid);
                    }

                    if (in_array(config('app.platform'), [PlatformConst::KS, PlatformConst::JD])) {
                        $query->orWhere('buyer_nick', $search);
                    }
                    if (!empty($packageOrderIdArr)) {
                        $query->orWhereIn('orders.id', $packageOrderIdArr);
                    }
                    if (!empty($orderIdArr)) {
                        $query->orWhereIn('orders.id', $orderIdArr);
                    }
                };
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => $function,
                ];
            }
        }

        //实付金额
        if ($orderSearchRequest->selectItem == 10 && $search) {
            if (is_numeric($search)) {
                $this->amountArr['payment'] = $search;
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->amountArr['payment'] = $searchArr;
                }
            }
        }

        //订单金额
        if ($orderSearchRequest->selectItem == 11 && $search) {
            if (is_numeric($search)) {
                $this->amountArr['total_fee'] = $search;
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->amountArr['total_fee'] = $searchArr;
                }
            }
        }

        if ($orderSearchRequest->selectItem == 12 && $search) {
            if (is_numeric($search)) {
                $this->goodsNum = $search;
                if ($this->goodsNum == 1) {
                    $query->where('num', $this->goodsNum);
                }
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->goodsNum = $searchArr;
                }
            }
        }
        if ($orderSearchRequest->selectItem == 16 && $search) {
            // 商品种类
            if (strpos($search, '-')) {
                list($left, $right) = explode('-', $search);
                $this->ordersKind = [$left, $right];
            } else {
                $this->ordersKind = $search;
            }
        }
        if ($orderSearchRequest->selectItem == 17 && $search) {
            // 订单数量
            if (strpos($search, '-')) {
                list($left, $right) = explode('-', $search);
                $this->ordersNum = [$left, $right];
            } else {
                $this->ordersNum = $search;
            }
        }
        $this->accurateWhere = array_merge($this->accurateWhere, $accurateWhere);
    }

    private function newQuery($relation): Builder
    {

        $builder = \App\Models\Order::query();
        if (!empty($relation)) {
            $builder->with($relation);
        }
        return $builder;
    }

    /**
     * @throws ErrorCodeException
     */
    private function buildBySimpleCondition(Builder $query, OrderSearchRequest $orderSearchRequest)
    {
        // shop id
        if ($orderSearchRequest->printMode == 1) {
            $query->whereIn('orders.shop_id', $orderSearchRequest->shopIds);
        } else {
            $query->whereIn('orders.factory_id', $this->factoryShopIds);
        }
        //旗帜颜色
        if ($orderSearchRequest->flag) {
            if (is_array($orderSearchRequest->flag)) {
//                $query->whereIn('seller_flag', $orderSearchRequest->flag);
                $this->accurateWhere[] = [
                    'func' => 'whereIn',
                    'args' => ['seller_flag', $orderSearchRequest->flag]
                ];
            } else {
//                $query->where('seller_flag', $orderSearchRequest->flag);
                $this->accurateWhere[] = [
                    'func' => 'where',
                    'args' => ['seller_flag', $orderSearchRequest->flag]
                ];
            }
//            $conditions[] = ['seller_flag', $orderSearchRequest->flag];
        }
        if (!empty($orderSearchRequest->customPrintContent)) {
//            $query->where('custom_print_content', $orderSearchRequest->customPrintContent);
            $this->accurateWhere[] = [
                'func' => 'where',
                'args' => ['custom_print_content', $orderSearchRequest->customPrintContent]
            ];
        }
        if (!empty($orderSearchRequest->customGroup)) {
//            $query->where('custom_group', $orderSearchRequest->customGroup);
            $this->accurateWhere[] = [
                'func' => 'where',
                'args' => ['custom_group', $orderSearchRequest->customGroup]
            ];
        }
        if ($goodsId = $orderSearchRequest->goodsId) {
            if ($orderSearchRequest->isOnlyGoods) {
//                $query->whereIn('order_items.num_iid', $orderSearchRequest->goodsId);
                $this->accurateWhere[] = [
                    'func' => 'whereIn',
                    'args' => ['order_items.num_iid', $goodsId]
                ];
            } else {
                $this->accurateWhere[] = [
                    'func' => 'whereIn',
                    'args' => ['order_items.num_iid', $goodsId]
                ];
            }

        }
        $skuIdList = $orderSearchRequest->skuIdList;
        if (!empty($skuIdList)) {

            $newSkuIdList = [];
            foreach ($skuIdList as $item) {
                $newItem = explode(',', $item);
                foreach ($newItem as $v) {
                    $newSkuIdList[] = $v;
                }
            }
            if ($orderSearchRequest->isOnlyGoods) {
                $query->whereIn('order_items.sku_id', $newSkuIdList);
            } else {
                $this->accurateWhere[] = [
                    'func' => 'whereIn',
                    'args' => ['order_items.sku_id', $newSkuIdList]
                ];
                // 解决一个订单多个商品，其他商品发货了导致被查出来
                if ($orderSearchRequest->isUnshippedList) {
                    $this->accurateWhere[] = [
                        'func' => 'whereIn',
                        'args' => ['order_items.status', [OrderItem::ORDER_STATUS_PAYMENT, OrderItem::ORDER_STATUS_PART_DELIVERED]]
                    ];
                }

            }

//            Log::info('补充sku条件到精确查询', $this->accurateWhere);
        }
        if ($orderSearchRequest->smartLogistics != -1) {
            $query->where('smart_logistics', $orderSearchRequest->smartLogistics);
            Log::info('smartLogistics', $orderSearchRequest->smartLogistics);
        }
        if (!empty($orderSearchRequest->goodsNum)) {
            $this->goodsNum = StrUtil::extractedNumOrNumArr($orderSearchRequest->goodsNum);
//            if ($this->goodsNum == 1) {
//                $query->where('num', $this->goodsNum);
//            }
        }
        if(!empty($orderSearchRequest->ordersNum)){
            $this->ordersNum = StrUtil::extractedNumOrNumArr($orderSearchRequest->ordersNum);
        }
        if (!empty($orderSearchRequest->orderTotalFee)) {
            $total_fee = StrUtil::extractedNumOrNumArr($orderSearchRequest->orderTotalFee);
            if (is_numeric($total_fee)) {
                $this->amountArr['total_fee'] = $total_fee;
//                $query->where('orders.total_fee', $total_fee);
            } else {
                $this->amountArr['total_fee'] = $total_fee;
//                $query->whereBetween('orders.total_fee', $total_fee);
            }
        }
        if (!empty($orderSearchRequest->payment)) {
            $payment = StrUtil::extractedNumOrNumArr($orderSearchRequest->payment);
            if (is_numeric($payment)) {
                $this->amountArr['payment'] = $payment;
//                $query->where('orders.payment', $payment);
            } else {
                $this->amountArr['payment'] = $payment;
//                $
            }
        }
        // 商品相关
//        if (!empty($orderSearchRequest->goodsTitle)){
//            $query->where('order_items.goods_title','like', "%{$orderSearchRequest->goodsTitle}%");
//        }
        // 商品模糊搜索(包含、不包含、等于、不等于)
        $this->buildGoodsCondition($query, $orderSearchRequest);

        if (!empty($orderSearchRequest->waybillCodeList)) {
            $packageIdArr = Package::query()->whereIn('waybill_code', $orderSearchRequest->waybillCodeList)
                ->get(['id'])->pluck('id')->toArray();
            $tempOrderIdArr = PackageOrder::query()->whereIn('package_id', $packageIdArr)->get(['order_id'])->pluck('order_id')->toArray();
            $query->whereIn('orders.id', $tempOrderIdArr);
        }
        if (!empty($orderSearchRequest->wpCodeList)) {
//            $query->whereIn('orders.express_no', $orderSearchRequest->waybillCodeList);
            $query->whereHas('packages', function ($query) use ($orderSearchRequest) {
                $query->whereIn('wp_code', $orderSearchRequest->wpCodeList);
            });
        }

        if (!empty($orderSearchRequest->tidList)) {
            $tidList = OrderUtil::batchAppendOrderSuffix($orderSearchRequest->tidList);
            $this->accurateWhere[] = [
                'func' => 'whereIn',
                'args' => ['orders.tid', $tidList]
            ];
        }
        // 剩余发货时间
        if (!empty($orderSearchRequest->remainingDeliveryTime)) {
            if ($orderSearchRequest->remainingDeliveryTime == -1) {
//                $query->where('order_items.promise_ship_at', '<=', Carbon::now()->toDateTimeString());
                $this->accurateWhere[] = [
                    'func' => 'where',
                    'args' => ['order_items.promise_ship_at','<=', Carbon::now()->toDateTimeString()]
                ];
            } else {
                $tmpTime1 = Carbon::now()->toDateTimeString();
                $tmpTime2 = Carbon::now()->addHour($orderSearchRequest->remainingDeliveryTime)->toDateTimeString();
//                $query->where('order_items.promise_ship_at', '>=', $tmpTime1);
//                $query->where('order_items.promise_ship_at', '<=', $tmpTime2);
                $this->accurateWhere[] = [
                    'func' => 'whereBetween',
                    'args' => ['order_items.promise_ship_at', [$tmpTime1,$tmpTime2]]
                ];
            }
        }
        if (!empty($orderSearchRequest->authorIdList)) {
//            $query->whereIn('order_items.author_id', $orderSearchRequest->authorIdList);
            $this->accurateWhere[] = [
                'func' => 'whereIn',
                'args' => ['order_items.author_id', $orderSearchRequest->authorIdList]
            ];
        }
        if (!is_null($orderSearchRequest->refundStatus)) { // 空，1 有退款，2 无退款,3 无退款 包含部分退款
            if ($orderSearchRequest->refundStatus == 1) {
                $query->whereIn('orders.refund_status', [\App\Models\Order::REFUND_STATUS_YES, \App\Models\Order::REFUND_STATUS_PART]);
            } elseif ($orderSearchRequest->refundStatus == 0) {
                $query->whereIn('orders.refund_status', [\App\Models\Order::REFUND_STATUS_NO]);
            } else {
                $query->whereIn('orders.refund_status', [\App\Models\Order::REFUND_STATUS_NO, \App\Models\Order::REFUND_STATUS_PART]);
            }
        }
        // 预发货
        if ($orderSearchRequest->isPreShipmentList == 1) {
            $query->whereIn('orders.pre_shipment_status', [
                Order::PRE_SHIPMENT_STATUS_YES,
                Order::PRE_SHIPMENT_STATUS_PAUSE,
//                Order::PRE_SHIPMENT_STATUS_FINISHED
            ]);
        } elseif ($orderSearchRequest->isShippedList == 1) {

        } else { // 代发货列表
//            $query->where('orders.pre_shipment_status',Order::PRE_SHIPMENT_STATUS_NO);
            $query->where(function ($query) {
                $query->where('orders.pre_shipment_status', Order::PRE_SHIPMENT_STATUS_NO);
                $query->orWhere(function ($query) {
                    $query->whereIn('orders.pre_shipment_status', [
                        Order::PRE_SHIPMENT_STATUS_YES,
                        Order::PRE_SHIPMENT_STATUS_PAUSE,
//                      Order::PRE_SHIPMENT_STATUS_FINISHED
                    ]);
//                    $query->whereRaw('(order_items.send_remain_num-order_items.pre_send_num) > 0'); 会报错，优化如下
                    $query->where('order_items.refund_status',Order::REFUND_STATUS_NO);
                    $query->whereRaw('order_items.send_remain_num > order_items.pre_send_num');
                });

            });
        }

        // 显示异常订单
        if ($orderSearchRequest->showAbnormalType == 1) {
            $query->whereIn('orders.abnormal_type', AbnormalOrder::ABNORMAL_TYPE_ARRAY);
        }
        if(!empty($orderSearchRequest->logisticStatus)){
            $query->whereIn('packages.logistic_status', $orderSearchRequest->logisticStatus);
        }
        // 发货类型
        if($orderSearchRequest->deliveryType > 0){
            $this->buildDeliveryType($orderSearchRequest, $query);
        }
        // 指派类型
        if($orderSearchRequest->assignType != ''){
            $query->where('orders.assign_type', $orderSearchRequest->assignType);
        }

    }

    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @param Builder $query
     * @return void
     */
    private function buildByTime(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        $beginAt = $orderSearchRequest->beginAt;
        $endAt = $orderSearchRequest->endAt;
        $timeField = $orderSearchRequest->timeField;
        if (!in_array($orderSearchRequest->tabFlag, ['0', '1']) && empty($beginAt) && empty($endAt)) {
            $beginAt = date('Y-m-d 00:00:00', strtotime('-31 day'));
            $endAt = date('Y-m-d 23:59:59');
        }
        if (empty($beginAt) && empty($endAt)) { // 默认时间
            $timeField = 'pay_at';
            $beginAt = date('Y-m-d 00:00:00', strtotime('-61 day'));
            $endAt = date('Y-m-d 23:59:59');
        }
        if ($timeField && $beginAt && $endAt) {
            // 订单号忽略时间
            if (!(in_array($orderSearchRequest->selectItem, ['1', '19']) && !empty($search))) {
                $query->where('orders.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt)));
                $query->where('orders.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt)));
            }
        }
    }

    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @return array
     */
    private function getWhereArrByGroup(OrderSearchRequest $orderSearchRequest): array
    {
        $whereArr = $this->finalWhere;
        //筛出合并订单
        if ($this->onlyShowMergeOrder) {
            $whereArr[] = ['merge_orders_num', '>', 1];
        }
        //筛出非合并订单
        if ($this->notShowMergeOrder) {
            $whereArr[] = ['merge_orders_num', '=', 1];
        }
        $goodsNum = $this->goodsNum;
        //商品数量
        if ($goodsNum) {
            if ($goodsNum == 1) {
                $whereArr[] = ['goods_sum_num', '=', 1];
                $whereArr[] = ['merge_orders_num', '=', 1];
            } else if (is_array($goodsNum)) {
                $whereArr[] = ['goods_sum_num', '>=', $goodsNum[0]];
                $whereArr[] = ['goods_sum_num', '<=', $goodsNum[1]];
            } else {
                $whereArr[] = ['goods_sum_num', '=', $goodsNum];
            }
        }
        $ordersNum = $this->ordersNum;
        //订单数量
        if ($ordersNum) {
            if (is_array($ordersNum)) {
                $whereArr[] = ['merge_orders_num', '>=', $ordersNum[0]];
                $whereArr[] = ['merge_orders_num', '<=', $ordersNum[1]];
            } else {
                $whereArr[] = ['merge_orders_num', '=', $ordersNum];
            }
        }
        $ordersKind = $this->ordersKind;
        //商品种类
        if ($ordersKind) {
            if (is_array($ordersKind)) {
                $whereArr[] = ['unique_sku_num', '>=', $ordersKind[0]];
                $whereArr[] = ['unique_sku_num', '<=', $ordersKind[1]];
            } else {
                $whereArr[] = ['unique_sku_num', '=', $ordersKind];
            }
        }
        $salesAttributes = $orderSearchRequest->goodSkuNumType;
        if ($salesAttributes == 1) { //单商品单规格单件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['merge_orders_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '=', 1];
        } else if ($salesAttributes == 2) { //单商品单规格多件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 3) {//单商品多规格多件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 4) {//多商品多规格多件
            $whereArr[] = ['unique_goods_num', '>', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
        } else if ($salesAttributes == 5) {//多规格多件
            $whereArr[] = ['unique_sku_num', '>', 1];
        }

        //筛出同一人多个地址疑似订单
        if ($this->handToShowMergeOrder) {
            $whereArr[] = ['unique_address_num', '>', '1'];
        }

        $amountArr = $this->amountArr;
        if ($amountArr) {
            //实付金额
            if (isset($amountArr['payment'])) {
                if (is_array($amountArr['payment'])) {
                    $whereArr[] = ['merge_orders_payment', '>=', $amountArr['payment'][0]];
                    $whereArr[] = ['merge_orders_payment', '<=', $amountArr['payment'][1]];
                } else {
                    $whereArr[] = ['merge_orders_payment', '=', $amountArr['payment']];
                }
            }

            //订单金额
            if (isset($amountArr['total_fee'])) {
                if (is_array($amountArr['total_fee'])) {
                    $whereArr[] = ['merge_orders_total_fee', '>=', $amountArr['total_fee'][0]];
                    $whereArr[] = ['merge_orders_total_fee', '<=', $amountArr['total_fee'][1]];
                } else {
                    $whereArr[] = ['merge_orders_total_fee', '=', $amountArr['total_fee']];
                }
            }
        }
        return $whereArr;
    }

    /**
     * @param OrderSearchRequest $orderSearchRequest
     * @return array
     */
    private function getWhereArrBySingle(OrderSearchRequest $orderSearchRequest): array
    {
        $whereArr = $this->finalWhere;
        $goodsNum = $this->goodsNum;
        if ($goodsNum) {
            if ($goodsNum == 1) {
                $whereArr[] = ['order_items_num', '=', 1];
            } else if (is_array($goodsNum)) {
                $whereArr[] = ['goods_sum_num', '>=', $goodsNum[0]];
                $whereArr[] = ['goods_sum_num', '<=', $goodsNum[1]];

            } else {
                $whereArr[] = ['goods_sum_num', '=', $goodsNum];
            }
        }

        $ordersNum = $this->ordersNum;
        //订单数量
        if ($ordersNum) {
            if (is_array($ordersNum)) {
                $whereArr[] = ['merge_orders_num', '>=', $ordersNum[0]];
                $whereArr[] = ['merge_orders_num', '<=', $ordersNum[1]];
            } else {
                $whereArr[] = ['merge_orders_num', '=', $ordersNum];
            }
        }
        $ordersKind = $this->ordersKind;
        //商品种类
        if ($ordersKind) {
            if (is_array($ordersKind)) {
                $whereArr[] = ['unique_sku_num', '>=', $ordersKind[0]];
                $whereArr[] = ['unique_sku_num', '<=', $ordersKind[1]];
            } else {
                $whereArr[] = ['unique_sku_num', '=', $ordersKind];
            }
        }

        $salesAttributes = $orderSearchRequest->goodSkuNumType;
        if ($salesAttributes == 1) { //单商品单规格单件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '=', 1];
        } else if ($salesAttributes == 2) { //单商品单规格多件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 3) {//单商品多规格多件
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 4) {//多商品多规格多件
            $whereArr[] = ['unique_goods_num', '>', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
        } else if ($salesAttributes == 5) {//多规格多件
            $whereArr[] = ['unique_sku_num', '>', 1];
        }

        $amountArr = $this->amountArr;
        if ($amountArr) {
            //实付金额
            if (isset($amountArr['payment'])) {
                if (is_array($amountArr['payment'])) {
                    $whereArr[] = ['order_items_payment', '>=', $amountArr['payment'][0]];
                    $whereArr[] = ['order_items_payment', '<=', $amountArr['payment'][1]];
                } else {
                    $whereArr[] = ['order_items_payment', '=', $amountArr['payment']];
                }
            }

            //订单金额
            if (isset($amountArr['total_fee'])) {
                if (is_array($amountArr['total_fee'])) {
                    $whereArr[] = ['order_items_total_fee', '>=', $amountArr['total_fee'][0]];
                    $whereArr[] = ['order_items_total_fee', '<=', $amountArr['total_fee'][1]];
                } else {
                    $whereArr[] = ['order_items_total_fee', '=', $amountArr['total_fee']];
                }
            }
        }
        return $whereArr;
    }

    /**
     * 按需 懒join order_extras 表
     * @param Builder $queryBase
     * <AUTHOR>
     */
    private function handleLazyJoinOrderExtras(Builder $queryBase): void
    {
        foreach ($queryBase->getQuery()->wheres as $index => $where) {
            $column = $where['column'] ?? '';
            if (!empty($column) && strpos($column, 'order_extras.') !== false) {
                $queryBase->leftJoin('order_extras', 'orders.id', 'order_extras.order_id');
                break;
            }
        }
    }

    /**
     * 按需 懒join order_item_extras 表
     * @param Builder $queryBase
     * <AUTHOR>
     */
    private function handleLazyJoinOrderItemExtras(Builder $queryBase): void
    {
        foreach ($queryBase->getQuery()->wheres as $index => $where) {
            $column = $where['column'] ?? '';
            if (!empty($column) && strpos($column, 'order_item_extras.') !== false) {
                $queryBase->leftJoin('order_item_extras', 'order_items.id', 'order_item_extras.order_item_id');
                break;
            }
        }
    }
    private function handleFinalWhere($queryFinally, $whereArr)
    {
        foreach ($whereArr as $item) {
            if (!empty($item['func'])) {
                $this->handleAccurateWhere($queryFinally, [$item]);
            } else {
                $queryFinally->where(...$item);
            }
        }
    }

    /**
     * 处理精准查询
     * 在合单情况下要显示查询出来订单的合单
     * @param Builder|QueryBuilder $query
     * @param array $accurateWhere
     * <AUTHOR>
     */
    private function handleAccurateWhere($query, array $accurateWhere)
    {
//        Log::info("精确查询的条件", [$accurateWhere]);
        foreach ($accurateWhere as $where) {
            $func = $where['func'];
            if ($where['args'] instanceof Closure) {
                $query->$func($where['args']);
            } else {
                $query->$func(...$where['args']);
            }
        }
    }

    /**
     * @param $orderBy
     * @param $orderSort
     * @param Builder $queryBase
     * @param string $groupColumn
     * @param array $whereArr
     * @param OrderSearchRequest $orderSearchRequest
     * @return mixed
     * @throws ApiException
     */
    private function getCountByGroup($orderBy, $orderSort, Builder $queryBase, string $groupColumn, array $whereArr, OrderSearchRequest $orderSearchRequest)
    {
        $sort = $orderBy;


        switch ($sort) {
            case 'num':
                $sortColumn = "sum(`item-goods_num`)";
                break;
            default:
                $sortColumn = "MAX(orders.$sort)";
                if ($orderSort == 'asc') {
                    $sortColumn = "MIN(orders.$sort)";
                }
                break;
        }
        // 前置筛选出需要数量的 sql
        // 关联 order_items 忽略索引依赖
        $queryBase->join(DB::raw('order_items' . ' ignore index(idx_skuid)'), 'orders.id', 'order_items.order_id');
        $maxLimit = 50000;
        // 判断 $sort 是否包含 .
        if (in_array($sort, ['custom_title'])) {
            $queryBase->leftJoin('goods','order_items.num_iid','goods.num_iid');
            $queryBase->orderBy("goods.$sort", $orderSort);
            $selectSortColumn = "goods.$sort";
        }elseif (in_array($sort, ['custom_sku_value'])) {
            $queryBase->leftJoin('goods_skus','order_items.sku_id','goods_skus.sku_id');
            $queryBase->orderBy("goods_skus.$sort", $orderSort);
            $selectSortColumn = "goods_skus.$sort";
        } elseif (in_array($sort, ['outer_iid', 'outer_sku_iid'])) {
            $queryBase->orderBy("order_items.$sort", $orderSort);
            $selectSortColumn = "order_items.$sort";
        } else {
            $queryBase->orderBy("orders.$sort", $orderSort);
            $selectSortColumn = "orders.$sort";
        }
        $this->handleLazyJoinOrderExtras($queryBase);
        $this->handleLazyJoinOrderItemExtras($queryBase);
//        $queryBase->select(['orders.*','order_items.num_iid','order_items.sku_id']);
        $queryBase->select([$groupColumn]);
        // $queryInGroupColumn 用作 IN 查询出来的 $groupColumn，避免条件 合并订单+商品ID 出问题。
        $queryInGroupColumn = clone $queryBase;
        $this->handleAccurateWhere($queryBase, $this->accurateWhere);
        $baseSql = getSqlByQuery($queryBase);
        // 套一层子查询不会报错。
        $baseSqlSub = "select $groupColumn from ($baseSql) as base";

        if (Environment::isKs()){
            // ks 不知道为什么不走这个索引
            $queryInGroupColumn->fromRaw('orders USE INDEX (idx_shopid_orderstatus_payat_withdata)');
        }
        $queryInGroupColumn->whereRaw("$groupColumn in ($baseSqlSub)");
        $queryInGroupColumn->select([
            'orders.*',
            'order_items.id as item-id',
            'order_items.num_iid',
            'order_items.sku_id',
            'order_items.outer_iid',
            'order_items.outer_sku_iid',
            'order_items.payment as item-payment',
            'order_items.total_fee as item-total_fee',
            'order_items.goods_num as item-goods_num',
            'order_items.print_status as item-print_status',
            'order_items.refund_status as item-refund_status',
            "$selectSortColumn as sort_column",
        ]);

        // 查询分组取出 $groupColumn
        $sqlInGroupColumn = $queryInGroupColumn->toSql();
        $queryGroup = \DB::query()->fromRaw("($sqlInGroupColumn) as orders", $queryInGroupColumn->getBindings());
        $groupBindings = [
            $groupColumn,
            "COUNT(distinct orders.id) as merge_orders_num",
            "COUNT(distinct $groupColumn) as distinct_merge_orders_num",
            "$sortColumn as sort_column",
            'count(distinct num_iid) as unique_goods_num',
            'count(distinct sku_id) as unique_sku_num',
            'sum(`item-goods_num`) as goods_sum_num',
            'count(`item-id`) as sku_sum_num',
//            'sum(sku_num) as sku_sum_num',
            'count(distinct receiver_address) as unique_address_num',
            'count(address_md5) as address_md5_num',
            'sum(`item-payment`) as merge_orders_payment',
            'sum(`item-total_fee`) as merge_orders_total_fee',
            'GROUP_CONCAT(orders.id) as order_ids',
            'smart_logistics',
        ];
        for ($groupSelectArr = [], $i = 0; $i < count($groupBindings); $i++) $groupSelectArr[] = '%s';
        $selectRawStr = sprintf(implode(',', $groupSelectArr), ...$groupBindings);
        $queryGroup->where($this->groupWhere);
        $queryByOrderCount = clone $queryGroup;
        $queryGroup->selectRaw($selectRawStr);
//		$queryGroup->join('order_items', 'orders.id', 'order_items.order_id');
        $queryGroup->groupBy([$groupColumn])->orderBy("sort_column", $orderSort)->limit($maxLimit);
        $sql = getSqlByQuery($queryGroup);
        $selectRawStr = "$groupColumn, sort_column, merge_orders_num,distinct_merge_orders_num, unique_goods_num, unique_sku_num,
        goods_sum_num, sku_sum_num, unique_address_num ,address_md5_num,merge_orders_payment ,merge_orders_total_fee,
        order_ids,smart_logistics";
        $queryFinally = \DB::query()
            ->fromRaw("($sql) as b")
            ->selectRaw($selectRawStr)
//            ->where($whereArr)
            ->orderBy('sort_column', $orderSort);

        $this->handleFinalWhere($queryFinally, $whereArr);

//        $sql = sprintf("SELECT  FROM (%s) as b where %s ORDER BY `sort_column` %s", $sql, $whereStr, $orderSort);
        $sql = getSqlByQuery($queryFinally);
        if ($orderSearchRequest->tabFlag == OrderIndexTabConst::PRINTED){
            // 已打印的 tab 需要把未打印查出来,使用 preg_replace() 只替换第一次出现的内容
            $sql = preg_replace('/and `orders`\.`print_status` in \("1\", \"2\"\)/', 'and `orders`.`print_status` in ("0", "1", "2")', $sql, 1);
        }
        // max_execution_time sql执行超时时间（毫秒）
        $selectSql = sprintf('select /*+ max_execution_time(20000)*/ count(*) as count from (%s) as c', $sql);
        $count = \DB::selectOne($selectSql);
//        \Log::info("selectSql=" . $selectSql);
        if (empty($count)) {
            throw new ApiException(ErrorConst::SYSTEM_DB_EXECUTION_TIMEOUT);
        }
        if (in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::ALL, OrderIndexTabConst::UNPRINT, OrderIndexTabConst::PRINTED, OrderIndexTabConst::LOCKED])) {
            $this->orderCount = $queryByOrderCount->count(DB::raw('distinct orders.id'));
        }
        $this->sql = $sql;
        return $count;
    }

    /**
     * @param Builder $queryBase
     * @param $orderBy
     * @param $orderSort
     * @param array $whereArr
     * @return mixed
     * @throws ErrorCodeException
     */
    private function getCountBySingle(Builder $queryBase, $orderBy, $orderSort, array $whereArr)
    {
        // 前置筛选出需要数量的 sql
        $queryBase->join(DB::raw('order_items' . ' ignore index(idx_skuid)'), 'orders.id', 'order_items.order_id');
        $maxLimit = 50000;
        if (in_array($orderBy, ['custom_title'])) {
            $queryBase->leftJoin('goods','order_items.num_iid','goods.num_iid');
            $queryBase->orderBy("goods.$orderBy", $orderSort);
            $selectSortColumn = "goods.$orderBy";
        }elseif (in_array($orderBy, ['custom_sku_value'])) {
            $queryBase->leftJoin('goods_skus','order_items.sku_id','goods_skus.sku_id');
            $queryBase->orderBy("goods_skus.$orderBy", $orderSort);
            $selectSortColumn = "goods_skus.$orderBy";
        } elseif (in_array($orderBy, ['outer_iid', 'outer_sku_iid'])) {
            $queryBase->orderBy("order_items.$orderBy", $orderSort)->limit($maxLimit);
            $selectSortColumn = "order_items.$orderBy";
        } else {
            $queryBase->orderBy("orders.$orderBy", $orderSort)->limit($maxLimit);
            $selectSortColumn = "orders.$orderBy";
        }
        $this->handleLazyJoinOrderExtras($queryBase);
        $this->handleLazyJoinOrderItemExtras($queryBase);
        $queryBase->select([
            'orders.*',
            'order_items.num_iid',
            'order_items.sku_id',
            'order_items.outer_iid',
            'order_items.outer_sku_iid',
            'order_items.print_status as item-print_status',
            'order_items.refund_status as item-refund_status',
            "$selectSortColumn as sort_column",
        ]);
        $queryInGroupColumn = clone $queryBase;
//        $this->handleAccurateWhere($queryBase, $this->accurateWhere);
        $this->handleAccurateWhere($queryInGroupColumn, $this->accurateWhere);
//        $baseSql = getSqlByQuery($queryBase);
        // 套一层子查询不会报错。
//        $queryInGroupColumn->whereRaw("orders.id in (select id from ($baseSql) as base)");
        if (Environment::isKs()){
            // ks 不知道为什么不走这个索引
            $queryInGroupColumn->fromRaw('orders USE INDEX (idx_shopid_orderstatus_payat_withdata)');
        }

        // 查询分组取出 $groupColumn
        $sqlInGroupColumn = $queryInGroupColumn->toSql();
        $queryGroup = \DB::query()->fromRaw("($sqlInGroupColumn) as orders", $queryInGroupColumn->getBindings());
        $groupBindings = [
            'COUNT(distinct orders.id) as merge_orders_num',
            "orders.id as order_ids",
            "receiver_phone",
            "num",
            "sku_num",
            "COUNT(1) as order_items_num",
            "orders.$orderBy as sort_column",
            "count(distinct num_iid) as unique_goods_num",
            "count(distinct sku_id) as unique_sku_num",
            "num as goods_sum_num",
            "sku_num as sku_sum_num",
            "count(distinct receiver_address) as unique_address_num",
            "count(address_md5) as address_md5_num",
            "orders.payment as order_items_payment",
            "orders.total_fee as order_items_total_fee",
            "smart_logistics",
        ];
        for ($groupSelectArr = [], $i = 0; $i < count($groupBindings); $i++) $groupSelectArr[] = '%s';
        $selectRawStr = sprintf(implode(',', $groupSelectArr), ...$groupBindings);
        $queryGroup->where($this->groupWhere);
        $queryGroup->selectRaw($selectRawStr);
//		$queryGroup->join('order_items', 'orders.id', 'order_items.order_id');
        $queryGroup->groupBy(['orders.id'])->orderBy("sort_column", $orderSort)->limit($maxLimit);
//        $bindings = $queryGroup->getBindings();
//        $sql       = str_replace('%', '%%', $queryGroup->toSql());
//        $sql       = str_replace('?', '"%s"', $sql);
//        $sql       = sprintf($sql, ...$bindings);
        $sql = getSqlByQuery($queryGroup);
        $selectRawStr = 'order_ids, receiver_phone, sort_column, order_items_num, unique_goods_num, unique_sku_num,
        goods_sum_num, sku_sum_num, unique_address_num ,address_md5_num,order_items_payment , order_items_total_fee,smart_logistics';
//		$sql = sprintf("SELECT $selectRawStr FROM (%s) as b where %s ORDER BY `sort_column` %s", $sql, $whereStr, $orderSort);
        $queryFinally = \DB::query()
            ->fromRaw("($sql) as b")
            ->selectRaw($selectRawStr)
            ->where($whereArr)
            ->orderBy('sort_column', $orderSort);
        $sql = getSqlByQuery($queryFinally);

        $selectSql = sprintf('select /*+ max_execution_time(20000)*/ count(*) as count from (%s) as c', $sql);
        $count = \DB::selectOne($selectSql);
        if (!isset($count)) {
            Log::debug('getCountBySingle 返回错误：',[$count,$selectSql]);
            throw_error_code_exception(StatusCode::TOO_MANY_RECORD);
        }
        $this->sql = $sql;
        return $count;
    }

    /**
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * @param string $groupColumn
     * @param int $limit
     * @param int $offset
     * @return array|void
     * @throws ApiException
     * @throws ErrorCodeException
     */
    private function getQueryByDisplayMerge(Builder &$query, OrderSearchRequest $orderSearchRequest, string $groupColumn, int $limit, int $offset): void
    {
        $queryBase = clone $query;

        $orderBy = $this->sortArr[0] ?? 'pay_at';
        $orderSort = $this->sortArr[1] ?? 'desc';
        if ($orderSearchRequest->displayMerge) {
//            $queryBase2 = clone $queryBase;
            // 未发货这边要查询出总的订单数
//            if (in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::ALL, OrderIndexTabConst::UNPRINT, OrderIndexTabConst::PRINTED, OrderIndexTabConst::LOCKED])) {
//                $whereArr2 = $this->getWhereArrBySingle($orderSearchRequest);
//                $count2 = $this->getCountBySingle($queryBase2, $orderBy, $orderSort, $whereArr2);
//                $this->orderCount = $count2->count;
//            }
            $whereArr = $this->getWhereArrByGroup($orderSearchRequest);
            $count = $this->getCountByGroup($orderBy, $orderSort, $queryBase, $groupColumn, $whereArr, $orderSearchRequest);
            $this->buyerCount = $count->count;
            Log::info('未发货订单数SqLX：', [$queryBase->toSql()]);
            // count 接口直接返回
            if ($orderSearchRequest->isCountApi){
                $this->count = $count->count;
                return;
            }
        } else {
            $queryBase2 = clone $queryBase;
            // count 接口直接返回
            if ($orderSearchRequest->isCountApi){
                $whereArr = $this->getWhereArrBySingle($orderSearchRequest);
                $count = $this->getCountBySingle($queryBase, $orderBy, $orderSort, $whereArr);
                $this->count = $count->count;
                return;
            }
            // 未发货这边要查询出总的订单数
            // 降低查询速度
//            if (in_array($orderSearchRequest->tabFlag, [OrderIndexTabConst::ALL, OrderIndexTabConst::UNPRINT, OrderIndexTabConst::PRINTED, OrderIndexTabConst::LOCKED])) {
//                $whereArr2 = $this->getWhereArrByGroup($orderSearchRequest);
//                $count2 = $this->getCountByGroup($orderBy, $orderSort, $queryBase2, $groupColumn, $whereArr2, $orderSearchRequest);
//                Log::info('买家数SqL：', [$queryBase2->toSql()]);
//                $this->buyerCount = $count2->count;
//            }

            $whereArr = $this->getWhereArrBySingle($orderSearchRequest);
            $count = $this->getCountBySingle($queryBase, $orderBy, $orderSort, $whereArr);
            $this->orderCount = $count->count;
            $this->buyerCount = $count->count;
            Log::info('未发货订单数SqL：', [$queryBase2->toSql()]);
        }

        $this->count = $count->count;
        if ($limit <= 0) {
            return;
        }
        $slectSql = $this->sql . ' limit ' . $limit . ' offset ' . $offset;
        Log::info("订单查询sql:".$slectSql);
        $groupResult = \DB::select($slectSql);
//                $groupColumnArr = collect($groupResult)->pluck($groupColumn);
        $orderIdArr = [];
        collect($groupResult)->pluck('order_ids')->map(function ($item) use (&$orderIdArr) {
            $arr = explode(',', $item);
            $orderIdArr = array_merge($orderIdArr, $arr);
        });
        $orderIdArr = array_unique($orderIdArr);
        // 批量转成 int
        $orderIdArr = array_map(function ($item) {
            return (int)$item;
        }, $orderIdArr);

        // limit 是为了防止有的手机号下几百单导致订单溢出
        $query = \App\Models\Order::query();
        $query->selectRaw('/*+ max_execution_time(20000) */ orders.*');
        if (in_array($orderBy, ['outer_iid', 'outer_sku_iid'])) {
            $query->leftJoin('order_items', 'orders.id', 'order_items.order_id')
                ->select(['orders.*', 'order_items.outer_iid', 'order_items.outer_sku_iid'])
                ->groupBy(['orders.id']);
        }
        //如果没有查出订单ID来 就是这个whereIn就会生成0=1的查询条件，正好查不出数据室
        $query->whereIn('orders.id', $orderIdArr);

        $query->limit(6000);
        $queryOrders = (array)$query->getQuery()->orders;
        foreach ($queryOrders as $queryOrder) {
            if ($queryOrder['type'] == 'Raw') {
                $query->orderByRaw($queryOrder['sql']);
            }
        }
        $query->orderBy($orderBy, $orderSort);

//        return $orderIdArr;
    }

    /**
     * @return int
     */
    public function getCount(): int
    {
        return $this->count;
    }

//    /**
//    /** 方法迁移到了StrUtil::extractedNumOrNumArr
//     * 提取数字或者数字范围 1 or 1-10
//     * @param string $input
//     * @return int|float|string[]|null
//     */
//    public function extractedNumOrNumArr(string $input)
//    {
//        $value = null;
//        if (is_numeric($input)) {
//            $value = $input;
//        } else {
//            $searchArr = explode('-', $input);
//            if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
//                $value = [$searchArr[0], $searchArr[1]];
//            }
//        }
//        return $value;
//    }

    public function getOrderCount(): int
    {
        return $this->orderCount;
    }

    public function getBuyerCount(): int
    {
        return $this->buyerCount;
    }

    /**
     * 商品模糊搜索(包含、不包含、等于、不等于)
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * @return void
     * @throws ErrorCodeException
     */
    public function buildGoodsCondition(Builder $query, OrderSearchRequest $orderSearchRequest): void
    {
        // 商品相关查询
        $goodsFieldArr = ['goodsTitle', 'customTitle', 'outerIid', 'numIid', 'skuValue', 'customSkuValue', 'outerSkuIid', 'skuId'];
        foreach ($goodsFieldArr as $field) {
            if (!empty($orderSearchRequest->$field)) {
                $value = $orderSearchRequest->$field;
                //如果是goodsTitle,customTitle,outerIid,numIid operator取 $orderSearchRequest->goodsQueryOperator
                //如果是skuValue,customSkuValue,outerSkuIid,skuId operator取 $orderSearchRequest->$skuQueryOperator
                if(in_array($field, ['goodsTitle', 'customTitle', 'outerIid', 'numIid'])){
                    $operator = $orderSearchRequest->goodsQueryOperator;
                }
                elseif(in_array($field, ['skuValue', 'customSkuValue', 'outerSkuIid', 'skuId'])){
                    $operator = $orderSearchRequest->skuQueryOperator;
                }else{
                    throw_error_code_exception(ErrorConst::PARAM_ERROR, null,'goodsQueryOperator和skuQueryOperator只能是include、notInclude、equal、notEqual');
                }

//                $operator = $orderSearchRequest->goodsQueryOperator;
                switch ($operator) {
                    case 'include':
                        $operator = 'like';
                        $value = "%{$value}%";
                        $customGoodsOperator = 'like';
                        $customGoodsFunc = 'whereIn';
                        break;
                    case 'notInclude':
                        $operator = 'not like';
                        $value = "%{$value}%";
                        $customGoodsOperator = 'like';
                        $customGoodsFunc = 'whereNotIn';
                        break;
                    case 'equal':
                    default:
                        $operator = '=';
                        $customGoodsOperator = '=';
                        $customGoodsFunc = 'whereIn';
                        break;
                    case 'notEqual':
                        $operator = '!=';
                        $customGoodsOperator = '=';
                        $customGoodsFunc = 'whereNotIn';
                        break;
                }

                if ($field == 'customSkuValue') {
                    $goodsSkuIdArr = GoodsSku::query()->whereIn('shop_id', $orderSearchRequest->shopIds)
                        ->where('custom_sku_value', $customGoodsOperator, $value)->pluck('sku_id')->toArray();
                    $query->$customGoodsFunc('order_items.sku_id', $goodsSkuIdArr);
                } elseif ($field == 'customTitle') {
                    $goodsIdArr = Goods::query()->whereIn('shop_id', $orderSearchRequest->shopIds)
                        ->where('custom_title', $customGoodsOperator, $value)->pluck('num_iid')->toArray();
                    $query->$customGoodsFunc('order_items.num_iid', $goodsIdArr);
                } else {
                    // 小驼峰转为下划线
                    $query->where('order_items.' . Str::snake($field), $operator, $value);
                }
            }
        }
    }

    /**
     * 订单总价查询
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * @return void
     */
    private function buildOrderTotalFee(Builder $query, OrderSearchRequest $orderSearchRequest)
    {
        $orderTotalFee = $orderSearchRequest->getOrderTotalFee();
        if(is_array($orderTotalFee)){
            $query->whereBetween('orders.total_fee', $orderTotalFee);
        }
        if(is_numeric($orderTotalFee)){
            $query->where('orders.total_fee', $orderSearchRequest->getOrderTotalFee());
        }
    }

    /**
     * 订单实付金额查询
     * @param Builder $query
     * @param OrderSearchRequest $orderSearchRequest
     * @return void
     */
    private function buildPayment(Builder $query, OrderSearchRequest $orderSearchRequest): void{
        $payment = $orderSearchRequest->getPayment();
        if($payment){
            $query->where('orders.payment', $payment);
        }
        if(is_numeric($payment)){
            $query->where('orders.payment', $payment);
        }
    }

    public function buildGoodsNum(Builder $query, OrderSearchRequest $orderSearchRequest): void{
        $goodsNum = $orderSearchRequest->getGoodsNum();
        //这个地方只是增加查询窗口函数的值，不进行过滤，过滤等到后面用代码去过滤
        if($goodsNum){
            $query->addSelect(DB::raw('SUM(order_items.goods_num) over (PARTITION BY orders.id )  as order_goods_num'));
        }

    }


    public function filterGoodsNum(Collection &$collection,OrderSearchRequest $orderSearchRequest): void{
        $goodsNum = $orderSearchRequest->getGoodsNum();
        if(!$goodsNum) {
            return ;
        }
        if(is_array($goodsNum)){
            $collection = $collection->filter(function ($item) use($goodsNum){

                return $item['order_goods_num'] >= $goodsNum[0] && $item['order_goods_num'] <=$goodsNum[1];

            });
        }
        if(is_numeric($goodsNum)){
            $collection = $collection->filter(function ($item) use($goodsNum){
                return $item['order_goods_num'] == $goodsNum;
            });
        }
        Log::info('过滤后的数量：'.$collection->count());
    }

    public function buildOrderBizType(Builder $query, OrderSearchRequest $orderSearchRequest): void{
        $orderBizType = $orderSearchRequest->orderBizType;
        if(isset($orderBizType)){
            $query->where('order_extras.order_biz_type', $orderBizType);
        }

    }
    public function buildPreSaleStatus(Builder $query, OrderSearchRequest $orderSearchRequest): void{
        $preSaleStatus = $orderSearchRequest->isPreSale;
        if(isset($preSaleStatus)){
            $query->where('orders.is_pre_sale', $preSaleStatus);
        }
    }

    /**
     * 商品搜索
     * @param OrderSearchRequest $orderSearchRequest
     * @param Builder $query
     * @return void
     */
    public function buildDeliveryType(OrderSearchRequest $orderSearchRequest, Builder $query): void
    {
        $order2Service = new Order2Service();
        if ($orderSearchRequest->deliveryType == Package::DELIVERY_TYPE_EXTERNAL) {
//            $query->whereHas('ptLogistics', function ($query) use ($orderSearchRequest, $order2Service) {
//                $order2Service->buildDeliveryTypeQuery($query, $orderSearchRequest->delivery_type);
//            });
            $subQuery = PtLogistics::query()->selectRaw('DISTINCT order_id');
            $order2Service->buildDeliveryTypeQuery($subQuery, $orderSearchRequest->deliveryType);
            $query->joinSub($subQuery,'pt_logistics', 'orders.id', '=', 'pt_logistics.order_id');
        } else {
            $query->whereHas('packages', function ($query) use ($orderSearchRequest, $order2Service) {
                $order2Service->buildDeliveryTypeQuery($query, $orderSearchRequest->deliveryType);
            });
        }
    }

    public function buildGoodsSearch(Builder $query,OrderSearchRequest  $orderSearchRequest):void{
        $goodsTitle=$orderSearchRequest->goodsTitle;
        $customTitle=$orderSearchRequest->customTitle;
        $numIid=$orderSearchRequest->numIid;
        $outerIid=$orderSearchRequest->outerIid;
        $goodsInclude=$orderSearchRequest->goodsInclude;
        //如果全部为空就不之行
        if(empty($goodsTitle)&&empty($customTitle)&&empty($numIid)&&empty($outerIid)){
            return ;
        }
        if ($goodsTitle||$numIid||$outerIid){
            //商品标题1: 模糊包含 2: 精确包含    3: 不包含
            if($goodsInclude==1){
                $query->whereHas('orderItem', function ($query) use ($goodsTitle,$numIid,$outerIid) {
                    if(!empty($goodsTitle)) {
                        $query->where('goods_title', 'like', '%'.$goodsTitle.'%');
                    }
                    if(!empty($numIid)){
                        $query->where('num_iid','like', $numIid);
                    }
                    if(!empty($outerIid)){
                        $query->where('outer_goods_id','like', $outerIid);
                    }
                });
            }
            if($goodsInclude==2){
                $query->whereHas('orderItem', function ($query) use ($goodsTitle,$numIid,$outerIid) {
                    if(!empty($numIid)){
                        $query->where('num_iid',$numIid);
                    }
                    if(!empty($goodsTitle)) {
                        $query->where('goods_title', $goodsTitle);
                    }
                    if(!empty($outerIid)){
                        $query->where('outer_goods_id', $outerIid);
                    }
                });
            }
            if($goodsInclude==3){
                $query->whereHas('orderItem', function ($query) use ($goodsTitle,$numIid,$outerIid) {
                    if(!empty($numIid)){
                        $query->where('num_iid', 'not like', '%' . $numIid . '%');
                    }
                    if(!empty($goodsTitle)) {
                        $query->where('goods_title', 'not like', '%' . $goodsTitle . '%');
                    }
                    if(!empty($outerIid)){
                        $query->where('outer_goods_id', '!=', $outerIid );
                    }
                });
            }
        }
        if ($customTitle){
            $goodsQuery=Goods::query()->whereIn('shop_id',$orderSearchRequest->shopIds);
            //商品简称1: 模糊包含 2: 精确包含    3: 不包含
            //商品简称需要跟goods表
            if($goodsInclude==1){
                $goodsQuery->where('custom_title', 'like', '%' . $customTitle . '%');
            }
            if($goodsInclude==2){
                $goodsQuery->where('custom_title', $customTitle);
            }
            if($goodsInclude==3){
                $goodsQuery->where('custom_title', '!=',  $customTitle );
            }
            $numIids=$goodsQuery->select('num_iid')->get()->pluck('num_iid')->toArray();
            Log::info('商品简称查询结果：',[$customTitle,$goodsInclude,$numIids]);
            if(!empty($numIids)) {
                $query->whereHas('orderItem', function ($query) use ($numIids) {
                    $query->whereIn('num_iid', $numIids);
                });
            }

        }


    }

    /**
     * sku搜索
     * @param  OrderSearchRequest  $orderSearchRequest
     * @param  Builder  $query
     * @return void
     */
    public function buildSkuSearch(Builder $query,OrderSearchRequest  $orderSearchRequest):void{

        $skuValue=$orderSearchRequest->skuValue;
        $customSkuValue=$orderSearchRequest->customSkuValue;
        $skuInclude=$orderSearchRequest->skuInclude;
        $outerSkuIid=$orderSearchRequest->outerSkuIid;
        $skuId=$orderSearchRequest->skuId;
        //如果全部为空就不之行
        if(empty($skuValue)&&empty($customSkuValue)&&empty($skuId)&&empty($outerSkuIid)){
            return ;
        }
        if ($skuValue||$skuId||$outerSkuIid){
            if($skuInclude==1){
                $query->whereHas('orderItem', function ($query) use ($skuValue,$skuId,$outerSkuIid) {
                    if(!empty($skuValue)) {
                        $query->where('sku_value', 'like', '%'.$skuValue.'%');
                    }
                    if(!empty($skuId)){
                        $query->where('sku_id','like',$skuId);
                    }
                    if(!empty($outerSkuIid)){
                        $query->where('outer_sku_id','like', $outerSkuIid);
                    }
                });
            }
            if($skuInclude==2){
                $query->whereHas('orderItem', function ($query) use ($skuValue,$skuId,$outerSkuIid) {
                    if(!empty($skuId)){
                        $query->where('sku_id',$skuId);
                    }
                    if(!empty($skuValue)) {
                        $query->where('sku_value', $skuValue);
                    }
                    if(!empty($outerSkuIid)){
                        $query->where('outer_sku_id', $outerSkuIid);
                    }
                });
            }
            if($skuInclude==3){
                $query->whereHas('orderItem', function ($query) use ($skuValue,$skuId,$outerSkuIid) {
                    if(!empty($skuId)){
                        $query->where('sku_id', 'not like', '%' . $skuId . '%');
                    }
                    if(!empty($skuValue)) {
                        $query->where('sku_value', 'not like', '%' . $skuValue . '%');
                    }
                    if(!empty($outerSkuIid)){
                        $query->where('outer_sku_id',"!=",  $outerSkuIid );
                    }
                });
            }
        }
        if($customSkuValue){
            $skuQuery=GoodsSku::query()->whereIn('shop_id',$orderSearchRequest->shopIds);
            if($skuInclude==1){
                $skuQuery->where('custom_sku_value', 'like', '%' . $customSkuValue . '%');
            }
            if($skuInclude==2){
                $skuQuery->where('custom_sku_value', $customSkuValue);
            }
            if($skuInclude==3){
                $skuQuery->where('custom_sku_value', '!=', $customSkuValue );
            }
            $skuIds=$skuQuery->select('sku_id')->get()->pluck('sku_id')->toArray();
            Log::info('sku简称查询结果：',[$customSkuValue,$skuInclude,$skuIds]);
            if(!empty($skuIds)) {
                $query->whereHas('orderItem', function ($query) use ($skuIds) {
                    $query->whereIn('sku_id', $skuIds);
                });
            }
        }
    }


}
