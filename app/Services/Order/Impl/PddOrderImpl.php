<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:29
 */

namespace App\Services\Order\Impl;


use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Constants\SubscribeMessageType;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Client\PddClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Utils\ArrayUtil;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\ExpressCompanyUtil;
use Exception;
use Illuminate\Support\Facades\Log;
use Workerman\Connection\AsyncTcpConnection;
use Workerman\Worker;
use Carbon\Carbon;

class PddOrderImpl extends AbstractOrderService
{

    /**
     * PDD两个OAID的分割符
     */

    protected $platformType = PlatformConst::PLATFORM_TYPE_PDD;

    protected $orderStatusMap = [
        1 => Order::ORDER_STATUS_PAYMENT,
        2 => Order::ORDER_STATUS_DELIVERED,
        3 => Order::ORDER_STATUS_RECEIVED,
    ];


//退款状态，枚举值：1：无售后或售后关闭，2：售后处理中，3：退款中，4： 退款成功
    protected $refundStatusMap = [
        1 => Order::REFUND_STATUS_NO,
        2 => Order::REFUND_STATUS_YES,
        3 => Order::REFUND_STATUS_YES,
        4 => Order::REFUND_STATUS_YES,// 商家同意退款，退款中

    ];

    protected $orderFlagMap = [
        1 => Order::FLAG_RED,
        2 => Order::FLAG_YELLOW,
        3 => Order::FLAG_GREEN,
        4 => Order::FLAG_BLUE,
        5 => Order::FLAG_PURPLE,
        0 => Order::FLAG_GRAY, // 反转的时候替代
    ];

    /**
     * 订单旗帜映射
     */
    const ORDER_FLAG_MAP_REVERT = [
        Order::FLAG_RED => 1,
        Order::FLAG_YELLOW => 2,
        Order::FLAG_GREEN => 3,
        Order::FLAG_BLUE => 4,
        Order::FLAG_PURPLE => 5,
    ];

    const ORDER_FLAG_NAME_MAP_REVERT = [
        Order::FLAG_RED => "红色",
        Order::FLAG_YELLOW => "黄色",
        Order::FLAG_GREEN => "绿色",
        Order::FLAG_BLUE => "蓝色",
        Order::FLAG_PURPLE => "紫色",

    ];

    //物流状态码
    const TRACE_ARR = [
        'GOT',
        'SEND',
        'SIGN',
        'ARRIVAL',
        'DEPARTURE',
        'FAIL',
        'REJECTION',
        'STAY_IN_WAREHOUSE',
        'SIGN_ON_BEHALF',
        'OTHER',
        'RETURN',
        'IN_CABINET ',
        'OUT_CABINET ',
    ];

    /**
     * 每次拉取订单间隔的分钟
     * 最大间隔是24小时
     * @var int
     */
    public $orderTimeInterval = 1440;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 30;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 30;

    public function formatToAfterSale(array $trade)
    {

    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        $orderItems = [];
        $tradePayment = $trade['pay_amount'];
        $orderPaymentSum = "0";
        $orderItemTotalFee = "0"; //订单行的总金额，需要把item_list里面的goods_price*goods_count累加
        $itemList = $trade['item_list'];
        $orderAmountSum = bcround(array_reduce($itemList, function ($carry, $item) {
            return round_bcadd($carry, round_bcmul($item['goods_price'], $item['goods_count']), 3, PHP_ROUND_HALF_UP);
        }, "0"), 2);
        $sumTotalFee = "0";
        $sumPayment = "0";
        $tradeTotalFee = $trade['goods_amount'];
        $tid = (string)$trade['order_sn'];
        $oid = $tid;
        foreach ($itemList as $index => $orderItem) {
            //这个是订单行的总金额
            $total = round_bcmul($orderItem['goods_price'], $orderItem['goods_count']);
            $totalFee = "0";
            $payment = "0";
            if ($index != sizeof($itemList) - 1) {
                $totalFee = round_bcmul($tradeTotalFee, round_bcdiv($total, $orderAmountSum));
                $sumTotalFee = round_bcadd($sumTotalFee, $totalFee);
                $payment = round_bcmul($tradePayment, round_bcdiv($totalFee, $tradeTotalFee));
                $sumPayment = round_bcadd($sumPayment, $payment);
            } else {
                $totalFee = round_bcsub($tradeTotalFee, $sumTotalFee);
                $payment = round_bcsub($tradePayment, $sumPayment);
            }

            $skuValue = $orderItem['goods_spec'] ?? '';
            $skuValueArr = explode(',', $skuValue); // 黑色背心,150cm
            $skuValue = str_replace(',', ';', $skuValue);
            $skuValue1 = $skuValueArr[0] ?? '';
            $skuValue2 = $skuValueArr[1] ?? '';

            $orderItems[] = [
                "tid" => $tid, //主订单
                "oid" => $tid, //子订单号
                "type" => $this->platformType, //订单类型
                "payment" => $payment,//平台没有订单支付金额，所以需要计算，计算的逻辑是把订单的payment按订单的总金额的比例进行分割，最后一个订单把剩余金额填充进去
                "total_fee" => $totalFee, //总金额
                "discount_fee" => 0, //优惠金额
                "goods_pic" => $orderItem['goods_img'], //商品图片
                "goods_title" => $orderItem['goods_name'], //商品标题
                "goods_price" => $orderItem['goods_price'], //商品单价
                "goods_num" => $orderItem['goods_count'], //商品数量
                "num_iid" => $orderItem['goods_id'], //商品id
                "sku_id" => $orderItem['sku_id'] ?? '', //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => $item['outer_goods_id'] ?? '', //商家外部商品编码
                "outer_sku_iid" => $item['outer_id'] ?? '', //商家外部sku编码
                "order_created_at" => $trade['created_time'], //订单创建时间
                "order_updated_at" => $trade['updated_at'], //订单修改时间
                "refund_id" => 0, //
                "refund_status" =>$this->formatRefundStatus($trade['refund_status']),
                "refund_sub_status"=>$this->formatRefundSubStatus($trade['refund_status']),
                "status" => $this->formatOrderStatus($trade['order_status']), //订单状态
            ];
        }

        $sellerMemo = empty($trade['remark']) ? [] : [$trade['remark']];


        $consolidateType = array_get($trade, 'consolidate_info.consolidate_type');
        //把trade['open_address_id']，[open_address_id_2]拼接起来， 如果有的话
        $oaid = array_get($trade, 'open_address_id');
        $address_id2 = array_get($trade, 'open_address_id_2');
        if ($oaid) {
            if (!empty($address_id2)) {
                $oaid .= OrderCipherInfo::PDD_OAID_SEPARATOR . array_get($trade, 'open_address_id_2');
            }
        }
        $receiver_phone = md5(implode('_', [$trade['province'], $trade['city'],
            $trade['town'], $trade['receiver_address_mask'], $trade['receiver_name_mask'], $trade['receiver_phone_mask']]));
        $cipher_info = [
            'receiver_phone_ciphertext' => $trade['receiver_phone'],
            'receiver_name_ciphertext' => $trade['receiver_name'],
            'receiver_address_ciphertext' => $trade['receiver_address'],
            'receiver_phone_mask' => $trade['receiver_phone_mask'],
            'receiver_name_mask' => $trade['receiver_name_mask'],
            'receiver_address_mask' => $trade['receiver_address_mask'],
            'oaid' => $oaid,
        ];
        $logistics_data = [];
        $trackingNumber = array_get($trade, 'tracking_number');
        if ($trackingNumber) {

            $pddLogistics = ExpressCompanyUtil::getPddLogisticsById($trade['logistics_id']);
            $logistics_data[] = LogisticsDataBo::ofPddLogisticsData($tid, $oid, $trackingNumber, $itemList, $pddLogistics['logistics_company'], $pddLogistics['code']);
        }
        $extraDeliveryList = array_get($trade, 'extra_delivery_list');
        if (!empty($extraDeliveryList)) {
            foreach ($extraDeliveryList as $extraDelivery) {
                $pddLogistics = ExpressCompanyUtil::getPddLogisticsById($extraDelivery['logistics_id']);
                $logistics_data[] = LogisticsDataBo::ofPddLogisticsData($tid, $oid, $extraDelivery['tracking_number'], $itemList, $pddLogistics['logistics_company'], $pddLogistics['code']);
            }
        }
        $giftDeliveryList = array_get($trade, 'gift_delivery_list');
        if (!empty($giftDeliveryList)) {
            $giftList = array_get($trade, 'gift_list');
            foreach ($giftDeliveryList as $giftDelivery) {
                $pddLogistics = ExpressCompanyUtil::getPddLogisticsById($giftDelivery['logistics_id']);
                $logistics_data[] = LogisticsDataBo::ofPddLogisticsData($tid, $oid, $giftDelivery['tracking_number'], $giftList, $pddLogistics['logistics_company'], $pddLogistics['code']);
            }
        }


        $shop = $this->getShop();
        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->platformType, //订单类型
//                "user_id" => $trade[''], //用户ID
//            "express_no" => $trade[''], //快递单号
//            "buyer_id" => '', //买家ID
//            "buyer_nick" => '', //买家昵称
            "seller_nick" => $shop->shop_name,
            "order_status" => $this->formatOrderStatus($trade['order_status']), //订单状态
            // todo 退款状态要改
            "refund_status" => $this->formatRefundStatus($trade['refund_status']), //退款状态
//                "print_status" => $trade[''], //打印状态
            "shop_title" => $shop->shop_name, //店铺名
            "receiver_state" => $trade['province'], //收货人省份
            "receiver_city" => $trade['city'], //收货人城市
            "receiver_district" => $trade['town'], //收货人地区
//            "receiver_town" => '', //收货人街道
            "receiver_name" => $trade['receiver_name_mask'], //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => 0, //收件人邮编
            "receiver_address" => $trade['receiver_address_mask'], //收件人详细地址
            "payment" => $tradePayment, //实付金额
            "total_fee" => $tradeTotalFee, //总金额
            "discount_fee" => $trade['discount_amount'], //优惠金额
            "post_fee" => $trade['postage'], //运费
            "seller_flag" => $this->formatOrderFlag($trade['remark_tag'] ?? null),//卖家备注旗帜
            "seller_memo" => json_encode($sellerMemo, 320), //卖家备注
            "buyer_message" => $trade['buyer_memo'] ?? '', //买家留言
            "has_buyer_message" => !empty($trade['buyer_memo']),
//                "express_code" => $trade[''], //快递公司代码
            "order_created_at" => $trade['created_time'], //订单创建时间
            "order_updated_at" => $trade['updated_at'], //订单修改时间
            "send_at" => $trade['shipping_time'], //发货时间
            "finished_at" => $trade['receive_time'] ?? null, //订单完成时间
            "groupon_at" => $trade['confirm_time'] ?? null, //订单完成时间
            "pay_at" => $trade['pay_time'] ?? null, //支付时间
//                "refund_id" => $trade[''], //退款id
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => 1, // SKU数量
            'is_pre_sale' => $trade['is_pre_sale'] ?: 0, // 是否预售1是0否
            'promise_ship_at' => $trade['promise_delivery_time'] ?? null, // 承诺发货时间
            'items' => $orderItems,
            'is_remote_transit' => isset($consolidateType),
            'cipher_info' => $cipher_info,
            'order_extra' => [
                'order_biz_type' => OrderExtra::BIZ_TYPE_GENERAL, // 订单业务类型
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);

        return $orderData;
    }

    /**
     * 地址MD5,拼多多通过
     * @param $order
     * @param $cipherInfo
     * @param $isCrossShopMergeOrder
     * @param Order $oldOrder
     * @return string
     */
    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder,$oldOrder): string
    {
        //如果新的数据里面没有oaid（因为PDD会把已发货的oaid清空），所以用老的订单数据里面的oaid
        $oaid = $cipherInfo['oaid'];
        if(empty($oaid)&&!empty($oldOrder)&&!empty($oldOrder->orderCipherInfo->oaid)){
            $oaid = $oldOrder->orderCipherInfo->oaid;
        }
        $addressArr = [
            $order['shop_id'],
//            $order['receiver_city'],
//            $order['receiver_district'],
//            $cipherInfo['receiver_name_mask'],
//            $cipherInfo['receiver_phone_mask'],
//            $cipherInfo['receiver_address_mask']
            $oaid
        ];
        if ($isCrossShopMergeOrder) {
            array_shift($addressArr); // 跨店合单，不需要店铺id
        }
        Log::info('buildAddressMd5', ["orderSn" => $order['tid'], "addressArr" => $addressArr]);
        return md5(implode(',', $addressArr));
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $res): array
    {
        $orders = [];
        foreach ($res as $index => $item) {
            $orders[] = $this->formatToOrder($item);
        }
        return $orders;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['sku_list'] as $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['sku_id'],
                "sku_value" => $item['spec'],
                "outer_id" => $item['outer_id'],
                "outer_goods_id" => $item['outer_goods_id'],
                "sku_pic" => null,
                "is_onsale" => $item['is_sku_onsale'] ? GoodsSku::IS_ONSALE_YES : GoodsSku::IS_ONSALE_NO,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['goods_id'],
            'outer_goods_id' => null,
            'goods_title' => $good['goods_name'],
            'goods_pic' => $good['image_url'],
            'is_onsale' => $good['is_onsale'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => null,
            'goods_updated_at' => null,
            'skus' => $skus
        ];
    }

    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * @inheritDoc
     * @throws ClientException
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
//            'is_lucky_flag' => 0, // 订单类型（是否抽奖订单），0-全部，1-非抽奖订单，2-抽奖订单
            'order_status' => 5, // 发货状态，1-待发货，2-已发货待签收，3-已签收，5-全部
            'start_confirm_at' => $startTime,
            'end_confirm_at' => $endTime,
            'page_size' => $this->pageSize,
//            'page_size' => 5,
            'page' => 1,
            'refund_status' => 5, // 售后状态，1-无售后或售后关闭，2-售后处理中，3-退款中，4-退款成功 5-全部
//            'trade_type' => 0, // 订单类型： 0-普通订单、1-定金订单 不传为全部
            'has_next' => 'true', // 是否启用has_next的分页方式
        ];
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['order_status'] = 1;
        }
        $response = $client->execute('pdd.order.list.get', $params);
        if ($response['order_list_get_response']['has_next']) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        $order_list = $response['order_list_get_response']['order_list'];
        Log::info('获取订单', $order_list);
        return $order_list;

    }

    /**
     * 订单详情
     * @param string $tid
     * @return mixed
     * @throws ClientException
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'order_sn' => $tid,
        ];
        $response = $client->execute('pdd.order.information.get', $params);

        return $response['order_info_get_response']['order_info'];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {

    }

    /**
     * 商品获取
     * @param int $pageSize ,
     * @param int $currentPage
     * @return mixed
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'page_size' => $this->pageSize,
            'page' => $currentPage,
        ];
        $response = $client->execute('pdd.goods.list.get', $params);
        if (isset($response['error_response'])) {
            Log::error('pdd.goods.list.get', [$response]);
        }
        if (isset($response['goods_list_get_response']['total_count'])) {
            $this->goodsTotalCount = $response['goods_list_get_response']['total_count'];
        } else {
            $this->goodsTotalCount = 0;
        }

        return $response['goods_list_get_response']['goods_list'];
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return bool|mixed
     * @throws ClientException
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId
    = [], bool                                     $silent = true)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        //todo wpCode 对应pdd官方快递公司ID
        $express = collect(config('express_company'))->where('wpCode', $expressCode)->first();
        if (empty($express)) {
            return false;
        }
        $params = [
            'logistics_id' => $express['id'],
            'order_sn' => $tid,
            'tracking_number' => $expressNo,
        ];
        $response = $client->execute('pdd.logistics.online.send', $params);
        Log::info('pdd_delivery_result', [$response]);
        if (isset($response['error_response'])) {
            Log::error('订单发货失败!', [$response, $this->platformType, $tid, $expressCode, $expressNo]);
            return false;
        }

        return $response['logistics_online_send_response']['is_success'];
    }

    /**
     * @throws ApiException
     */
    protected function sendBatchGetOrderInfo($orders, $safe = false): array
    {
        $pddClient = PddClient::newInstance($this->getAccessToken());
        $tids = array_column($orders, 'tid');
        $params = array_map(function (string $tid) {
            return [
                'order_sn' => $tid
            ];
        }, $tids);
        $result = [];
        $curlResultArr = $pddClient->poolCurl("pdd.order.information.get", $params);
        foreach ($curlResultArr as $curlResult) {
            if ($curlResult->isSuccess()) {
                $result[] = $curlResult->arrayGet('order_info_get_response.order_info');
            }
        }
        return $result;


    }

    /**
     * @inheritDoc
     * @throws ClientException
     * @throws ApiException
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $accessToken = $this->getAccessToken();
        $client = PddClient::newInstance($accessToken);
        $params = [
            'is_lucky_flag' => 0, // 订单类型（是否抽奖订单），0-全部，1-非抽奖订单，2-抽奖订单
            'order_status' => 5, // 发货状态，1-待发货，2-已发货待签收，3-已签收，5-全部
            'start_updated_at' => $startTime,
            'end_updated_at' => $endTime,
            'page_size' => $this->pageSize,
            'page' => 1,
            'refund_status' => 5, // 售后状态，1-无售后或售后关闭，2-售后处理中，3-退款中，4-退款成功 5-全部
//            'trade_type' => 0, // 订单类型： 0-普通订单、1-定金订单 不传为全部
            'has_next' => 'true', // 是否启用has_next的分页方式
        ];
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['order_status'] = 1;
        }
        Log::info('pdd.order.number.list.increment.get', ["accessToken" => $accessToken, "params" => $params]);
        $response = $client->execute('pdd.order.number.list.increment.get', $params);
        if ($response['order_sn_increment_get_response']['has_next']) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        return $response['order_sn_increment_get_response']['order_sn_list'];

    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        $client = new PddClient(config('socialite.pdd.client_id'), config('socialite.pdd.client_secret'));
        $client->setAccessToken($this->getAccessToken());
        return $client;
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg(): bool
    {
        try {
            $client = $this->getClient();
            $client->setAccessToken($this->getAccessToken());

//            $topics = [
//                'trace_isv_notify', //物流轨迹 物流消息
////            'pdd_refund_RefundBuyerModifyAgreement', //买家修改退款协议消息 售后消息
////            'pdd_refund_RefundBuyerReturnGoods', //买家退货给卖家消息 售后消息
////            'pdd_refund_RefundCreateMessage', //发表退款留言消息 售后消息
//                'pdd_refund_RefundCreated', //退款创建消息 售后消息
////            'pdd_refund_RefundAgreeAgreement', //同意退款协议消息 售后消息
////            'pdd_trade_TradeLogisticsAddressChanged', //修改交易收货地址消息 订单消息
//                'pdd_trade_TradeSuccess', //交易成功消息 订单消息
//                'pdd_trade_TradeSellerShip', //卖家发货消息 订单消息
//                'pdd_trade_TradeConfirmed', //交易确认 订单消息
//                'pdd_trade_TradeMemoModified', //交易备注 修改消息
//            ];
            $params = [

            ];
            $response = $client->execute('pdd.pmc.user.permit', $params);
            if ($response['pmc_user_permit_response']['is_success']) {
                return true;
            }
        } catch (\Throwable $throwable) {
            Log::error('订阅消息失败!', ["exception" => $throwable]);
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {

    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        return true;
    }

    public function handleSubscribeMsg($data)
    {
        $content = json_decode($data, true);
        if ($content['commandType'] == 'Fail') {
            Log::error('订阅消息错误!', $content);
            return [];
        }
        if (empty($content['message']['content'])) {
            return [];
        }
        $message = json_decode($content['message']['content'], true);
        $body = [];
        switch ($content['message']['type']) {
            case 'pdd_refund_RefundCreated':
            case 'pdd_trade_TradeSuccess':
            case 'pdd_trade_TradeSellerShip':
            case 'pdd_trade_TradeConfirmed':
            case 'pdd_trade_TradeMemoModified':
                $type = SubscribeMessageType::ORDER;
//                $body['tid'] = $message['tid'];
//                $body['order_status'] = 1;
                break;
            case 'trace_isv_notify':
            case 'trace_isv_NotifyByMall':
                // @see https://open.pinduoduo.com/application/document/message?secondCatId=5&id=20
                $type = SubscribeMessageType::LOGISTIC;
                $orderTraceMap = array_combine(self::TRACE_ARR, OrderTraceList::STATUS_ARR);
                $body = [
//                    "type"              => $this->platformType,
                    'express_code' => $message['shippingId'],
                    'express_no' => $message['trackingNumber'],
                    'status' => $orderTraceMap[$message['node_description']],
                    'action' => $message['node_description'],
                    'latest_updated_at' => $message['status_time'],
                    'latest_trace' => $message['desc'],
                ];
                break;
            default:
                $type = SubscribeMessageType::ERROR;
                $body = $message;
                break;
        }

        return [
            'type' => $type,
            'body' => $body,
        ];
    }


    /**
     * @inheritDoc
     * @throws \Exception
     */
    public function createWebsocketConnection()
    {
        $baseUrl = "ws://message-api.pinduoduo.com";
        $clientId = config('waybill.pddwb.client_id');
        $clientSecret = config('waybill.pddwb.client_secret');
        $nowTime = time() * 1000;
        $sign = base64_encode(md5("{$clientId}{$nowTime}{$clientSecret}"));
        $websocketUrl = $baseUrl . "/message/{$clientId}/{$nowTime}/{$sign}";

        // ssl需要访问443端口
        $con = new AsyncTcpConnection($websocketUrl);

        // 设置以ssl加密方式访问，使之成为wss
        $con->transport = 'ssl';
        return $con;
    }

    /**
     * @inheritDoc
     * @throws ApiException|ClientException
     */
    public function sendServiceInfo(): array
    {
        $client = PddClient::newInstance($this->getAccessToken());
        $shop = $this->getShop();


        $resp = $client->execute('pdd.servicemarket.contract.search', ["mallId" => $shop->identifier]);

        $response = $resp["servicemarket_contract_search_response"];
        Log::info("获取订阅信息", [$response]);
        $specValueList = array_get($response, "specValue.specValueList", null);

        $versionSpecValueArr = array_filter($specValueList, function ($item) {
            return $item['specName'] == "服务版本";
        });
        $versionSpecValue = array_pop($versionSpecValueArr);
        $versionDesc = $versionSpecValue['specValue'] ?? '';

        /**
         * {
         * "item_code":"ts-123-1",
         * "deadline":"2000-01-01 00:00:00"
         * }
         */
        return [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'expired_at' => DateTimeUtil::second2str($response['endAt'] / 1000),
            'version' => UserExtra::getVersionValueByName($versionDesc),
            'version_desc' => $versionDesc,
        ];

        /*
        self::generateOrEdit($mallId, $userId);
        //获取最高等级
        $weightOrder = PddOrder::findHignLevelOrder($userId, Carbon::now()->timestamp * 1000);
        if (empty($weightOrder)) {
            $ret = UserExtra::where('user_id', $userId)->update(
                [
                    'user_level'  => User::LEVEL_INVALID,
                    'expire_time' => null,
                ]
            );
            Log::info('登录处理-用户等级过期处理完成', [$ret]);
            return false;
        }

        //更新用户等级
        $expireAt = Carbon::createFromTimestampMs($weightOrder->pay_time)->addSeconds($weightOrder->time_length)->toDateTimeString();
        $res      = User::updateLevel($userId, $weightOrder->sku_id, $expireAt);

        return $res;


        return [];
        return isset($resp['mall_info_get_response'])?$resp['mall_info_get_response']:[];
        */
    }

    private function findHignLevelOrder($orders)
    {
        // TODO
    }


    /**
     * @inheritDoc
     * @see https://open.pinduoduo.com/#/apidocument/port?portId=pdd.refund.list.increment.get
     * @throws ClientException
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        $client = $this->getClient();
        $token = $this->getAccessToken();
        $client->setAccessToken($token);
        $params = [
            'page' => $this->page,
            'page_size' => $this->pageSize,
            'start_updated_at' => $startTime,
            'end_updated_at' => $endTime,
            'after_sales_status' => 1, //售后状态 1：全部 2：买家申请退款，待商家处理 3：退货退款，待商家处理 4：商家同意退款，退款中 5：平台同意退款，退款中 6：驳回退款， 待买家处理 7：已同意退货退款,待用户发货 8：平台处理中 9：平台拒 绝退款，退款关闭 10：退款成功 11：买家撤销 12：买家逾期未处 理，退款失败 13：买家逾期，超过有效期 14 : 换货补寄待商家处理 15:换货补寄待用户处理 16:换货补寄成功 17:换货补寄失败 18:换货补寄待用户确认完成 31：商家同意拒收退款，待用户拒收;32: 待商家补寄发货
            'after_sales_type' => 1, //售后类型 1：全部 2：仅退款 3：退货退款 4：换货 5：缺货补寄
        ];
        $resp = $client->execute('pdd.refund.list.increment.get', $params);

        return $resp['refund_increment_get_response']['refund_list'];
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        return [
            'tid' => $order['order_sn'],
            'oid' => $order['order_sn'],
            'refund_id' => $order['id'],
//            'express_no' => $order['tracking_number'],
            'refund_created_at' => $order['created_time'],
            'refund_updated_at' => $order['updated_time'],
            'refund_status' => $this->formatRefundStatus($order['after_sales_status']),
        ];
    }

    /**
     * @throws ApiException
     * @throws ClientException
     * @throws ErrorCodeException
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool
    {
        $pddClient = PddClient::newInstance($this->getAccessToken(), $this->shop->id);
        $param = [
            'order_sn' => $tid,
            'note' => $sellerMemo
        ];
        if ($sellerFlag) {
            if (!array_key_exists($sellerFlag, self::ORDER_FLAG_MAP_REVERT)) {
                throw_error_code_exception(ErrorConst::PARAM_ERROR, null, $this->flagErrorMsg($sellerFlag));
            }
            $param['tag'] = self::ORDER_FLAG_MAP_REVERT[$sellerFlag];
            $param['tag_name'] = self::ORDER_FLAG_NAME_MAP_REVERT[$sellerFlag];
        }

        $response = $pddClient->execute("pdd.order.note.update", $param);
        Log::info("订单备注修改", [$response]);
        return array_get($response, "response.success");
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    public function sendMarketOrderDetail($orderId)
    {
        // TODO: Implement sendMarketOrderDetail() method.
    }

    public function sendPlatformOrder($startTime, $endTime)
    {
        // TODO: Implement sendPlatformOrder() method.
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sentBatchDecrypt($order)
    {
        // TODO: Implement sentBatchDecrypt() method.
    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sendDecrypt($order)
    {
        // TODO: Implement sentDecrypt() method.
    }

    /**
     * 批量加密
     * @param $order
     * @return mixed
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * 获取订单列表的方式来检查
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        //当前时间5分钟
        $startTime = Carbon::now()->subMinutes(5)->timestamp;
        $endTime = Carbon::now()->subMinutes(4)->timestamp;

        try {
            $response = $this->sendGetTradesOrdersByIncr($startTime, $endTime);
            Log::info("订单列表检查", [$response]);
            return true;
        } catch (Exception $e) {
            Log::info("订单列表检查失败", [$e->getMessage()]);
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendDDShopRoleType() method.
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getDDTradesOrder() method.
    }


    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * 请求查询交易订单的 Tid
     * @param array $query_list
     * <AUTHOR>
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        // TODO: Implement sendAddress() method.
    }

    /**
     * @param  string  $type
     * @param  string  $search
     * @return array
     * @throws ApiException
     */
    public function getQueryTradeOrderId(string $type, string $search): array
    {
        // 查询类型：0-收货人手机号，1-收货人姓名
        $map = [
            'receiver_name' => 1,
            'receiver_phone' => 0,
        ];
        $searchType = $map[$type];
        if (empty($search)
            || ($type == 'receiver_phone' && !isPhoneNumber($search)) // 手机号，匹配不上手机号规则
            || ($type == 'receiver_name' && is_numeric($search))) { // 名字但是是数字
            return [];
        }
        Log::info('pdd查询订单号', [$type, $search, $searchType]);
        $beginAt = \request()->input('begin_at', date('Y-m-d H:i:s', strtotime('-90 day')));
        $endAt = \request()->input('end_at', date('Y-m-d H:i:s'));
        $client = PddClient::newInstance($this->accessToken, $this->shop->id);
        $params = [
            'start_created_at' => strtotime($beginAt),
            'end_created_at' => strtotime($endAt),
        ];
        $params[$type] = $search;

        try {
            $response = $client->execute('pdd.order.search.order', $params);
            $tidArr = array_get($response, 'search_order_response.order_sn_list', []);
            Log::info('查询订单号', $tidArr);
        }catch (\Exception $e){
            Log::error('查询订单号失败', [$e->getMessage()]);
            return [];
        }

//        $this->handleErrorCode($response);
        if (!empty($tidArr)) {

            $shop = $this->getShop();

            return Order::query()
                ->select('id')
                ->where('shop_id', $shop->id)
                ->whereIn('tid', $tidArr)
                ->get()
                ->pluck('id')
                ->toArray();
        }

        return [];
    }

    /**
     * 拼多多多包裹发货，拼多多并没没有类似抖音的拆掉发货的接口
     * 采用的方式是整单发货+
     * @param int $shopId
     * @param array{
     *      tid:string,
     *      shopId:int,
     *      packs:array{
     *          waybillCode:string,
     *          expressCode:string,
     *          goods:array{
     *              oid:string,
     *              shippedNum:int
     *          }
     *      }
     *  }[] $orderDeliveryRequests
     * @return array
     * >* @throws ApiException
     * @throws ApiException
     */
    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $pddClient = PddClient::newInstance($this->getAccessToken(), $this->shop->id);
        $requestData = [];
        $orderDeliveryRequestsGroupByTid = ArrayUtil::array_group_by($orderDeliveryRequests, 'tid');
        foreach ($orderDeliveryRequestsGroupByTid as $tid => $tidOrderDeliveryRequests) {

            $extraLogistics = [];
            $extraLogistics["extra_track_type"] = 1;
            $extraLogistics['order_sn'] = $tid;
            foreach ($tidOrderDeliveryRequests as $tidOrderDeliveryRequestItem) {
                $extraLogistics['extra_track_list'][] = [
                    'shipping_id' => ExpressCompanyUtil::getPddLogisticsByCode($tidOrderDeliveryRequestItem['expressCode'])['id'],
                    'tracking_number' => $tidOrderDeliveryRequestItem['waybillCode'],
                ];
            }
            $requestData[] = $extraLogistics;
        }
        $curlDataResults = $pddClient->poolCurl("pdd.order.upload.extra.logistics", $requestData);
        $successes = [];
        $failures = [];
        foreach ($curlDataResults as $index=> $curlDataResult) {
            $extraLogistics = $requestData[$index];
            $waybillCodes=implode(',',array_column($extraLogistics['extra_track_list'],'tracking_number'));
            if ($curlDataResult->isSuccess()) {
                $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
            } else {
                //进入了异常情况，返回的是一个数组

                $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => $curlDataResult->errorMsg];
            }


        }
        return ["successes" => $successes, "failures" => $failures];

    }

    /**
     * @inheritDoc
     * @throws ApiException
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $pddClient = PddClient::newInstance($this->getAccessToken(), $this->shop->id);
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            $orderItemOId = $orderDeliveryRequest->orderItemOId;
            $pddLogisticsByCode = ExpressCompanyUtil::getPddLogisticsByCode($expressCode);
            Log::info('pddlogistics', [$expressCode, $pddLogisticsByCode]);
            $params = [
                'logistics_id' => $pddLogisticsByCode['id'],
                'order_sn' => strval($tid),
                'tracking_number' => $expressNo,
            ];
            $requestData[] = $params;

        }
        Log::info('batchDeliveryOrders requestData', [$requestData]);
        $curlDataResults = $pddClient->poolCurl('pdd.logistics.online.send', $requestData);
//        Log::info('batchDeliveryOrders poolCurl',[$requestData,$responses]);
        $results = [];
        foreach ($curlDataResults as $index => $curlResult) {
            $commonResponse = new CommonResponse();
            $orderDeliveryRequest = $orderDeliveryRequests[$index];
            $commonResponse->setRequest($orderDeliveryRequest);
            if ($curlResult->isSuccess()) {
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $commonResponse->setSuccess(true);
            } else {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($curlResult->errorCode);
                $commonResponse->setMessage($curlResult->errorMsg);
            }
            $results[] = $commonResponse;
        }
        Log::info('poolCurl done', [$results]);
        return $results;
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        return $apiParams;
    }

    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = $this->getClient();
        $result = $client->execute($apiMethod, $apiParams);
        return $result;
    }

    /**
     * 批量修改订单备注,返回更新失败的tid
     * @param $tidList
     * @param $sellerFlag
     * @param $sellerMemo
     * @return array|string[]
     * @throws ApiException
     */
    public function sendBatchEditSellerRemark($tidList, $sellerFlag, $sellerMemo): array
    {
        $failTids = [];
        $pddClient = PddClient::newInstance($this->getAccessToken(), $this->shop->id);
        $params = array_filter(array_map(function ($tid) use ($sellerFlag, $sellerMemo, &$failTids) {
            $param = [
                'order_sn' => $tid,
                'note' => $sellerMemo
            ];

            if ($sellerFlag) {
                if (!array_key_exists($sellerFlag, self::ORDER_FLAG_MAP_REVERT)) {
                    $failTids[$tid] = $this->flagErrorMsg($sellerFlag);
                    //如果旗标不支持，返回null，然后从调用接口的时候过滤掉
                    return null;
                }
                $param['tag'] = self::ORDER_FLAG_MAP_REVERT[$sellerFlag];
                $param['tag_name'] = self::ORDER_FLAG_NAME_MAP_REVERT[$sellerFlag];
            }
            return $param;
        }, $tidList), function ($param) {
            return $param !== null;
        });
        $curlResults = $pddClient->poolCurl("pdd.order.note.update", $params);
        foreach ($curlResults as $index => $curlResult) {
            $param=$params[$index];
            $tid = $param['order_sn'];
            if (!$curlResult->isSuccess()) {
                Log::error('sendBatchEditSellerRemark error', [$curlResult->errorCode, $curlResult->errorMsg]);

                $failTids[$tid] = $curlResult->errorMsg;
            }
        }
        Log::info('sendBatchEditSellerRemark done', $failTids);
        return $failTids;

    }

    public function sendCheckMergeOrder(array $orderList): array
    {
        return self::groupOrdersByOaid($orderList);
    }

    /**
     * 把订单进行分组，分组的逻辑是。如果订单的open_address_id相同，则认为是一个订单组，
     * 如果订单的open_address_id2相同，则认为是一个订单组。
     * 把相同组的订单的订单号tid连接成字符串
     * @param Order[] $orders
     * //
     * @return string[]
     */
    public static function groupOrdersByOaid(array $orders): array
    {
        /**
         * @var Order[][] $groupedOrders
         */
        $groupedOrders = [];

        foreach ($orders as $order) {
            $foundGroup = false;
            Log::info('groupOrdersByOaid', [$order]);
            $oaidStr = array_get($order, 'order_cipher_info.oaid');
            $orderOaid = OrderCipherInfo::getOaid($oaidStr);
            $orderOaid1 = OrderCipherInfo::getOaid1($oaidStr);
            foreach ($groupedOrders as &$group) {
                foreach ($group as $existingOrder) {
                    $existOaidStr = array_get($existingOrder, 'order_cipher_info.oaid');
                    $existOaid = OrderCipherInfo::getOaid($existOaidStr);
                    $existOaid1 = OrderCipherInfo::getOaid1($existOaidStr);
                    if (
                        ($orderOaid != null && $orderOaid == $existOaid) ||
                        ($orderOaid1 != null && $orderOaid1 == $existOaid1)) {
                        $group[] = $order;
                        $foundGroup = true;
                        break 2;
                    }
                }
            }

            if (!$foundGroup) {
                $newGroup = [];
                $newGroup[] = $order;
                $groupedOrders[] = $newGroup;
            }
        }

        // 把订单组连接成字符串
        $ordersGroup = array_map(function ($group) {
            return implode(',', array_map(function ($order) {
                return $order['tid'];
            }, $group));
        }, $groupedOrders);
        Log::info('通过open_address_id分组后的订单组', $ordersGroup);
        return $ordersGroup;
    }

    /**
     * 1：无售后或售后关闭，2：售后处理中，3：退款中，4： 退款成功
     * @param $refund_status
     * @return void
     */
    private function formatRefundSubStatus($refund_status):int
    {
     switch ($refund_status):
        case 1:
            return RefundSubStatusConst::NONE;
         case 3:
         case 2:
            return RefundSubStatusConst::MERCHANT_PROCESSING;
         case 4:
            return RefundSubStatusConst::REFUND_COMPLETE;
        default:
            return 0;
     endswitch;
    }

}
