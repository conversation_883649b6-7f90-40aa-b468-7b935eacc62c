<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/8/12
 * Time: 11:58
 */

namespace App\Services\Order\Impl;


use ACES\TDEClient;
use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Events\BaseRequestEvent;
use App\Events\Event;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Exceptions\OrderException;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Bo\OrderResponseBo;
use App\Services\Client\Jd\Request\BuyOrderSopWaybillUpdateRequest;
use App\Services\Client\JdClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\OrderServiceManager;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\Order\Result\OrderRefund;
use App\Services\Order\Result\OrderRefundItem;
use App\Utils\ArrayUtil;
use App\Utils\DateTimeUtil;
use App\Utils\ExpressCompanyUtil;
use App\Utils\LogUtil;
use AreasCityGetRequest;
use AreasCountyGetRequest;
use AreasProvinceGetRequest;
use AreasTownGetRequest;
use AscServiceAndRefundViewRequest;
use EtmsWaybillcodeGetRequest;
use EtmsWaybillSendRequest;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use IsvUploadBatchLogRequest;
use JdClient as JdClientSdk;
use Jdcloud\Credentials\Credentials;
use Jdcloud\Yundingdatapush\YundingdatapushClient;
use JosOrderOaidDecrypt\Attribute1;
use JosOrderOaidDecrypt\GetReceiverInfoListReqVO;
use JosOrderOaidDecryptRequest;
use JosOrderOaidMerge\MergeItem;
use JosOrderOaidMergeRequest;
use JosOrderOaidSearchRequest;
use OrderVenderRemarkQueryByOrderIdRequest;
use PopAfsRefundapplyQuerylistRequest;
use PopAfsSoaRefundapplyQueryPageListRequest;
use PopFwOrderListwithpageRequest;
use PopOrderEncryptMobileNumRequest;
use PopOrderGetmobilelistRequest;
use PopOrderGetRemarkByModifyTimeRequest;
use PopOrderGetRequest;
use PopOrderModifyVenderRemarkRequest;
use PopOrderPartialOutship\DeliveryNumberDtoItem;
use PopOrderPartialOutship\OutshipItem;
use PopOrderPartialOutship\PartialShipmentGoodsListItem;
use PopOrderPartialOutshipRequest;
use PopOrderSearchRequest;
use PopOrderShipmentRequest;
use PopOrderSopLogisticsUpdate\LogisticsGlobalModel;
use PopOrderSopLogisticsUpdate\OneGlobalOrderModelNoLogistic;
use PopOrderSopLogisticsUpdateRequest;
use PopOrderWaybillUpdate\PartialShipmentInfoListItem;
use PopOrderWaybillUpdateRequest;
use PrintingPrintDataPullDataRequest;
use SkuReadSearchSkuListRequest;
use VasSubscribeGetByCodeRequest;
use WareReadSearchWare4ValidRequest;

class JdOrderImpl extends AbstractOrderService implements OrderCipherInterface
{

    const ORDER_FIELDS = 'orderId,venderId,orderType,payType,orderTotalPrice,
        orderSellerPrice,orderPayment,freightPrice,sellerDiscount,orderState,orderStateRemark,
        deliveryType,orderRemark,orderStartTime,orderEndTime,consigneeInfo,itemInfoList,
        venderRemark,pin,returnOrder,paymentConfirmTim,waybill,logisticsId,modified,directParentOrderId,parentOrderId,storeId,
        idSopShipmenttype,storeOrder,paymentConfirmTime,partialLogisticsInfoModel,sendpayMap,originalConsigneeInfo';

    protected $platformType = PlatformConst::PLATFORM_TYPE_JD;

    protected $orderStatusMap = [
        'WAIT_SELLER_STOCK_OUT' => Order::ORDER_STATUS_PAYMENT,
        'WAIT_GOODS_RECEIVE_CONFIRM' => Order::ORDER_STATUS_DELIVERED,
        'WAIT_SELLER_DELIVERY' => Order::ORDER_STATUS_PAYMENT,
        'POP_ORDER_PAUSE' => Order::ORDER_STATUS_CLOSE,
        'TRADE_CANCELED' => Order::ORDER_STATUS_CLOSE,
        'LOCKED' => Order::ORDER_STATUS_CLOSE,
        'WAIT_SEND_CODE' => Order::ORDER_STATUS_CLOSE,
        'PAUSE' => Order::ORDER_STATUS_CLOSE,
        'DELIVERY_RETURN' => Order::ORDER_STATUS_CLOSE,
        'FINISHED_L' => Order::ORDER_STATUS_SUCCESS,
        'UN_KNOWN' => Order::ORDER_STATUS_UNKNOWN,
    ];

    /**
     * 审核状态: 0、未审核
     * 1、审核通过((包括商家审核以及平台审核)
     * 2、审核不通过
     * 3、京东财务审核通过
     * 4、京东财务审核不通过
     * 5、人工审核通过（该值已废弃）
     * 6、京东拦截并退款
     * 7、青龙拦截成功
     * 8、青龙拦截失败
     * 9、强制关单并退款
     * 10、物流待跟进(线下拦截)
     * 11、用户撤销
     * 16、拒收后退款
     * 17、协商退货退款
     * 18、协商关闭
     * 19、纠纷介入（中间状态，表示进入协商流程后，消费者不同意协商会生成纠纷单进入纠纷）
     * 27、京东承诺拦截（京东拦截的升级版，跟status=6基本一致）
     */

    const  SUB_REFUND_STATUS_MAP = [
        0 => RefundSubStatusConst::MERCHANT_PROCESSING,
        1 => RefundSubStatusConst::REFUND_COMPLETE,

        2 => RefundSubStatusConst::MERCHANT_REFUSE_REFUND,
        3 => RefundSubStatusConst::REFUND_COMPLETE,
        4 => RefundSubStatusConst::MERCHANT_REFUSE_REFUND,
        6 => RefundSubStatusConst::REFUND_COMPLETE,
        7 => RefundSubStatusConst::REFUND_COMPLETE,
        8 => RefundSubStatusConst::REFUND_COMPLETE,
        9 => RefundSubStatusConst::REFUND_COMPLETE,
        10 => RefundSubStatusConst::REFUND_COMPLETE,
        11 => RefundSubStatusConst::REFUND_CLOSE,
        16 => RefundSubStatusConst::REFUND_COMPLETE,
        17 => RefundSubStatusConst::REFUND_COMPLETE,
        18 => RefundSubStatusConst::REFUND_CLOSE,
        19 => RefundSubStatusConst::MERCHANT_PROCESSING,
        27 => RefundSubStatusConst::REFUND_COMPLETE,

    ];

    /**
     * 审核状态: 0、未审核
     * 1、审核通过((包括商家审核以及平台审核)
     * 2、审核不通过
     * 3、京东财务审核通过
     * 4、京东财务审核不通过
     * 5、人工审核通过（该值已废弃）
     * 6、京东拦截并退款
     * 7、青龙拦截成功
     * 8、青龙拦截失败
     * 9、强制关单并退款
     * 10、物流待跟进(线下拦截)
     * 11、用户撤销
     * 16、拒收后退款
     * 17、协商退货退款
     * 18、协商关闭
     * 19、纠纷介入（中间状态，表示进入协商流程后，消费者不同意协商会生成纠纷单进入纠纷）
     * 27、京东承诺拦截（京东拦截的升级版，跟status=6基本一致）
     */
    protected $refundStatusMap = [
        0 => Order::REFUND_STATUS_YES,
        1 => Order::REFUND_STATUS_YES,
        2 => Order::REFUND_STATUS_NO,
        3 => Order::REFUND_STATUS_YES,
        4 => Order::REFUND_STATUS_NO,
        5 => Order::REFUND_STATUS_YES,
        6 => Order::REFUND_STATUS_YES,
        7 => Order::REFUND_STATUS_YES,
        8 => Order::REFUND_STATUS_YES,
        9 => Order::REFUND_STATUS_YES,
        10 => Order::REFUND_STATUS_YES,
        11 => Order::REFUND_STATUS_NO,
        16 => Order::REFUND_STATUS_YES,
        17 => Order::REFUND_STATUS_YES,
        18 => Order::REFUND_STATUS_NO,
        19 => Order::REFUND_STATUS_YES,
        27 => Order::REFUND_STATUS_YES,
    ];

    protected $orderFlagMap = [
        0 => Order::FLAG_GRAY,
        1 => Order::FLAG_RED,
        2 => Order::FLAG_ORANGE,
        3 => Order::FLAG_CYAN,
        4 => Order::FLAG_BLUE,
        5 => Order::FLAG_PURPLE,
    ];

    const ORDER_FLAG_MAP_REVERT = [
        Order::FLAG_GRAY => 0,
        Order::FLAG_RED => 1,
        Order::FLAG_ORANGE => 2,
        Order::FLAG_CYAN => 3,
        Order::FLAG_BLUE => 4,
        Order::FLAG_PURPLE => 5,
    ];

    /**
     * @throws ApiException
     */
    public function sendBatchEditSellerRemark($tidList, $sellerFlag, $sellerMemo): array
    {
        $failTids = [];
        $client = $this->getJdClient();
        $data = [];
        foreach ($tidList as $index => $tid) {
            $request = new PopOrderModifyVenderRemarkRequest();
            $request->setOrderId($tid);
            if (isset($sellerFlag)) {
                if (!array_key_exists($sellerFlag, self::ORDER_FLAG_MAP_REVERT)) {
                    $failTids[$tid] = $sellerFlag."旗标不支持";
                    //如果旗标不支持，返回null，然后从调用接口的时候过滤掉
                    continue;
                }
                $request->setFlag(self::ORDER_FLAG_MAP_REVERT[$sellerFlag]);
            }
            $request->setRemark($sellerMemo);
            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($request);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl,
                "tid" => $tid,
            ];
        }

        $response = $this->poolCurl($data, 'post');
        foreach ($response as $index => $body) {
            $tid = $data[$index]['tid'];
            try {

                $resp = $this->handleResp($body);

//                $results[$index] = $this->formatToOrder($orderInfo);
            } catch (\Exception $e) {

                $failTids[$tid] = $e->getMessage();
            }
        }
        return $failTids;
    }//https://jos.jd.com/commondoc?listId=335

    protected $expressCodeList = [
        'ZTO' => 1499,
        'YUNDA' => 1327,
        'STO' => 470,
        'UC' => 1747,
        'QFKD' => 2016,
        'KJKD' => 2094,
        'GTO' => 2465,
        'QY' => 2100,
        'SE' => 2105,
        'EMSBZ' => 3668,
        'ZJS' => 1409,
        'ZGYZZHDD' => 2170,
        'KYE' => 599866,
        'SF' => 467,
        'YTO' => 463,
        'DBKD' => 3046,
        'ANE' => 4832,
        'SAVOR' => 835370,
        'DSBK' => 2124945,
        'DDTCKS' => 1999327,
        'HBX' => 2134079,
        'JGSD' => 2133515,
        'BYL' => 222693,
        'BESTJD' => 1748,
        'DDKY' => 2119699,
        'JTSD' => 1255654,
        'AF' => 323141,
        'BDB' => 2087,
        'JDKY' => 773574,
        'JDYY' => 1613410,
        'JDDJ' => 336878,

    ];


    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * @var string
     */
    private $appKey;
    /**
     * @var string
     */
    private $appSecret;

    public function __construct()
    {
        parent::__construct();
        $this->appKey = config('socialite.jd.client_id');
        $this->appSecret = config('socialite.jd.client_secret');
    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
//        Log::info('JD订单详情',  $trade);
        $orderItems = [];
        $tid = $trade['orderId'];
        $partialLogistics = $trade['partialLogisticsInfoModel'] ?? [];//部分发货物流信息
        //把部分发货信息里面的所有的sku都合并到一个数组里面,然后拉平
        $partialSkuList = array_merge(array_flatten(array_column($partialLogistics, 'skus'), 1));
//        Log::info('partialSkuList', $partialSkuList);
        //再把发货的SkuList按SkuUUid分组
        $partialSkusGroupBySkuUuid = ArrayUtil::array_group_by($partialSkuList, "skuUuid");
        $orderStatus = $this->formatOrderStatus($trade['orderState']);
        if ($orderStatus == Order::ORDER_STATUS_PAYMENT && sizeof($partialLogistics) > 0) {
            //JD的原始状态定义没有部分发货，所以这里判断一下待发货+部分发货的信息>0 则订单状态为部分发货
            $orderStatus = Order::ORDER_STATUS_PART_DELIVERED;
        }
        /**
         * @var  OrderRefund[] $refundApplies
         */
        $refundApplies = array_get($trade, 'refundApplies', []);
        //把关闭对的关闭的退款单过滤掉
        $refundApplies=array_filter($refundApplies, function (OrderRefund  $item) {
           return  $item->refundSubStatus!=RefundSubStatusConst::REFUND_CLOSE;
        });
        $isRefund = false;
        if (empty($refundApplies)) {
            $refundStatus = Order::REFUND_STATUS_NO;
        } else {
            $isRefund = true;
            $isPartRefund = $refundApplies[0]->isPartRefund;
            $refundStatus = $isPartRefund ? Order::REFUND_STATUS_PART : Order::REFUND_STATUS_YES;
        }
        //获取商家备注
//        $sellerMemo = $this->getSellerMemo($tid);
        foreach ($trade['itemInfoList'] as $index => $item) {
//            $sku = $this->getSkuInfo($item['skuId']);
            $sku = $item['skuInfo'];
            $total = $item['jdPrice'];
            $itemExt = empty($item['itemExt']) ? [] : json_decode($item['itemExt'], true);


            $skuUuid = array_get($itemExt, 'skuUuid');
            $refundSubStatus = RefundSubStatusConst::NONE;
            if(!empty($refundApplies)){
                foreach ($refundApplies as $refundApply){
                    $containsSkuUuid = $refundApply->containsSkuUuid($skuUuid);
                    if($containsSkuUuid){
                        $refundSubStatus = $refundApply->refundSubStatus;
                        break;
                    }
                }
            }
            //部分发货的Sku的信息
            $sendSkuList = array_get($partialSkusGroupBySkuUuid, $skuUuid, []);
            $wareTitle = $sku['wareTitle'] ?? ''; // 值有可能没有
            //平台返回的SKU Value是商品+规格，这里把商品名$wareTitle去掉，然后trim掉前后的空格
//            Log::info('skuValue', [$item['skuName']]);
            $skuValue = trim(str_replace($wareTitle, '', $item['skuName']));
            $skuValueArr = explode(';', $skuValue);
            $skuValue1 = $skuValueArr[0] ?? '';
            $skuValue2 = $skuValueArr[1] ?? '';
//            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);
            $sendNum = array_sum(array_column($sendSkuList, 'num'));
            $orderItemStatus=$orderStatus;
            $goodsNum = $item['itemTotal'];
            //匹配到改sku的发货信息
            if(!empty($sendSkuList)){
                if($sendNum==$goodsNum){
                    $orderItemStatus=OrderItem::ORDER_STATUS_DELIVERED;
                }elseif($sendNum>0 && $sendNum<$goodsNum){
                    $orderItemStatus=OrderItem::ORDER_STATUS_PART_DELIVERED;
                }else{
                    $orderItemStatus=OrderItem::ORDER_STATUS_PAYMENT;
                }
                Log::info('部分发货状态判断',[$skuUuid,$orderItemStatus]);

            }
            //有部分发货的信息，但没有sku的发货信息，就是未发货
            if(!empty($partialSkusGroupBySkuUuid)&&empty($sendSkuList)){
                $orderItemStatus=OrderItem::ORDER_STATUS_PAYMENT;
            }

            $orderItem = [
                "tid" => $tid, //主订单
                "oid" => $tid.'-'.$item['skuId'] ?? '', //子订单号
                "type" => $this->platformType, //订单类型
                "payment" => $total, //实付金额
                "total_fee" => $total, //总金额
                //"discount_fee" => formatToYuan($item['promotion_amount']), //优惠金额
                "goods_pic" => isset($sku['logo']) ? 'https://img10.360buyimg.com/n0/'.$sku['logo'] : "", //商品图片
                "goods_title" => $wareTitle,//从SKU里面获取商品名称
                "goods_price" => $total, //商品单价
                "goods_num" => $goodsNum, //商品数量
                "send_num" => $sendNum,//已发货数量
                "num_iid" => $item['wareId'] ?? '', //商品id
                "sku_id" => $item['skuId'] ?? '', //sku id
                //"sku_value" =>$item['skuName']?? $sku['saleAttrs'][0]['attrValueAlias'][0] ?? 0, //sku值
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => $sku['outerId'] ?? null, //商家外部商品编码,商家编码code
                "outer_sku_iid" => $item['outerSkuId'] ?? '', //商家外部sku编码
                "order_created_at" => $trade['orderStartTime'], //订单创建时间
                "order_updated_at" => $trade['modified'], //订单修改时间
                "sku_uuid" => $skuUuid, //sku uuid
                "status" => $orderItemStatus,
                "refund_status" => $refundStatus, //
                "refund_sub_status"=>$refundSubStatus,
                'is_comment' => (isset($item['is_comment']) && $item['is_comment'] == Order::IS_COMMENT_YES) ? Order::IS_COMMENT_YES : 0,
                // 是否评价
                'product_no' => $item['productNo'] ?? '',
            ];
            $orderItems[] = $orderItem;
        }
        $orderItemsKeyBySkuUuId = array_column($orderItems, null, 'sku_uuid');
        //原始收件人，不是集运的
        $originalConsigneeInfo = $trade['originalConsigneeInfo'] ?? $trade['consigneeInfo'];

        Log::info('originalConsigneeInfo', $originalConsigneeInfo);
        $consigneeInfo = $trade['consigneeInfo'];
        $oaid = ArrayUtil::getArrayValue($originalConsigneeInfo, 'oaid');
        //加密信息
        //如果有oaid只需要有掩码做展示，而不需要加密内容了,不管是集运的还是不集运的都用$consigneeInfo里面的联系人信息
        $cipher_info = [
            'oaid' => $oaid,
            'receiver_phone_ciphertext' => $consigneeInfo['mobile'],
            'receiver_telephone_ciphertext' => $consigneeInfo['telephone'] ?? $consigneeInfo['mobile'],
            'receiver_phone_mask' => $consigneeInfo['mobile'],
            'receiver_name_mask' => $consigneeInfo['fullname'],
            'receiver_name_ciphertext' => $consigneeInfo['fullname'],
            'receiver_telephone_mask' => $consigneeInfo['telephone'] ?? $consigneeInfo['mobile'],
            'receiver_address_mask' => $consigneeInfo['fullAddress'],
            'receiver_address_ciphertext' => $consigneeInfo['fullAddress'] ?? '',
        ];

        //格式化订单已发货的包裹数据
        $expressCodeListFlipped = array_flip($this->expressCodeList);
        $logistics_data = [];
        $waybill=$trade['waybill'];
        Log::info('waybill',[$waybill]);
        if(!empty($waybill)){
            //整单发货把所有的sku信息都加进去
            $product_list = [];
            foreach ($trade['itemInfoList'] as $index => $item) {
                $itemExt = empty($item['itemExt']) ? [] : json_decode($item['itemExt'], true);


                $skuUuid = array_get($itemExt, 'skuUuid');
                $skuId = $item['skuId'];
//                Log::info('item',[$item,$orderItemsKeyBySkuUuId]);
                $orderItem = array_get($orderItemsKeyBySkuUuId, $skuUuid);
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $orderItem['oid'];
                $logisticsDataProductBo->sku_uuid = $skuUuid;
                $logisticsDataProductBo->sku_id = $skuId;
                $logisticsDataProductBo->outer_sku_id = $orderItem['outer_sku_id'] ?? null;
                $logisticsDataProductBo->num_iid = $orderItem['num_iid'];
                $logisticsDataProductBo->num = $orderItem['goods_num'];
                $logisticsDataProductBo->goods_title = $orderItem['goods_title'];
                $logisticsDataProductBo->sku_value = $orderItem['sku_value'];
                $product_list[] = $logisticsDataProductBo;
            }

            //整单发货的
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsId=$trade['logisticsId'];
            $logisticsDataBo->waybill_code = $waybill;
            $wpCode = $expressCodeListFlipped[$logisticsId];
            $logisticsDataBo->wp_code = $wpCode;
            $logisticsDataBo->wp_name = ExpressCompanyUtil::findExpressCompanyName($wpCode);
//            $logisticsDataBo->delivery_at = DateTimeUtil::toDateTimeTimeString($logistics_info['shipmentTime']) ?? null;
//            $logisticsDataBo->delivery_id = $logistics_info['shipmentId'];
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
            Log::info('获取整单发货的物流数据', $logistics_data);

        }

        //拆单发货
        foreach ($partialLogistics ?? [] as $logistics_info) {
            $product_list = [];
            foreach ($logistics_info['skus'] as $item) {
                $skuId = $item['skuId'];
                $skuUuid = $item['skuUuid'];
//                Log::info('item',[$item,$orderItemsKeyBySkuUuId]);
                $orderItem = array_get($orderItemsKeyBySkuUuId, $skuUuid);
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $orderItem['oid'];
                $logisticsDataProductBo->sku_uuid = $skuUuid;
                $logisticsDataProductBo->sku_id = $skuId;
                $logisticsDataProductBo->outer_sku_id = $orderItem['outer_sku_id'] ?? null;
                $logisticsDataProductBo->num_iid = $orderItem['num_iid'];
                $logisticsDataProductBo->num = $orderItem['goods_num'];
                $logisticsDataProductBo->goods_title = $orderItem['goods_title'];
                $logisticsDataProductBo->sku_value = $orderItem['sku_value'];
                $product_list[] = $logisticsDataProductBo;
            }
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['waybillId']; // 运单号
            $wpCode = $expressCodeListFlipped[$logistics_info['logicId']];
            $logisticsDataBo->wp_code = $wpCode;
            $logisticsDataBo->wp_name = ExpressCompanyUtil::findExpressCompanyName($wpCode);
            $logisticsDataBo->delivery_at = DateTimeUtil::toDateTimeTimeString($logistics_info['shipmentTime']) ?? null;
            $logisticsDataBo->delivery_id = $logistics_info['shipmentId'];
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }


        //收件人


        $sendPayMap = json_decode(array_get($trade, 'sendpayMap', '{}'), true);
//        $receiver_phone = $consigneeInfo['desen_mobile'] ?? $consigneeInfo['mobile'];
        $receiver_phone = implode('_', [
            $trade['pin'], $consigneeInfo['province'], $consigneeInfo['city'], $consigneeInfo['county'],
            $consigneeInfo['fullname']
        ]);
        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->platformType, //订单类型
//            "express_code" => array_search($trade['logistics_id'], $this->expressCodeList) ?: null , //快递公司代码
//            "express_no" => array_get($trade, 'logistics_code', null), //快递单号
            "buyer_id" => '', //买家ID
            "buyer_nick" => $trade['pin'], //买家昵称
//            "seller_nick" => $trade['shop_name'], //卖家昵称
            "order_status" => $orderStatus, //订单状态
            "refund_status" => $refundStatus, //退款状态
            "receiver_state" => $consigneeInfo['province'], //收货人省份
            "receiver_city" => $consigneeInfo['city'], //收货人城市
            "receiver_district" => $consigneeInfo['county'] ?? '', //收货人地区
            "receiver_town" => $consigneeInfo['town'] ?? null, //收货人街道
            "receiver_name" => $consigneeInfo['fullname'], //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_phone_mask" => $consigneeInfo['mobile'], //收货人手机
            'receiver_telephone' => ArrayUtil::getArrayValue($consigneeInfo, 'desen_telephone'), //收货人电话
            'receiver_telephone_mask' => ArrayUtil::getArrayValue($consigneeInfo, 'telephone'), //收货人电话
            "receiver_zip" => 0, //收件人邮编
            "receiver_address" => $consigneeInfo['fullAddress'], //收件人详细地址
            "payment" => $trade['orderSellerPrice'], //实付金额
            "total_fee" => $trade['orderTotalPrice'], //总金额
            "discount_fee" => $trade['sellerDiscount'], //优惠金额
            "post_fee" => $trade['freightPrice'], //运费
            //"seller_flag" => Order::FLAG_NONE, //卖家备注旗帜
            "seller_flag" => empty($trade['flag']) ? Order::FLAG_NONE : $this->formatOrderFlag($trade['flag']), //卖家备注旗帜
            "seller_memo" => empty($trade['remark']) ? '[]' : json_encode([$trade['remark']], JSON_UNESCAPED_UNICODE),
            //卖家备注
            "buyer_message" => $trade['orderRemark'] ?? null, //买家留言
            "has_buyer_message" => empty($trade['orderRemark']) ? 0 : 1, //买家留言
            "order_created_at" => $trade['orderStartTime'], //订单创建时间
            "order_updated_at" => $trade['modified'], //订单修改时间
//            "send_at" => !empty($trade['ship_time']) ? date('Y-m-d H:i:s', $trade['ship_time']) : null, //发货时间 //京东没有发货时间
            "pay_at" => !empty($trade['paymentConfirmTime']) ? $trade['paymentConfirmTime'] : null, //支付时间
            'is_comment' => 0, // 是否评价
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'cipher_info' => $cipher_info,
            'is_pre_sale' => 0, // 是否预售1是0否
            'promise_ship_at' => null, // 承诺发货时间
            'items' => $orderItems,
//            'store_type' => 0,
            'send_type' => $trade['idSopShipmenttype'] ?? 0, //配送类型
            'store_id' => $trade['storeId'] ?? 0, //仓库id
            'order_extra' => [
                'logistics_data' => jsonEncode($logistics_data),
                'extra_json' => jsonEncode(['sendPayMap' => $sendPayMap]),

            ],
            'is_remote_transit' => array_get($sendPayMap, 1086, 0) == 1 ? 1 : 0, //是否为偏远中转订单

        ];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'],
            $orderData['receiver_city'], $orderData['receiver_district']);

        return $orderData;
    }


    /**
     * 追加包裹，JD不支持，先写到商家备注里面
     * @param  string  $tid
     * @param  string  $wpCode
     * @param  string  $waybillCode
     * @param  array  $goodsList
     * @return bool
     * @throws ApiException
     */
    public function orderAppendPackages(string $tid, string $wpCode, string $waybillCode, array $goodsList = []): bool
    {
        $orderService = OrderServiceManager::create(PlatformConst::JD);
        $shop = $this->getShop();
        $userId = $shop->user_id;
        $orderService->setUserId($userId);
        $orderService->setShop($shop);
        $order = Order::firstByTid($tid);
        $flag = $order->flag;

        //请求平台修改留言备注
        $tid = $order->tid;
        $remark = $order->seller_memo;
        isJson($remark) ? $remarkArr = json_decode($remark, true) : $remarkArr = [$remark];
        $remarkArr[] = "包裹号:{$wpCode} 运单号:{$waybillCode}";
        $remarkStr = json_encode($remarkArr, JSON_UNESCAPED_UNICODE);
        return $orderService->sendEditSellerRemark($tid, $flag, $remarkStr);
    }

    public function getSkuInfo($skuId)
    {
        $client = $this->getClient();
        $req = new SkuReadSearchSkuListRequest();
        $req->setSkuId($skuId);
        $req->setSkuStatuValue([1, 2, 4]);
        $req->setField("wareId,skuId,status,saleAttrs,features,jdPrice,outerId,logo,skuName,wareTitle,fixedDeliveryTime,relativeDeliveryTime,modified,created,multiCateProps");

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        return $resp['jingdong_sku_read_searchSkuList_responce']['page']['data'][0] ?? [];
    }

    /**
     * @return TDEClient
     * @throws \ACES\Common\Exception\JosGwException
     * @throws \ACES\Common\Exception\VoucherInfoGetException
     * @throws \JsonMapper_Exception
     */
    public function getTdeByShop($shop): TDEClient
    {
        $appKey = config('socialite.jd.client_id');
        $appSecret = config('socialite.jd.client_secret');
        $tde = TDEClient::getInstance($shop['access_token'], $appKey, $appSecret);
        return $tde;
    }

    /**
     * @param  array  $tids
     * @return array
     * @throws ApiException
     */
    public function getRefundApplyIdsByTids(array $tids): array
    {
        $client = $this->getJdClient();
//        $result = [];
//        foreach ($tids as $tid) {
//            $tidRefundApplies = $this->getRefundAppliesByTid($tid);
//            if (!empty($tidRefundApplies)) {
//                $result[strval($tid)] = $tidRefundApplies;
//            }
//        }
        $data = [];
        foreach ($tids as $index => $tid) {
            $req = new PopAfsRefundapplyQuerylistRequest();
            $req->setOrderId($tid);
            $req->setPageIndex(1);
            $req->setPageSize(100);
            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl,
                'tid' => $tid,
            ];
        }
        $responses = $this->poolCurl($data, "post");
        $refundIds = [];
        //先根据tid把退款ID提取出来
        foreach ($responses as $response) {
            try {
                Log::info('售后详情', [$response]);
                $resp = $this->handleResp($response);
                $success = $resp['jingdong_pop_afs_refundapply_querylist_responce']['refundApplyResponse']['result_state'];
                if (!$success) {
                    Log::error('获取售后详情失败', [$response]);
                    continue;
                }
                $refundList = $resp['jingdong_pop_afs_refundapply_querylist_responce']['refundApplyResponse']['results'];
                foreach ($refundList as $refund) {
                    $refundIds[] = $refund['id'];
                }
            } catch (\Exception $e) {
                Log::error('获取售后详情失败', [$response]);
//                $results[$index] = $this->formatToOrder($orderInfo);
            }
        }

        return $refundIds;
    }

    public function getBatchSkuInfo($orders): array
    {
        $client = $this->getJdClient();
        $data = [];
        foreach ($orders as $index => $order) {
            foreach ($order['itemInfoList'] as $item) {
                $req = new SkuReadSearchSkuListRequest();
                $req->setSkuId($item['skuId']);
                $req->setSkuStatuValue([1, 2, 4]);
                $req->setField("wareId,skuId,status,saleAttrs,features,jdPrice,outerId,logo,skuName,wareTitle,fixedDeliveryTime,relativeDeliveryTime,modified,created,multiCateProps");
                list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
                $data[$item['skuId']] = [
                    'params' => $apiParams,
                    'url' => $requestUrl
                ];
            }
        }

        $responses = $this->poolCurl($data, 'post');
        foreach ($orders as $index => $order) {
            foreach ($order['itemInfoList'] as $itemIdx => $item) {
                try {
                    $response = $responses[$item['skuId']];
                    $resp = $this->handleResp($response);
                    $respData = $resp['jingdong_sku_read_searchSkuList_responce']['page']['data'][0] ?? [];
                    $orders[$index]['itemInfoList'][$itemIdx]['skuInfo'] = $respData;
                } catch (\Exception $e) {
                    $orders[$index]['itemInfoList'][$itemIdx]['skuInfo'] = [];
                }

            }
        }
        return $orders;
    }

    public function getSkuList($goodId)
    {
        $client = $this->getClient();
        $req = new SkuReadSearchSkuListRequest();

        $req->setWareId($goodId);
        $req->setField('wareId,skuId,status,jdPrice,outerId,logo,skuName,wareTitle,modified,created,multiCateProps');


        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        return $resp['jingdong_sku_read_searchSkuList_responce']['page']['data'] ?? [];
    }

    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $index => $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param  array  $goods
     * @return array
     */
    public function formatToGood(array $goods): array
    {
        $skus = [];
        $skuList = $this->getSkuList($goods['wareId']);
        foreach ($skuList as $index => $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['skuId'],
                "sku_value" => $item['multiCateProps'][0]['attrValueAlias'][0] ?? 0,
                "outer_id" => $item['outerId'] ?? null,
                "outer_goods_id" => $goods['outerId'] ?? null,
                "sku_pic" => $item['jdPrice'] ?? null,
                "is_onsale" => 1,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $goods['wareId'],
            'outer_goods_id' => $goods['outerId'] ?? null,
            'goods_title' => $goods['title'],
            'goods_pic' => isset($goods['logo']) ? 'https://img10.360buyimg.com/n0/'.$goods['logo'] : '',
            'is_onsale' => Goods::IS_ONSALE_YES,
            'goods_created_at' => $goods['created'],
            'goods_updated_at' => $goods['modified'],
            'skus' => $skus
        ];
    }

    /**
     * @inheritDoc
     * @throws ApiException
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $this->hasNext = false;
        $c = JdClient::newSdkClient($this->getAccessToken());
        //$c->serverUrl = "https://127.0.0.1:8888/routerjson";
//        $c->appKey = config('socialite.jd.client_id');
//        $c->appSecret = config('socialite.jd.client_secret');
//        $c->accessToken = $this->getAccessToken();
        $req = new PopOrderSearchRequest();
        $req->setStartDate(date('Y-m-d H:i:s', $startTime));
        $req->setEndDate(date('Y-m-d H:i:s', $endTime));
        if ($isFirstPull) {
            $req->setOrderState('WAIT_SELLER_STOCK_OUT');
        } else {
            $req->setOrderState('WAIT_SELLER_STOCK_OUT,WAIT_GOODS_RECEIVE_CONFIRM,WAIT_SELLER_DELIVERY,FINISHED_L,TRADE_CANCELED,LOCKED');
        }
        $req->setOptionalFields(self::ORDER_FIELDS.'');
        $req->setPage($this->page);
        $req->setPageSize($this->pageSize);
        $req->setSortType(1); // 排序方式，默认升序,1是降序,其它数字都是升序
        $req->setDateType(1); // 查询时间类型，0按修改时间查询，1为按订单创建时间查询；其它数字同0，也按订单修改（订单状态、修改运单号）修改时间
        $resp = $c->execute($req, $c->accessToken);
        $resp = json_decode(json_encode($resp), true);

        $this->handleResp($resp, $req);
        Log::info("JD订单同步", $resp);
        if (!isset($resp['jingdong_pop_order_search_responce']['searchorderinfo_result']) ||
            !$resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['apiResult']['success']) {
            return [];
        }

        $orderInfoList = $resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['orderInfoList'];

        if (count((array) $orderInfoList) < $this->pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        if (empty($orderInfoList) || empty($orderInfoList[0])) {
            return [];
        }
        $batchSellerMemos = $this->getBatchSellerMemos($orderInfoList);

        $batchSellerMemos = array_pluck($batchSellerMemos, null, 'orderId');
        $orderInfoList = array_map(function ($orderInfo) use ($batchSellerMemos) {
            return array_merge($orderInfo, $batchSellerMemos[$orderInfo['orderId']] ?? []);
        }, $orderInfoList);
        $orderInfoList = $this->getBatchSkuInfo($orderInfoList);
        $tids = array_column($orderInfoList, 'orderId');
        $refundApplies = $this->batchGetRefundAppliesByTid($tids);
        $refundApplies = array_column($refundApplies, null, "tid");
        $orderInfoList = array_map(function ($orderInfo) use ($refundApplies) {
            $refundApplies = $refundApplies[$orderInfo['orderId']] ?? [];
            return array_merge($orderInfo, ["refundApplies" => $refundApplies]);
        }, $orderInfoList);

        $this->setOrderTotalCount($resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['orderTotal'] ?? -1);

        return $orderInfoList;
    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $req = new PopOrderGetRequest();
        $req->setOrderId($tid);
        $req->setOptionalFields(self::ORDER_FIELDS);

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        $this->handleResp($resp, $req);
        if (!isset($resp['jingdong_pop_order_get_responce']['orderDetailInfo']['apiResult']) || !$resp['jingdong_pop_order_get_responce']['orderDetailInfo']['apiResult']['success']) {
            return [];
        }
        $refundApplies = $this->getRefundAppliesByTid($tid);

        $orderInfo = $resp['jingdong_pop_order_get_responce']['orderDetailInfo']['orderInfo'];
        $orderInfo['refundApplies'] = $refundApplies;
        return $orderInfo;

    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        $client = $this->getClient();
        $req = new WareReadSearchWare4ValidRequest();
        $req->setPageNo($currentPage);
        $req->setPageSize($pageSize);
        $req->setWareStatusValue('8');
        $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'])) {
            $this->goodsTotalCount = $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'];
        } else {
            $this->goodsTotalCount = 0;
        }

        $arr = [];
        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page'])) {
            if ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageNo'] <
                ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'] / $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageSize'])) {
                $this->hasNext = true;
            }

            return $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['data'];
        }

        return $arr;

    }

    /**
     * @param  string  $tid
     * @param  string  $expressCode
     * @param  string  $expressNo
     * @param  array  $orderItemOId
     * @param  bool  $silent
     * @return bool
     * @throws ApiException
     * @throws ErrorCodeException
     * @throws OrderException
     */
    protected function deliverySellerOrders(
        string $tid,
        string $expressCode,
        string $expressNo,
        array $orderItemOId,
        bool $silent = true
    ) {
        $client = $this->getClient();
        $req = new PopOrderShipmentRequest();
        $req->setOrderId($tid);
        $logiCoprId = $this->expressCodeList[$expressCode] ?? 0;
        if (!$logiCoprId) {
            throw_error_code_exception(\App\Http\StatusCode\StatusCode::WP_CODE_UNMATCHED);
        }
        $req->setLogiCoprId($logiCoprId);
        $req->setLogiNo($expressNo);

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        Log::info('jd 发货接口返回:'.json_encode($resp));

        if ($resp['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['success']) {
            return true;
        }

        $this->handleDeliveryErrorCode($resp);
        return false;
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrdersForOpenApi(
        string $tid,
        string $expressCode,
        string $expressNo,
        array $orderItemOId
    ) {
        $client = $this->getClient();
        $req = new PopOrderShipmentRequest();
        $req->setOrderId($tid);
        $req->setLogiCoprId($this->expressCodeList[$expressCode]);
        $req->setLogiNo($expressNo);

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        Log::info('对外jd发货接口返回:'.json_encode($resp));

        $this->handleResp($resp, $req);
        if ($resp['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['success'] ?? false) {
            return true;
        }
        $this->handleDeliveryErrorCode($resp);
        return false;
    }

    /**
     * @inheritDoc
     * @throws ApiException
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $this->hasNext = false;
        $c = new JdClientSdk();
        //$c->serverUrl = "https://127.0.0.1:8888/routerjson";
        $c->appKey = config('socialite.jd.client_id');
        $c->appSecret = config('socialite.jd.client_secret');
        $c->accessToken = $this->getAccessToken();
        $req = new PopOrderSearchRequest();
        $req->setStartDate(date('Y-m-d H:i:s', $startTime));
        $req->setEndDate(date('Y-m-d H:i:s', $endTime));
        if ($isFirstPull) {
            $req->setOrderState('WAIT_SELLER_STOCK_OUT');
        } else {
            $req->setOrderState('WAIT_SELLER_STOCK_OUT,WAIT_GOODS_RECEIVE_CONFIRM,WAIT_SELLER_DELIVERY,FINISHED_L,TRADE_CANCELED,LOCKED');
        }
        $req->setOptionalFields(self::ORDER_FIELDS);
        $req->setPage($this->page);
        $req->setPageSize($this->pageSize);
        $req->setSortType(2);
        $req->setDateType(2);
        $resp = $c->execute($req, $c->accessToken);
        $resp = json_decode(json_encode($resp), true);
        Log::info('订单列表', $resp);
        $this->handleResp($resp, $req);
        if (!isset($resp['jingdong_pop_order_search_responce']['searchorderinfo_result']) ||
            !$resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['apiResult']['success']) {
            return [];
        }

        $orderInfoList = $resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['orderInfoList'];
        if (count((array) $orderInfoList) < $this->pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        if (empty($orderInfoList) || empty($orderInfoList[0])) {
            return [];
        }
        $batchSellerMemos = $this->getBatchSellerMemos($orderInfoList);
        $batchSellerMemos = array_pluck($batchSellerMemos, null, 'orderId');
        $orderInfoList = array_map(function ($orderInfo) use ($batchSellerMemos) {
            return array_merge($orderInfo, $batchSellerMemos[$orderInfo['orderId']] ?? []);
        }, $orderInfoList);
        $orderInfoList = $this->getBatchSkuInfo($orderInfoList);
        $tids = array_column($orderInfoList, 'orderId');
        $refundApplies = $this->batchGetRefundAppliesByTid($tids);
        $orderInfoList = array_map(function ($orderInfo) use ($refundApplies) {
            $refundApplies = $refundApplies[$orderInfo['orderId']] ?? [];
            return array_merge($orderInfo, ["refundApplies" => $refundApplies]);
        }, $orderInfoList);

        $this->setOrderTotalCount($resp['jingdong_pop_order_search_responce']['searchorderinfo_result']['orderTotal'] ?? -1);

        return $orderInfoList;
    }

    /**
     * @inheritDoc
     */
    public function sendServiceInfo(): array
    {
        $client = $this->getClient();
        $req = new VasSubscribeGetByCodeRequest();
        $service_code = config('socialite.jd.service_item_code');
        $req->setItemCode($service_code);

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        \Log::info('sendServiceInfo:', [$req, $resp]);
        //\Log::info('xxxxxxresp:'.json_encode($resp));

        if (!isset($resp['jingdong_vas_subscribe_getByCode_responce']['end_date'])) {
            return [];
        }


        $expiredAt = ceil($resp['jingdong_vas_subscribe_getByCode_responce']['end_date'] / 1000);
        $shop = $this->getShop();
        $result = [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'expired_at' => date('Y-m-t H:i:s', $expiredAt),
            'version' => "专业版",
            'version_desc' => "专业",
            'pay_amount' => 0,
        ];

        return $result;
    }

    /**
     * @inheritDoc
     * @throws ErrorCodeException
     */
    protected function sendDecrypt($order)
    {
        $result = [];
        $filed = $order['filed'];
        $text = $order['text'];
        $tid = $order['tid'];
        if (in_array($filed, ['receiver_name', 'receiver_address'])) {
            $appKey = config('socialite.jd.client_id');
            $appSecret = config('socialite.jd.client_secret');
            $tde = TDEClient::getInstance($this->getAccessToken(), $appKey, $appSecret);
            Log::info("数据解密", ['field' => $filed, 'text' => $text]);
            $text = $tde->decrypt($text);
            $result['text'] = $text;
        } elseif ($filed == 'receiver_phone') {
            //手机号需要调用隐私号服务

            $client = $this->getClient();
            $req = new PopOrderGetmobilelistRequest();
            $req->setAppName('快递侠');
            $req->setOrderId($tid);

            $resp = $client->execute($req, $this->getAccessToken());
            $resp = json_decode(json_encode($resp), true);
            \Log::info("获取手机号", ['tid' => $tid, 'resp' => $resp]);
            $result = ArrayUtil::getArrayValue($resp, 'jingdong_pop_order_getmobilelist_responce.result');
            $code = ArrayUtil::getArrayValue($result, 'code');
            if ($code != "200") {
                $message = ArrayUtil::getArrayValue($result, 'message');
                throw_error_code_exception([$code, $message]);
            }

            $result['text'] = ArrayUtil::getArrayValue($result, 'data.'.$tid.'.consMobilePhone');
        }
        $result[$tid] = $tid;

        return [$result];
    }

    /**
     * @param $param
     * @return array{receiverPhone:string ,receiverName:string,receiverAddress:string}
     * @throws ErrorCodeException
     */
    public function getEncryptData($param): array
    {
        $result = [];
        Log::info("获取加密数据", $param);
        $oaid = ArrayUtil::getArrayValue($param, 'oaid');

        if ($oaid) {
            $req = new JosOrderOaidDecryptRequest();

            $getReceiverInfoListReqVO = new GetReceiverInfoListReqVO();
            $getReceiverInfoListReqVO->setScenesType('1001');
            $getReceiverInfoListReqVO->setOrderType('POP');
            $getReceiverInfoListReqVO->setAppName('快递侠');
            $orderInfo = new Attribute1();
            $orderInfo->setOrderId($param['tid']);
            $orderInfo->setOaid($oaid);
            $orderInfos = [$orderInfo];
            $getReceiverInfoListReqVO->setOrderInfos($orderInfos);
            $req->setGetReceiverInfoListReqVO($getReceiverInfoListReqVO->getInstance());
            $client = $this->getClient();
            $resp = $client->execute($req, $this->getAccessToken());
            $resp = json_decode(json_encode($resp), true);
            Log::info("获取加密数据结果", ["request" => $req, "response" => $resp]);
            $returnType = ArrayUtil::getArrayValue($resp, 'jingdong_jos_order_oaid_decrypt_responce.returnType');
            $code = ArrayUtil::getArrayValue($returnType, 'code');
            if ($code != "200") {
                $message = ArrayUtil::getArrayValue($returnType, 'message');
                throw_error_code_exception([$code, $message]);
            }
            $data = ArrayUtil::getArrayValue($returnType, 'data')[0];
            $result['receiverName'] = ArrayUtil::getArrayValue($data, 'customerName');
            $result['receiverPhone'] = ArrayUtil::getArrayValue($data, 'consMobilePhone');
            $result['receiverAddress'] = ArrayUtil::getArrayValue($data, 'address');

        }
        Log::info('通过oaid获取加密数据', $result);
        return $result;
    }

    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder, $oldOrder): string
    {
        if (empty($cipherInfo['oaid'])) {
            return parent::buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder, $oldOrder);
        }
        $addressArr = [
            $cipherInfo['oaid'],
            $cipherInfo['receiver_name_mask'],
            $cipherInfo['receiver_address_mask'],
            $order['shop_id']
        ];
        return md5(implode(',', $addressArr));
    }

    public function sendCheckMergeOrder(array $orderList): array
    {
        $req = new JosOrderOaidMergeRequest();
        $request = new \JosOrderOaidMerge\Request();

        $merge_list = [];
        $input = [];
        foreach ($orderList as $order) {
            $item = new MergeItem();
            $input[] = ["tid" => $order['tid'], "oaid" => $order['order_cipher_info']['oaid']];
            $item->setOrderId($order['tid']);
            $item->setOaid($order['order_cipher_info']['oaid']);
            // 超过15天的订单不允许合单
            $merge_list[] = $item;
        }
//        $mergeListChunk = array_chunk($merge_list, 100); // 取100个

        //取前面的100个
        $merge_list = array_slice($merge_list, 0, 100);
        $request->setMergeList($merge_list);
        $client = $this->getClient();
        $req->setRequest($request->getInstance());


        $result = $client->execute($req, $this->accessToken);
        try {
            $response = $this->handleResp($result);
            $data = array_get($response, 'jingdong_jos_order_oaid_merge_responce.response.data');
        } catch (\Exception $e) {
            Log::info('sendCheckMergeOrder:error', [$e->getMessage()]);
            return [];
        }
        $result = [];
        $mergeResults = array_get($response, 'jingdong_jos_order_oaid_merge_responce.response.data.mergeResults', []);
        foreach ($mergeResults as $mergeResult) {
            $mergeOrderIds = array_get($mergeResult, 'mergeOrderIds');
            foreach ($mergeOrderIds as $mergeOrderId) {
                //”1111,2222” 合并的订单号,判断一下有没有,有的话日志输出
                if (strpos($mergeOrderId, ',') !== false) {
                    $orderIdArr = explode(',', $mergeOrderId);
                    Log::info('检查到订单可以合并', ['orderIdArr' => $orderIdArr]);
                }
                $result[] = $mergeOrderId;
            }
        }
        Log::info('订单合并检查', ["input" => $input, 'result' => $result]);
        return $result;

    }

    /*
     * @inheritDoc
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        $client = new JdClientSdk();
        $client->appKey = config('socialite.jd.client_id');
        $client->appSecret = config('socialite.jd.client_secret');
        $client->accessToken = $this->getAccessToken();
        $client->serverUrl = config('socialite.jd.gateway_url');

        return $client;
    }

    /**
     * @throws ApiException
     */
    protected function getJdClient(): JdClient
    {
        $client = new JdClient(config('socialite.jd.client_id'), config('socialite.jd.client_secret'));
        $client->setAccessToken($this->getAccessToken());
        return $client;
    }

    /**
     * @return JdClientSdk
     * <AUTHOR>
     */
    protected function getLogClient()
    {
        $jdClient = $this->getClient();
        $jdClient->serverUrl = 'https://api-log.jd.com/routerjson';
        return $jdClient;
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg(): bool
    {
        $logger = LogUtil::jdDbSync();
        $appKey = config('socialite.jd.client_id');
        $secretAccessKey = config("jd_cloud.secretKey");
        $accessKeyId = config("jd_cloud.accessKey");
        $ydRdsInstanceId = config('jd_cloud.dbPush.rdsInstanceId');
        $yundingdatapushClient = new YundingdatapushClient([
            'credentials' => new Credentials($accessKeyId, $secretAccessKey),
            'version' => 'latest',
            'scheme' => 'https'
        ]);
        $shop = $this->getShop();
        try {
            $param = [
                "datapushVender" => [
                    "appkey" => $appKey,
                    "ydRdsInstanceId" => $ydRdsInstanceId,
                    'venderId' => $shop->service_id
                ]
            ];
            $result = $yundingdatapushClient->addDatapushVender($param);

            $logger->info("订阅补偿", ["shopId" => $shop->id, "param" => $param, "result" => $result->toArray()]);
            return array_get($result->toArray(), 'result.success');
        } catch (\Throwable $e) {
            $logger->error("订阅补偿", [$e->getTraceAsString()]);
            return false;

        }
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    /**
     * @throws ApiException
     * @throws OrderException
     * @throws ErrorCodeException
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool
    {
        $client = $this->getClient();
        $req = new PopOrderModifyVenderRemarkRequest();
        $req->setOrderId($tid);
        if (isset($sellerFlag)) {
            if (array_key_exists($sellerFlag, self::ORDER_FLAG_MAP_REVERT)) {
                $req->setFlag(self::ORDER_FLAG_MAP_REVERT[$sellerFlag]);
            } else {
                throw_error_code_exception(ErrorConst::PARAM_ERROR, null, $this->flagErrorMsg($sellerFlag));
            }
        }
        $req->setRemark($sellerMemo);

        $resp = $client->execute($req, $client->accessToken);
        $resp = $this->handleResp($resp);
        Log::info('修改订单备注', [$resp, $req->getApiParas()]);

        if (isset($resp['jingdong_pop_order_modifyVenderRemark_responce']['modifyvenderremark_result']) &&
            $resp['jingdong_pop_order_modifyVenderRemark_responce']['modifyvenderremark_result']['success']) {
            return true;
        }

        return false;

    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        $client = $this->getClient();
        $req = new PopFwOrderListwithpageRequest();
        $req->setPageSize($this->pageSize);
        $req->setFwsPin(config('socialite.jd.fus_pin'));
        $req->setCurrentPage($this->page);
        $req->setServiceCode(config('socialite.jd.service_code'));

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        \Log::info('jd platform order response:'.json_encode($resp));

        return $resp['jingdong_pop_fw_order_listwithpage_responce']['returnType']['orderList'] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptBatch()
    {
        // TODO: Implement cipherDecryptBatch() method.
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptMaskBatch(array $list): array
    {
        $returnData = [];
//        Log::info("解密数据掩码", $list);
        foreach ($list as $item) {

            $returnData[] = [
                'tid' => $item['tid'],
                'text' => $item['text']
            ];
        }

        return $returnData;
    }

    /**
     * @inheritDoc
     */
    public function cipherExtractSearch(string $encryptedData): string
    {
        //解密
        $accessToken = $this->getAccessToken();
        $appKey = config('socialite.jd.client_id');
        $appSecret = config('socialite.jd.client_secret');
        $tde = TDEClient::getInstance($accessToken, $appKey, $appSecret);
        $ct = $tde->decrypt($encryptedData);

        //根据明文数据获取索引
        return $tde->calculateStringIndex($ct);
    }

    public function getSearchIndex(string $text, string $type): string
    {
        if ($type == 'name') {
            $accessToken = $this->getAccessToken();
            $appKey = config('socialite.jd.client_id');
            $appSecret = config('socialite.jd.client_secret');
            $tde = TDEClient::getInstance($accessToken, $appKey, $appSecret);
            $text = empty($text) ? 'test' : $text;
            $text = $tde->calculateStringIndex($text);
        } else {
            $client = $this->getClient();
            $req = new PopOrderEncryptMobileNumRequest();
            $req->setMobile($text);


            $resp = $client->execute($req, $this->getAccessToken());
            $resp = json_decode(json_encode($resp), true);
            $text = $resp['jingdong_pop_order_encryptMobileNum_responce']['result']['data']['cipherText'] ?? $text;
        }

        return $text;
    }

    /**
     * @throws ApiException
     * @throws OrderException
     */
    public function sendBatchGetOrderInfo($orders, $safe = false): array
    {

        $client = $this->getJdClient();
        $data = $results = [];
        $tids = array_column($orders, 'tid');
        foreach ($orders as $index => $order) {
            $request = new PopOrderGetRequest();
            $request->setOrderId($order['tid']);
            $request->setOptionalFields(self::ORDER_FIELDS);
            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($request);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }

        $response = $this->poolCurl($data, 'post');
        foreach ($response as $index => $data) {
            try {
//                Log::info('订单详情', [$data]);
                $resp = $this->handleResp($data);
                $orderInfo = $resp['jingdong_pop_order_get_responce']['orderDetailInfo']['orderInfo'] ?? [];
                if (!isset($resp['jingdong_pop_order_get_responce']['orderDetailInfo']['apiResult'])
                    || !$resp['jingdong_pop_order_get_responce']['orderDetailInfo']['apiResult']['success']
                    || empty($orderInfo)) {
                    $results[$index] = [];
                    continue;
                }
                $results[$index] = $orderInfo;
//                $results[$index] = $this->formatToOrder($orderInfo);
            } catch (\Exception $e) {
                if (!$safe) {
                    throw $e;
                }
                $results[$index] = [];
            }
        }
        if (empty($results) || empty($results[0])) {
            return [];
        }
        $batchSellerMemos = $this->getBatchSellerMemos($results);
        $batchSellerMemos = array_pluck($batchSellerMemos, null, 'orderId');
        $results = array_map(function ($orderInfo) use ($batchSellerMemos) {
            return array_merge($orderInfo, $batchSellerMemos[$orderInfo['orderId']] ?? []);
        }, $results);
        $refundApplies = $this->batchGetRefundAppliesByTid($tids);
        $results = array_map(function ($orderInfo) use ($refundApplies) {
            $refundApplies = $refundApplies[$orderInfo['orderId']] ?? [];
            return array_merge($orderInfo, ["refundApplies" => $refundApplies]);
        }, $results);
        return $this->getBatchSkuInfo($results);
    }

    /**
     * 日志批量上传
     * @see https://open.kwaixiaodian.com/docs/api?apiName=open.security.log.batch&categoryId=62&version=1
     * @param  string  $method
     * @param  array  $data
     * @return bool
     * @throws OrderException
     * <AUTHOR>
     */
    public function sendLogUploadBatch(string $method, array $data): bool
    {
        $c = $this->getLogClient();

        $data = json_encode($data);
        $req = new IsvUploadBatchLogRequest();
        $req->setJosAppKey($c->appKey);
        $req->setData($data);
        $req->setTimeStamp(time() * 1000);
        $req->setType($method);
        $resp = $c->execute($req, $c->accessToken);
        $resp = $this->handleResp($resp, $req);
        if (!empty($resp['jingdong_isv_uploadBatchLog_response']['code'])) {
            return false;
        }
        return true;
    }

    /**
     * 日志批量上传
     * https://open.jd.com/home/<USER>/doc/api?apiCateId=147&apiId=3989&apiName=jingdong.isv.uploadBatchLog
     * @see operation 看 https://open.jd.com/home/<USER>/doc/common?listId=899
     * @param  Event  $event
     * @return bool
     * @throws OrderException
     */
    public function reportBatchLogByEvent(Event $event): bool
    {
        $data = [];
        if (!($event instanceof BaseRequestEvent)) {
            return false;
        }
        Log::debug('uploadBatchLogByEvent', [class_basename($event)]);
        /** @var UserLoginEvent $event */
        $commonData = [
            'josAppKey' => $this->appKey,
            'user_ip' => $event->clientIp,
            'user_id' => $event->shop->identifier,
            'app_name' => config('app.app_domain'),
            'device_id' => $event->clientInput['jd_device_id'] ?? '',
            'time_stamp' => $event->time * 1000,
        ];
        if ($event instanceof UserLoginEvent) {
            $method = 'login';
            if (empty($commonData['device_id'])) {
                return true;
            }
            $data[] = array_merge($commonData, [
                'result' => $event->loginStatus == $event::LOGIN_STATUS_SUCCESS ? 0 : 1,
                'message' => $event->loginStatus == $event::LOGIN_STATUS_SUCCESS ? 'success' : 'fail',
                'jd_id' => $event->shop->shop_name,
            ]);

        } elseif ($event instanceof OrderQueryEvent) {
            $method = 'order';
            foreach (array_chunk($event->orderIds, 100) as $orderIdArr) {
                $data[] = array_merge($commonData, [
                    'order_ids' => implode(',', $orderIdArr),
                    'url' => $event->clientUrl,
                    'operation' => 1,
                ]);
            }

        } elseif ($event instanceof OrderPrintEvent) {
            $method = 'order';
            foreach (array_chunk($event->orderIds, 100) as $orderIdArr) {
                $data[] = array_merge($commonData, [
                    'order_ids' => implode(',', $orderIdArr),
                    'url' => $event->clientUrl,
                    'operation' => 2,
                ]);
            }
        } elseif ($event instanceof OrderDecryptEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $data[] = array_merge($commonData, [
                    'order_ids' => "$orderId",
                    'url' => $event->clientUrl,
                    'operation' => 5,
                ]);
            }
        } elseif ($event instanceof OrderUpdateEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $data[] = array_merge($commonData, [
                    'order_ids' => "$orderId",
                    'url' => $event->clientUrl,
                    'operation' => 9,
                ]);
            }
        } elseif ($event instanceof SqlLogEvent) {
            $method = 'sql';
            $data[] = array_merge($commonData, [
                'url' => $event->clientUrl,
                'db' => config('database.connections.mysql.host'),
                'sql' => $event->sql,
            ]);
        } else {
            return true;
        }
        if (empty($data)) {
            return true;
        }
        return $this->sendLogUploadBatch($method, $data);
    }

    /**
     * 处理响应
     *
     * @param $response
     * @param  null  $request
     * @return array
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    public function handleResp($response, $request = null): array
    {
        return $this->getJdClient()->handleResp($response, $request);
    }

    private function handleDeliveryErrorCode($result)
    {
        $msg = $result['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['chineseErrCode'] ?? '';
        empty($msg) && $msg = json_encode($result, JSON_UNESCAPED_UNICODE);
        switch ($result['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['errorCode']) {
            //{"jingdong_pop_order_shipment_responce":{"code":"0","sopjosshipment_result":{"englishErrCode":"orders have been out of library","errorCode":"10400001","success":false,"chineseErrCode":"218487032232订单已出库"}}}
            case '50010':
                throw new ApiException(ErrorConst::PLATFORM_SERVER_ERROR);
            default:
                throw new OrderException('京东服务异常：'.$msg);
        }
    }

    public function getSellerMemo($tid)
    {
        $client = $this->getClient();
        $req = new OrderVenderRemarkQueryByOrderIdRequest();
        $req->setOrderId($tid);
        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        if (isset($resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['api_jos_result']['success']) &&
            $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['api_jos_result']['success']) {
            return [
                'flag' => $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['vender_remark']['flag'],
                'remark' => $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['vender_remark']['remark']
            ];
        }

        return [];
    }

    public function getRefundOrderInfo($afterSaleId)
    {
        return parent::getRefundOrderInfo($afterSaleId); // TODO: Change the autogenerated stub
    }

    /**
     * 获取订单的申请退款信息
     * @param  array  $tids
     * @return array{string:OrderRefund[]} $refundApplies key是tid
     * @throws ApiException
     */
    public function batchGetRefundAppliesByTid(array $tids): array
    {
        //先根据tid获取售后ID
        $refundIds = $this->getRefundApplyIdsByTids($tids);
        Log::info('获取售后ID', ["tids" => $tids, 'refundIds' => $refundIds]);

        $refundApplies = new Collection($this->getRefundAppliesByRefundIds($refundIds));

        $refundAppliesGroupByTid =$refundApplies->groupBy('tid')->toArray() ;      // ArrayUtil::array_group_by($refundApplies, null, 'tid');
        Log::info('获取售后信息', ["tids" => $tids, 'refundAppliesGroupByTid' => $refundAppliesGroupByTid]);
        return $refundAppliesGroupByTid;


    }

    /**
     * 获取订单的申请退款信息
     * @param  string  $tid
     * @return array
     */
    public function getRefundAppliesByTid(
        string $tid
    ): array {
        $client = $this->getClient();
        $req = new PopAfsRefundapplyQuerylistRequest();
        $req->setOrderId($tid);
        $req->setPageIndex(1);
        $req->setPageSize(100);
        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        Log::info('获取售后信息', ["tid" => $tid, 'resp' => $resp]);
        return array_get($resp, 'jingdong_pop_afs_refundapply_querylist_responce.refundApplyResponse.results', []);
    }

//    /**
//     * 批量获取售后详情
//     * @param  array  $refundIds
//     * @return CurlResult[]
//     * @throws ApiException
//     */
//    public
//    function getBatchRefundDetailsByRefundIds(
//        array $refundIds
//    ): array {
//
//        /**
//         * @var  OrderRefund[] $results
//         */
//        $results = [];
//        $responses = $this->poolCurl($data, 'post');
//        foreach ($responses as $index => $response) {
//            $refundId = $data[$index]['refundId'];
//            try {
//                $resp = $this->handleResp($response);
//                Log::info('jdOrderImpl', ['response' => $resp, "refundId" => $refundId]);
//
//                $success = array_get($resp, 'jingdong_pop_afs_soa_refundapply_queryById_responce.queryResult.success',
//                    false);
//                if (!$success) {
//                    Log::error("获取订单退款详情失败", ["refundId" => $refundId, "response" => $response]);
////                    $results[] = CurlResult::ofError("400",
////                        array_get($resp, 'jingdong_pop_afs_soa_refundapply_queryById_responce.queryResult.errorCode'),
////                        array_get($resp,
////                            'jingdong_pop_afs_soa_refundapply_queryById_responce.queryResult.errorMessage'));
//                    continue;
//                }
//                $refunds = array_get($resp,
//                    'jingdong_pop_afs_soa_refundapply_queryById_responce.queryResult.result', []);
//                foreach ($refunds as $refund) {
//
//                    $results[] = $this->formatRefund($refund);
//                }
//
//            } catch (\Exception $e) {
//                Log::error("获取订单退款详情失败", ["refundId" => $refundId, "response" => $response]);
//            }
//        }
//        return $results;
//    }

    /**
     * @throws ApiException
     */
    public function getBatchSellerMemos(
        $orders
    ): array {
        $client = $this->getJdClient();
        $data = $results = [];
        foreach ($orders as $index => $order) {
            $req = new OrderVenderRemarkQueryByOrderIdRequest();
            $req->setOrderId($order['orderId']);
            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }

        $responses = $this->poolCurl($data, 'post');
        foreach ($responses as $index => $response) {
            try {
                $resp = $this->handleResp($response);
                $orderId = $orders[$index]['orderId'];
                Log::info('jdOrderImpl', ['response' => $resp, "orderId" => $orderId]);
                $success = $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['api_jos_result']['success'] ?? false;
                if (!$success) {
                    $results[$index] = [];
                    continue;
                }
                $results[$index] = [
                    'orderId' => $orderId,
                    'flag' => $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['vender_remark']['flag'] ?? 0,
                    'remark' => $resp['jingdong_order_venderRemark_queryByOrderId_responce']['venderRemarkQueryResult']['vender_remark']['remark'] ?? ''
                ];
            } catch (\Exception $e) {
                $results[$index] = [];
            }
        }
        return $results;
    }

    /**
     * 重新订单发货
     * @param  OrderDeliverAgainRequest  $orderDeliverAgainRequest
     * @return bool
     * @throws ApiException
     * @throws OrderException
     */
    protected
    function deliverySellerOrdersAgain(
        OrderDeliverAgainRequest $orderDeliverAgainRequest
    ): bool {
        Log::info('重新发货', ["orderDeliverAgainRequest" => $orderDeliverAgainRequest]);
        $client = $this->getClient();
        $tid = $orderDeliverAgainRequest->tid;
        $expressCode = $orderDeliverAgainRequest->wpCode;
        $expressName = ExpressCompanyUtil::findExpressCompanyName($expressCode);
        $expressNo = $orderDeliverAgainRequest->waybillCode;
        $deliveryId = $orderDeliverAgainRequest->deliveryId;

        $logiCorpId = $this->expressCodeList[$expressCode];
        if (!empty($deliveryId)) {
            //部分重发
            $req = new PopOrderWaybillUpdateRequest();
            $request = new \PopOrderWaybillUpdate\Request();
            $request->setBelongType(200);
            $data = new \PopOrderWaybillUpdate\Data();
            $data->setOrderId($tid);
            $shipmentId = $deliveryId;

            $partialShipmentInfoListItem = new PartialShipmentInfoListItem();
            $partialShipmentInfoListItem->setShipmentId($shipmentId);
            $partialShipmentInfoListItem->setLogicId($logiCorpId);
            $partialShipmentInfoListItem->setLogicName($expressName);
            $partialShipmentInfoListItem->setWaybillId($expressNo);

            $data->setPartialShipmentInfoList([$partialShipmentInfoListItem]);


            $request->setData($data);
            $req->setRequest($request->getInstance());
            $response = $client->execute($req, $client->accessToken);
            $responseArr = $this->handleResp($response);
            Log::info('部分发货重新发货', [$responseArr]);
            $boolResult = array_get($responseArr, 'jingdong_pop_order_waybill_update_responce.response.code',
                    '') == 200;
            if (!$boolResult) {
                $errMsg = array_get($responseArr, 'jingdong_pop_order_waybill_update_responce.response.msg');
                throw new OrderException($errMsg);
            }

        } else {
            //整单重发
            $req = new PopOrderSopLogisticsUpdateRequest();
            $oneGlobalOrderModelNoLogistic = new OneGlobalOrderModelNoLogistic();
            $oneGlobalOrderModelNoLogistic->setOrderId($tid);
            $oneGlobalOrderModelNoLogistic->setNoCheckLogistics(false);
            $req->setOneGlobalOrderModelNoLogistic($oneGlobalOrderModelNoLogistic->getInstance());
            $logisticsGlobalModelList = [];

            $logisticsGlobalModel = new LogisticsGlobalModel();
            $logisticsGlobalModel->setLogiScope(0);
            $logisticsGlobalModel->setLogiNoList([$expressNo]);

            $logisticsGlobalModel->setLogiCoprId($logiCorpId);
            $logisticsGlobalModelList[] = $logisticsGlobalModel->getInstance();
            $req->setLogisticsGlobalModelList($logisticsGlobalModelList);

            $response = $client->execute($req, $client->accessToken);
            $responseArr = $this->handleResp($response);

            $boolResult = array_get($responseArr,
                'jingdong_pop_order_sop_logistics_update_responce.returnType.success');
            Log::info('重新发货',
                ["tid" => $tid, "req" => json_encode($req), 'response' => $response, "boolResult" => $boolResult]);
            if (!$boolResult) {
                $errMsg = array_get($responseArr,
                    'jingdong_pop_order_sop_logistics_update_responce.returnType.resultDescribe');
                throw new OrderException($errMsg);
            }
        }
        return $boolResult;
    }

    /**
     * 检查授权状态
     * @return bool
     * <AUTHOR>
     */
    public
    function checkAuthStatus(): bool
    {
        try {
            $this->sendGetGoods(1, 1);
            return true;
        } catch (\Exception $ex) {
            return false;
        }
    }

    /**
     * 请求查询交易订单的 Tid
     * @param  array  $query_list
     * <AUTHOR>
     */
    public
    function sendQueryTradeTid(
        array $query_list
    ) {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * 请求地址列表
     * @return array [{"name":"海南省","code":460000,"parent_code":1,"level":1,"sub":[{"name":"海口市","code":460100,"parent_code":460000,"level":2,"sub":[{"name":"其它区","code":460109,"parent_code":460100,"level":3}]}]}]
     * <AUTHOR>
     */
    public
    function sendAddress(): array
    {
        $resArr = [];
        $client = $this->getClient();
        $req = new AreasProvinceGetRequest();
        //省份
        $resp = $client->execute($req, $this->getAccessToken());
        $result1 = json_decode(json_encode($resp), true);
        Log::info('sendAddress count:'.count($result1['jingdong_areas_province_get_responce']['baseAreaServiceResponse']['data']));
        //市 直辖市 北京、天津、上海、重庆
        $specialCity = ['北京', '天津', '上海', '重庆'];
        foreach ($result1['jingdong_areas_province_get_responce']['baseAreaServiceResponse']['data'] as $index => $province) {
            $resArr[] = [
                'code' => $province['areaId'],
                'level' => $province['level'],
                'name' => $province['areaName'],
                'parent_code' => 1,
            ];
            $req = new AreasCityGetRequest();
            $req->setParentId($province['areaId']);
            $req->setVersion(0);
            $resp = $client->execute($req, $this->getAccessToken());
            $result2 = json_decode(json_encode($resp), true);
            if (in_array($province['areaName'], $specialCity)) {
                switch ($province['areaName']) {
                    case  '北京':
                        $code = 110000;
                        break;
                    case '天津':
                        $code = 120000;
                        break;
                    case '上海':
                        $code = 310000;
                        break;
                    case '重庆':
                        $code = 500000;
                        break;
                }
                $resArr[] = [
                    'code' => $code,
                    'level' => $province['level'] + 1,
                    'name' => $province['areaName'].'市',
                    'parent_code' => $province['areaId'],
                ];
            }

            // 区
            foreach ($result2['jingdong_areas_city_get_responce']['baseAreaServiceResponse']['data'] as $city) {
                $resArr[] = [
                    'code' => $city['areaId'],
                    'level' => in_array($province['areaName'], $specialCity) ? $city['level'] + 1 : $city['level'],
                    'name' => $city['areaName'],
                    'parent_code' => in_array($province['areaName'], $specialCity) ? $code : $city['parentId'],
                ];
                $req = new AreasCountyGetRequest();
                $req->setParentId($city['areaId']);
                $req->setVersion(0);
                $resp = $client->execute($req, $this->getAccessToken());
                $result3 = json_decode(json_encode($resp), true);

                foreach ($result3['jingdong_areas_county_get_responce']['baseAreaServiceResponse']['data'] as $county) {
                    $resArr[] = [
                        'code' => $county['areaId'],
                        'level' => in_array($province['areaName'],
                            $specialCity) ? $county['level'] + 1 : $county['level'],
                        'name' => $county['areaName'],
                        'parent_code' => $county['parentId'],
                    ];
                    if (in_array($province['areaName'], $specialCity)) {
                        continue;
                    }
                    $req = new AreasTownGetRequest();
                    $req->setParentId($county['areaId']);
                    $req->setVersion(0);
                    $resp = $client->execute($req, $this->getAccessToken());
                    $result4 = json_decode(json_encode($resp), true);
                    foreach ($result4['jingdong_areas_town_get_responce']['baseAreaServiceResponse']['data'] as $town) {
                        $resArr[] = [
                            'code' => $town['areaId'],
                            'level' => $town['level'],
                            'name' => $town['areaName'],
                            'parent_code' => $town['parentId'],
                        ];
                    }
                }
            }
            Log::info('sendAddress item:'.($index + 1));
        }

        return $resArr;
    }

    /**
     * @param  string  $type
     * @param  string  $search
     * @return array
     */
    public function getQueryTradeOrderId(
        string $type,
        string $search
    ): array {
        try {
            if (empty($search)) {
                return [];
            }
            Log::info('getQueryTradeOrderId type:'.$type.' search:'.$search);
            $req = new  JosOrderOaidSearchRequest();
            $request = new \JosOrderOaidSearch\Request();
            if ($type == 'receiver_name') {
                $request->setReceiverName($search);
            }
            if ($type == 'receiver_phone') {
                $request->setReceiverMobile($search);
            }
            $request->setSceneId("1002");
            $req->setRequest($request->getInstance());
            $resp = $this->getClient()->execute($req, $this->getAccessToken());
            $this->handleResp($resp);
            $result = json_decode(json_encode($resp), true);
            Log::info('getQueryTradeOrderId response:'.json_encode($result));
            $response = ArrayUtil::getArrayValue($result, 'jingdong_jos_order_oaid_search_responce.response');
            $code = ArrayUtil::getArrayValue($response, 'code');
            if ($code != "200") {
                $message = ArrayUtil::getArrayValue($response, 'msg');
                throw_error_code_exception([$code, $message]);
            }
            $orderIdList = ArrayUtil::getArrayValue($response, 'data.orderIdList', []);
            $tidArr = array_pluck($orderIdList, 'orderId');
            if (!empty($tidArr)) {
                $shop = $this->getShop();
                return Order::query()
                    ->select('id')
                    ->where('shop_id', $shop->id)
                    ->whereIn('tid', $tidArr)
                    ->get()
                    ->pluck('id')
                    ->toArray();
            }
        } catch (\Exception $exception) {
            Log::error('getQueryTradeOrderIdByEncrytion error:'.$exception->getMessage());
        }
        return [];
    }

    /**
     * 历史的基于加密信息进行订单搜索
     * @param $type
     * @param $search
     * @return array
     * @throws ApiException
     * @throws \ACES\Common\Exception\JosGwException
     * @throws \ACES\Common\Exception\VoucherInfoGetException
     * @throws \JsonMapper_Exception
     */
    public function getQueryTradeOrderIdByEncrytion(
        $type,
        $search
    ) {
        if ($type == 'receiver_name') {
            $accessToken = $this->getAccessToken();
            $appKey = config('socialite.jd.client_id');
            $appSecret = config('socialite.jd.client_secret');
            $tde = TDEClient::getInstance($accessToken, $appKey, $appSecret);
            $text = empty($search) ? 'test' : $search;
            $text = $tde->calculateStringIndex($text);
        } else {
            $client = $this->getClient();
            $req = new PopOrderEncryptMobileNumRequest();
            $req->setMobile($search);


            $resp = $client->execute($req, $this->getAccessToken());
            $resp = json_decode(json_encode($resp), true);
            $text = $resp['jingdong_pop_order_encryptMobileNum_responce']['result']['data']['cipherText'] ?? $search;
        }

        $conditions = [];
        $beginAt = \request()->input('begin_at');
        $endAt = \request()->input('end_at');
        $timeField = \request()->input('timeField');

        if ($timeField && $beginAt && $endAt) {
            $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        $shop = $this->getShop();
        $idArr = Order::query()->select('id')
            ->where('shop_id', $shop->id)
            ->where($conditions)
            ->where($type, $text)
            ->get()
            ->pluck('id')
            ->toArray();

        return $idArr;
    }

    /**
     * @inheritDoc
     */
    public
    function batchDeliveryOrders(
        array $orderDeliveryRequests
    ): array {
        $client = $this->getJdClient();
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $orderItemOId = $orderDeliveryRequest->orderItemOId;
            $req = new PopOrderShipmentRequest();
            $req->setOrderId($orderDeliveryRequest->tid);
            $req->setLogiCoprId($this->expressCodeList[$orderDeliveryRequest->expressCode]);
            $req->setLogiNo($orderDeliveryRequest->expressNo);

            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
            $requestId = $orderDeliveryRequest->getRequestId();
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl,
            ];
        }
        $responseData = $this->poolCurl($requestData, 'post');
        $results = [];
        foreach ($responseData as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $thisRequestData = $requestData[$orderDeliveryRequest->getRequestId()] ?? [];
                $data = $this->handleResp($result, $thisRequestData);

                if ($data['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['success']) {
                    $commonResponse->setSuccess(true);
                } else {
                    $commonResponse->code = $data['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['errorCode'] ?? 0;
                    $commonResponse->message = $data['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['chineseErrCode'] ?? '未知发货错误！';
                }
            } catch (\Exception $e) {
                Log::info("发货失败：".$e->getMessage());
                $commonResponse->code = $e->getCode();
                $commonResponse->message = $e->getMessage();

            } finally {
                $results[] = $commonResponse;
            }
        }

        return $results;
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public
    function batchGetRefundApplyList(
        $data
    ) {
        $client = $this->getClient();
        $req = new PopAfsSoaRefundapplyQueryPageListRequest();
        $req->setCheckTimeStart($data['checkTimeStart']);
        $req->setCheckTimeEnd($data['checkTimeEnd']);
        $req->setPageIndex($data['pageIndex']);
        $req->setPageSize($data['pageSize']);

        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);

        return $resp;
    }

    /**
     * 获取平台的
     * @param $data
     * @return mixe
     */
    public
    function printingPrintDataPullData(
        $data
    ) {
        $client = JdClient::newSdkClient();
        $req = new PrintingPrintDataPullDataRequest();
        $req->setParam1($data['param1']);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);

        return $resp;

    }

    /**
     *  获取京东运单号接口
     * @param $data
     * @return mixed
     */
    public
    function getEtmsWaybillcode(
        $data
    ) {
        $client = $this->getClient();
        $req = new EtmsWaybillcodeGetRequest();
        $req->setCustomerCode($data['customerCode']);
        $req->setPreNum($data['preNum']);
        $req->setOrderType($data['orderType']);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);
        return $resp;
    }

    public
    function sendEtmsWaybill(
        $data
    ) {
        $client = $this->getClient();
        $req = new EtmsWaybillSendRequest();
        $req->setDeliveryId($data['deliveryId'] ?? null);
        $req->setSalePlat($data['salePlat'] ?? null);
        $req->setCustomerCode($data['customerCode'] ?? null);
        $req->setOrderId($data['orderId'] ?? null);
        $req->setThrOrderId($data['thrOrderId'] ?? null);
        $req->setSelfPrintWayBill($data['selfPrintWayBill'] ?? null);
        $req->setPickMethod($data['pickMethod'] ?? null);
        $req->setPackageRequired($data['packageRequired'] ?? null);
        $req->setSenderName($data['senderName'] ?? null);
        $req->setSenderAddress($data['senderAddress'] ?? null);
        $req->setSenderTel($data['senderTel'] ?? null);
        $req->setSenderMobile($data['senderMobile'] ?? null);
        $req->setSenderPostcode($data['senderPostcode'] ?? null);
        $req->setReceiveName($data['receiveName'] ?? null);
        $req->setReceiveAddress($data['receiveAddress'] ?? null);
        $req->setProvince($data['province'] ?? null);
        $req->setCity($data['city'] ?? null);
        $req->setCounty($data['county'] ?? null);
        $req->setTown($data['town'] ?? null);
        $req->setProvinceId($data['provinceId'] ?? null);
        $req->setCityId($data['cityId'] ?? null);
        $req->setCountyId($data['countyId'] ?? null);
        $req->setTownId($data['townId'] ?? null);
        $req->setCountyId($data['countyId'] ?? null);
        $req->setSiteType($data['siteType'] ?? null);
        $req->setSiteId($data['siteId'] ?? null);
        $req->setSiteName($data['siteName'] ?? null);
        $req->setReceiveTel($data['receiveTel'] ?? null);
        $req->setReceiveMobile($data['receiveMobile'] ?? null);
        $req->setPostcode($data['postcode'] ?? null);
        $req->setPackageCount($data['packageCount'] ?? null);
        $req->setWeight($data['weight'] ?? null);
        $req->setVloumLong($data['vloumLong'] ?? null);
        $req->setVloumWidth($data['vloumWidth'] ?? null);
        $req->setVloumHeight($data['vloumHeight'] ?? null);
        $req->setVloumn($data['vloumn'] ?? null);
        $req->setDescription($data['description'] ?? null);
        $req->setCollectionValue($data['collectionValue'] ?? null);
        $req->setCollectionMoney($data['collectionMoney'] ?? null);
        $req->setGuaranteeValue($data['guaranteeValue'] ?? null);
        $req->setGuaranteeValueAmount($data['guaranteeValueAmount'] ?? null);
        $req->setSignReturn($data['signReturn'] ?? null);
        $req->setAging($data['aging'] ?? null);
        $req->setTransType($data['transType'] ?? null);
        $req->setRemark($data['remark'] ?? null);
        $req->setGoodsType($data['goodsType'] ?? null);
        $req->setOrderType($data['orderType'] ?? null);
        $req->setShopCode($data['shopCode'] ?? null);
        $req->setOrderSendTime($data['orderSendTime'] ?? null);
        $req->setWarehouseCode($data['warehouseCode'] ?? null);
        $req->setAreaProvId($data['areaProvId'] ?? null);
        $req->setAreaCityId($data['areaCityId'] ?? null);
        $req->setShipmentStartTime($data['shipmentStartTime'] ?? null);
        $req->setShipmentEndTime($data['shipmentEndTime'] ?? null);
        $req->setIdNumber($data['idNumber'] ?? null);
        $req->setAddedService($data['addedService'] ?? null);
        $req->setExtendField1($data['extendField1'] ?? null);
        $req->setExtendField2($data['extendField2'] ?? null);
        $req->setExtendField3($data['extendField3'] ?? null);
        $req->setExtendField4($data['extendField4'] ?? null);
        $req->setExtendField5($data['extendField5'] ?? null);

        $req->setSenderCompany($data['senderCompany'] ?? null);
        $req->setReceiveCompany($data['receiveCompany'] ?? null);
        $req->setFreightPre($data['freightPre'] ?? null);
        $req->setGoods($data['goods'] ?? null);
        $req->setGoodsCount($data['goodsCount'] ?? null);
        $req->setPromiseTimeType($data['promiseTimeType'] ?? null);
        $req->setFreight($data['freight'] ?? null);
        $req->setUnpackingInspection($data['unpackingInspection'] ?? null);
        $req->setFileUrl($data['fileUrl'] ?? null);
        $req->setCustomerBoxCode($data['customerBoxCode'] ?? null);
        $req->setCustomerBoxNumber($data['customerBoxNumber'] ?? null);
        $req->setPickUpStartTime($data['pickUpStartTime'] ?? null);
        $req->setPickUpEndTime($data['pickUpEndTime'] ?? null);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);
        return $resp;
    }


    public
    function updateBuyOrderSopWaybill(
        $data
    ) {
        $client = $this->getClient();
        $req = new BuyOrderSopWaybillUpdateRequest();
        $req->setOrderId($data['orderId']);
        $req->setLogisticsId($data['logisticsId']);
        $req->setTradeNo($data['tradeNo']);
        $req->setWaybill($data['waybill']);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);
        return $resp;

    }

    public
    function viewServiceAndRefund(
        $data
    ) {
        $client = $this->getClient();
        $req = new AscServiceAndRefundViewRequest();
        $req->setOrderId($data['orderId'] ?? null);
        $req->setApplyTimeBegin($data['applyTimeBegin'] ?? null);
            $req->setApplyTimeEnd($data['applyTimeEnd']) ?? null;
        $req->setApproveTimeBegin($data['approveTimeBegin'] ?? null);
        $req->setApproveTimeEnd($data['approveTimeEnd'] ?? null);
        $req->setPageNumber($data['pageNumber'] ?? null);
        $req->setPageSize($data['pageSize'] ?? null);
        $req->setExtJsonStr($data['extJsonStr'] ?? null);
        $req->setBuId($data['buId'] ?? null);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);
        return $resp;
    }

    public
    function sendGetRemarks(
        string $startTime,
        string $endTime,
        int $page,
        int $sortType = 1
    ) {
        $client = $this->getClient();
        $req = new PopOrderGetRemarkByModifyTimeRequest();
        $req->setStartTime($startTime);
        $req->setEndTime($endTime);
        $req->setPage($page);
        $req->setSortTime($sortType);
        $resp = $client->execute($req, $this->getAccessToken());
        $resp = json_decode(json_encode($resp), true);
        $this->handleResp($resp);
        Log::info("remarkList", ["resp" => $resp, "request" => json_encode($req->getApiParas())]);
        return $resp['jingdong_pop_order_getRemarkByModifyTime_responce']['getremarkbymodifytime_result'] ?? [];
    }

    public
    function formatRemark(
        $remark
    ) {
        return [
            "orderSn" => strval($remark['orderId']),
            "remark" => $remark['remark'] ?? null,
            "created" => date('Y-m-d H:i:s', $remark['created'] / 1000),
            "modified" => date('Y-m-d H:i:s', $remark['modified'] / 1000),
            "remarkFlag" => isset($remark['flag']) ? $this->formatOrderFlag($remark['flag']) : null
        ];
    }

    /**
     * @inheritDoc
     */
    public
    function sendByCustom(
        $requestMethod,
        $apiMethod,
        $apiParams,
        $order
    ) {
        $client = $this->getJdClient();
        $req = new \CustomRequest();
        $req->setApiMethod($apiMethod);
        foreach ($apiParams as $key => $apiParam) {
            $req->putOtherTextParam($key, $apiParam);
        }
        list($requestUrl, $params) = $client->getApiParamsAndUrl($req);
        $params = json_decode($params, true);
        $resp = $client->executeByCustom($requestUrl, $params);
        Log::info('sendByCustom', ['requestUrl' => $requestUrl, 'params' => $params, 'resp' => $resp]);
        return $resp;
    }

    /**
     * 获取代打店铺角色类型
     * <AUTHOR>
     */
    public
    function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
        return 0;
    }

    /**
     * 获取代打订单列表
     * @param  int  $startTime
     * @param  int  $endTime
     * @return mixed
     * @throws OrderException|ClientException|ApiException
     * <AUTHOR>
     */
    public
    function getFactoryTradesOrder(
        int $startTime,
        int $endTime
    ) {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    /**
     * 批量获取厂家订单信息
     * @param $orders
     * @return mixed
     * <AUTHOR>
     */
    public
    function batchGetFactoryOrderInfo(
        $orders
    ) {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * 批量电子面单回传并发货
     * @param $orders
     * @return OrderResponseBo[]
     * <AUTHOR>
     */
    public
    function batchReturnFactoryOrder(
        $orders
    ) {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public
    function batchWaybillRecoveryFactoryOrder(
        $waybills
    ) {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public
    function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public
    function fillApiParamByOrder(
        $apiMethod,
        $apiParams,
        $order,
        $orderShop = null
    ): array {
        switch ($apiMethod) {
            case 'jingdong.ldop.alpha.waybill.receive';
            case 'jingdong.logistics.ewaybill.waybill.create';
                $tde = $this->getTdeByShop($order['shop']);
                $orderShop = $this->getShop();

                $content = json_decode($apiParams['content'], true);
                $oaid = ArrayUtil::getArrayValue($order, 'order_cipher_info.oaid');
                $receiverNameCipherText = array_get($order, 'order_cipher_info.receiver_name_ciphertext');
                $receiverAddressCipherText = array_get($order, 'order_cipher_info.receiver_address_ciphertext');
                $receiverName = empty($oaid) ? $tde->decrypt($receiverNameCipherText) : $order['receiver_name'];
                $receiverAddress = empty($oaid) ? $tde->decrypt($receiverAddressCipherText) : $order['receiver_address'];
                //从加密字段里面获取固定电话的加密值，如果没有就用手机的加密值
                $receiverTelephoneCipherText = array_get($order, 'order_cipher_info.receiver_telephone_ciphertext');
                $receiverTelephone = ArrayUtil::getArrayValue($order['order_cipher_info'],
                    'receiver_telephone_ciphertext', $order['receiver_phone']);
                $content['platformOrderNo'] = $order['tid'];
                $content['vendorCode'] = $orderShop->service_id;//商家编码
                $content['vendorName'] = $orderShop->shop_name; //商家名称
                $toAddress = [
                    'provinceName' => $order['receiver_state'] ?? $order['receiver_province'],
                    'cityName' => $order['receiver_city'],
                    'countryName' => $order['receiver_district'],
                    //'countrysideName' => '不知道什么街道',
                    'address' => $receiverAddress,//需解密
                    'contact' => $receiverName,//需解密
                    'mobile' => $order['receiver_phone'],
                    'phone' => $receiverTelephone,
                ];

                if ($oaid) {
                    $toAddress['oaid'] = $oaid;
                }

                $content['toAddress'] = $toAddress;
                $apiParams['content'] = json_encode($content);
//                \Log::info('fillApiParamByOrder', ['content' => $apiParams['content']]);
                break;
            default:
                break;
        }
        return $apiParams;
    }

    /**
     *
     *  这里不考虑拆单的问题。
     * https://open.jd.com/home/<USER>/#/doc/common?listId=2266
     * @inerhitDoc
     */
    public
    function orderMultiPackagesDelivery(
        int $shopId,
        array $orderDeliveryRequests
    ): array {
        $client = $this->getJdClient();
        $requestData = [];
        /**
         * @var array{
         *      tid:string,
         *      shopId:int,
         *      packs:array{
         *          waybillCode:string,
         *          expressCode:string,
         *          goods:array{
         *              oid:string,
         *              shippedNum:int,
         *              num_iid:string,
         *              sku_id:string,
         *              sku_uuid:string
         *          }
         *      }
         *  } $orderDeliveryRequest
         */
        foreach ($orderDeliveryRequests as $orderDeliveryRequest) {
            $request = new PopOrderPartialOutshipRequest();
            $req = new \PopOrderPartialOutship\Request();
            $req->setBelongType(200);
            $tid = $orderDeliveryRequest['tid'];
            //从packs中把所有的expressCode都提炼出来

            $packs = $orderDeliveryRequest['packs'];
            $expressCodeArray = array_pluck($packs, 'expressCode');
            //把所有的


            $outshipItems = [];
            $outshipItem = new OutshipItem();
            $outshipItem->setDeliveryType(5); //发货方式5:自己联系物流出库 目前仅支持5
            $outshipItem->setOrderIds([$tid]);
            $deliveryNumberDtoList = [];
            foreach ($packs as $pack) {
                $deliveryNumberDto = new DeliveryNumberDtoItem();
                $deliveryNumberDto->setDeliveryNums([$pack['waybillCode']]);
                $expressCode = $pack['expressCode'];
                $deliveryNumberDto->setDeliveryId(array_get($this->expressCodeList, $expressCode));
                $deliveryNumberDto->setDeliveryName(ExpressCompanyUtil::findExpressCompanyName($expressCode));
                $partialShipmentGoodsList = [];
                foreach ($pack['goods'] as $good) {
                    $partialShipmentGoods = new PartialShipmentGoodsListItem();
                    $partialShipmentGoods->setNum($good['shippedNum']);
                    $partialShipmentGoods->setSkuId($good['sku_id']);
                    $partialShipmentGoods->setSkuUuid($good['sku_uuid']);
                    $partialShipmentGoodsList[] = $partialShipmentGoods;
                }
                $deliveryNumberDto->setPartialShipmentGoodsList($partialShipmentGoodsList);
                $deliveryNumberDtoList[] = $deliveryNumberDto;
            }

            $outshipItem->setDeliveryNumberDtoList($deliveryNumberDtoList);

            $outshipItems[] = $outshipItem;
            $req->setData($outshipItems);
            $request->setRequest($req->getInstance());
            [$url, $params] = $client->getApiParamsAndUrl($request);
            $requestData[$tid] = [
                'params' => $params,
                'url' => $url,
            ];


        }

        $responseData = $this->poolCurl($requestData, 'post');
        $responseData = json_decode(json_encode($responseData), true);
        $success = [];
        $errors = [];
        $orderDeliveryRequestsKeyByTid = array_pluck($orderDeliveryRequests, null, 'tid');
        foreach ($responseData as $tid => $response) {
            Log::info("发货回调", [$tid, $response]);
            $orderDeliveryRequest = $orderDeliveryRequestsKeyByTid[$tid];
            $waybillCodes = [];
            foreach ($orderDeliveryRequest['packs'] as $pack) {
                $packs = [];
                foreach ($pack['goods'] as $goods) {
                    $oid = $goods['oid'];
                    $packs[] = ["oid" => $oid, "shippedNum" => $goods['shippedNum']];
                }
                $waybillCodes[] = [
                    "waybillCode" => $pack['waybillCode'], "expressCode" => $pack['expressCode'], "packs" => $packs
                ];
            }

            try {
                $responseBody = ArrayUtil::getArrayValue($response,
                    'jingdong_pop_order_partial_outship_responce.response', []);
                $code = ArrayUtil::getArrayValue($responseBody, 'code', 0);
                //先检查整体的有没有成功，如果整体失败，则直接返回
                if ($code != "200") {
                    throw new \Exception(ArrayUtil::getArrayValue($responseBody, 'msg', ''));
                }
                //再处理部分成功和部分失败

                $responseData = ArrayUtil::getArrayValue($responseBody, 'data', []);
                Log::info("发货回调", [$responseData]);
                $successList = ArrayUtil::getArrayValue($responseData, 'successList', []);
                $failList = ArrayUtil::getArrayValue($responseData, 'failList', []);
                foreach ($successList as $tid) {
                    $success[] = ["tid" => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
                }
                foreach ($failList as $failItem) {
                    $tid = ArrayUtil::getArrayValue($failItem, 'orderId', '');
                    $errorMsg = ArrayUtil::getArrayValue($failItem, 'failReason', '');
                    $errors[] = [
                        "tid" => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, "msg" => $errorMsg
                    ];
                }

            } catch (\Exception $e) {
                $errorMsg = $e->getMessage();
                $errors[] = ["tid" => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, "msg" => $errorMsg];

            }

        }
        Log::info("发货回调结果", [$success, $errors]);
        return ["successes" => $success, "failures" => $errors];

    }


    /**
     * 审核状态:
     * 0、未审核
     * 1、审核通过((包括商家审核以及平台审核)
     * 2、审核不通过
     * 3、京东财务审核通过
     * 4、京东财务审核不通过
     * 5、人工审核通过（该值已废弃）
     * 6、京东拦截并退款
     * 7、青龙拦截成功
     * 8、青龙拦截失败
     * 9、强制关单并退款
     * 10、物流待跟进(线下拦截)
     * 11、用户撤销
     * 16、拒收后退款
     * 17、协商退货退款
     * 18、协商关闭
     * 19、纠纷介入（中间状态，表示进入协商流程后，消费者不同意协商会生成纠纷单进入纠纷）
     * 27、京东承诺拦截（京东拦截的升级版，跟status=6基本一致）\
     * 28、协商修改金额
     * @param  int  $refund_status
     * @return void
     */

    public function formatSubRefundStatus(int $refund_status): int
    {
        if (array_key_exists($refund_status, self::SUB_REFUND_STATUS_MAP)) {
            return self::SUB_REFUND_STATUS_MAP[$refund_status] ;
        } else {
            Log::warning("退款状态转换失败", [$refund_status]);
            return RefundSubStatusConst::NONE;
        }
    }

    /**
     * @param  array  $refund
     * @return OrderRefund
     */
    private function formatRefund(
        array $refund
    ): OrderRefund {
        $orderRefund = new OrderRefund();
        $orderRefund->refundId = strval(array_get($refund, "id"));
        $orderRefund->reason = array_get($refund, "reason");
        $status = array_get($refund, "status");
        $orderRefund->status = $this->formatRefundStatus($status);
        $orderRefund->refundSubStatus = $this->formatSubRefundStatus($status);

        $tid = strval(array_get($refund, "orderId"));
        $orderRefund->tid = $tid;
        $partRefundType = array_get($refund, "partRefundType");
        if ($partRefundType == 1) {
//          ==1 标识是部分退款
            $orderRefund->isPartRefund = true;
            foreach (array_get($refund, "skuVoList") as $sku) {
                $orderRefundItem = new OrderRefundItem();
                $orderRefundItem->tid = $tid;
                $orderRefundItem->skuId = strval(array_get($sku, "skuId"));
                $orderRefundItem->skuUuid = strval(array_get($sku, "skuUuid"));
                $orderRefundItem->skuValue = array_get($sku, "skuName");
                $orderRefundItem->skuCount = intval(array_get($sku, "count"));
                $orderRefund->addItem($orderRefundItem);
            }
        } else {
            $orderRefund->isPartRefund = false;
        }
        return $orderRefund;

    }

    /**
     * @param  array  $refundIds
     * @return OrderRefund[]
     */
    private function getRefundAppliesByRefundIds(array $refundIds): array
    {
        $client = $this->getClient();
        $request = new PopAfsSoaRefundapplyQueryPageListRequest();
        $refundIdChunks = array_chunk($refundIds, 25);
        $refunds = [];
        foreach ($refundIdChunks as $chunk) {
            try {
                $request->setIds(implode(",", $chunk));
                $response = $client->execute($request, $client->accessToken);
                $resp = $this->handleResp($response);
                $refundList = array_get($resp,
                    'jingdong_pop_afs_soa_refundapply_queryPageList_responce.queryResult.result', []);
                Log::info("获取退款申请", [$chunk, $refundList]);
                foreach ($refundList as $refund) {
                    $orderRefund = $this->formatRefund($refund);
                    $refunds[] = $orderRefund;
                }
            } catch (\Exception $exception) {
                Log::error("获取退款申请失败", [$exception->getTraceAsString()]);
            }

        }
        return $refunds;
    }

}
