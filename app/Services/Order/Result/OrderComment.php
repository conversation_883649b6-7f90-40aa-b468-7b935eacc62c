<?php

namespace App\Services\Order\Result;

class OrderComment
{
    /**
     * @var string 订单号
     */
    public $tid;
    /**
     * @var string|null 买家旗标
     */
    public $sellerFlag;
    /**
     * @var string|null 卖家备注
     */
    public $sellerMemo;

    /**
     * @var string|null 买家留言
     */
    public $buyerMessage;

    /**
     * @param string $tid
     * @param string|null $sellerFlag
     * @param string|null $sellerMemo
     * @param string|null $buyerMessage
     */
    public function __construct(string $tid, ?string $sellerFlag, ?string $sellerMemo, ?string $buyerMessage)
    {
        $this->tid = $tid;
        $this->sellerFlag = $sellerFlag;
        $this->sellerMemo = $sellerMemo;
        $this->buyerMessage = $buyerMessage;
    }


}
