<?php

namespace App\Services\Order\Request;

/**
 * 备货统计的参数
 */
class GoodsMergeParam
{

    /**
     * @var int 商品合并类型
     *  1 商品ID合并，2 简称合并（没有简称按商品标题） 3 标题合并 4编码合并（没有编码按商品ID）
     */
    public $goodsMergeType;

    /**
     * @var int SKU合并类型
     * 1 规格ID合并，2 规格简称合并（没有简称按规格标题）3 规格标题合并 4 编码合并（没有编码按规格ID）
     */
    public $skuMergeType;

    const GOODS_MERGE_TYPE = 1;
    const SKU_MERGE_TYPE = 2;
    /**
     * @var int 输出格式
     * 1. 按商品 2 按规格
     */
    public $outputFormat;

    /**
     * @param int $goodsMergeType
     * @param int $skuMergeType
     * @param int $outputFormat
     */
    public function __construct(int $goodsMergeType, int $skuMergeType, int $outputFormat)
    {
        $this->goodsMergeType = $goodsMergeType;
        $this->skuMergeType = $skuMergeType;
        $this->outputFormat = $outputFormat;
    }


}
