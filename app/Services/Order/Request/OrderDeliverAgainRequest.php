<?php

namespace App\Services\Order\Request;

/**
 * 重新发货请求对象
 */
class OrderDeliverAgainRequest
{
    /**
     * 平台订单ID
     * @var string  $tid
     */
    public $tid;
    /**
     * 快递公司编码
     * @var string  $wpCode
     */
    public $wpCode;
    /**
     * 运单号
     * @var string  $waybillCode
     */
    public $waybillCode;

    /**
     * 订单发货记录的ID
     * @var string  $deliveryId
     */
    public $deliveryId;

    public $oldWpCode;

    public $oldWaybillCode;

    /**
     * @var array 包裹列表 用于淘宝
     */
    public $waybills = [];

    /**
     * @var array 子订单号列表 逗号分隔 用于淘宝
     */
    public $subTids = '';
    /**
     * @var int 是否拆单
     */
    public $isSplit = 0;
    /**
     * @var int 是否合单
     */
    public $isMerge = 0;
    /**
     * 要变更的包裹商品列表
     * @var \App\Models\PackageOrder[]
     */
    public $oldPackageOrders;

}
