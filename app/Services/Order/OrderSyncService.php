<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/11/1
 * Time: 17:20
 */

namespace App\Services\Order;

use App\Models\Order;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Utils\Environment;

class OrderSyncService
{
    /**
     * 是否限制同步订单
     * <AUTHOR>
     * @param $shopId
     * @return bool
     */
    public static function isLimitSyncOrder4Shop($shopId)
    {
        if (!Environment::isDy()) {
            return false;
        }
        $shopId = (string)$shopId;
        $version = \Cache::remember('shopVersion:' . $shopId, 60, function () use ($shopId) {
            return UserExtra::getVersionByShopId($shopId);
        });
        if (in_array($version, [UserExtra::VERSION_PROFESSIONAL, UserExtra::VERSION_SENIOR])) {
            // 专业版，高级版不限制
            return false;
        }
        $whitelist = config('app.sync_order_limit_whitelist', '');
        // 白名单不限制
        if (!empty($whitelist)) {
            $whitelist = explode(',', $whitelist);
            if (!empty($whitelist) && in_array($shopId, $whitelist)) {
                return false;
            }
        }
        return true;
    }
}
