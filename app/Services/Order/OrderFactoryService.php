<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/6/20
 * Time: 16:11
 */

namespace App\Services\Order;

use App\Constants\ErrorConst;
use App\Events\Orders\OrderFactoryAssignCancelEvent;
use App\Events\Orders\OrderFactoryAssignEvent;
use App\Exceptions\ApiException;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use Carbon\Carbon;

class OrderFactoryService
{
    /**
     * @var \Illuminate\Http\Request
     */
    private $request = null;

    /**
     * 检查绑定关系是否存在
     * @param $shopId
     * @param $factoryShopId
     * @param bool $throw
     * @return bool
     * @throws ApiException
     * <AUTHOR>
     */
    public function checkBind($shopId, $factoryShopId, bool $throw = false)
    {
        $info = ShopBind::getBindByShopIdType($factoryShopId, $shopId, ShopBind::TYPE_AGENT_PRINT_SHOP_2_FACTORY);
        if (empty($info)) {
            if ($throw) {
                throw new ApiException(ErrorConst::SHOP_FACTORY_NOT_BIND);
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 分配订单
     * @param $factoryShop
     * @param $shop
     * @param array $orderList
     * @return int[]
     * @throws ApiException
     * <AUTHOR>
     */
    public function assignOrder($factoryShop, $shop, array $orderList)
    {
        $shopIds = Shop::getShopIdsByShopIdentifier($shop->id);
        $successCounter = 0;
        $failList = [];
        $tidArr = array_pluck($orderList, 'tid');
        $orderInfos = Order::query()->whereIn('shop_id', $shopIds)->whereIn('tid', $tidArr)->get()->keyBy('tid');
        $user_id = $this->request->auth->user_id;
        $user = User::firstById($user_id);
        foreach ($orderList as $index => $item) {
            $tid = $item['tid'];
            try {
                $orderInfo = $orderInfos[$tid] ?? null;
                if (empty($orderInfo)) {
                    throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
                }
                if (!empty($orderInfo->send_at)) {
                    throw new ApiException(ErrorConst::ORDER_SHIPPED);
                }
                if ($orderInfo->refund_status >= Order::REFUND_STATUS_YES) {
                    throw new ApiException(ErrorConst::ORDER_HAS_REFUND);
                }
                if ($orderInfo->factory_id > 0) {
                    throw new ApiException(ErrorConst::ORDER_ASSIGNED);
                }
                // 绑定关系不存在
                $this->checkBind($orderInfo->shop_id, $factoryShop->id, true);

                $orderInfo->factory_id = $factoryShop->id;
                $orderInfo->save();
                OrderExtra::whereOrderId($orderInfo['id'])->update([
                    'assign_at' => Carbon::now()->toDateTimeString()
                ]);
                event((new OrderFactoryAssignEvent($user, $shop, time(), $orderInfo, $orderInfo->factory_id))->setClientInfoByRequest($this->request));
                $successCounter++;
            } catch (\Exception $e) {
                $failList[] = [
                    'tid' => $tid,
                    'code' => $e->getCode(),
                    'msg' => $e->getMessage(),
                ];
            }
        }

        return [$successCounter, $failList];

    }

    /**
     * 取消分配订单
     * @param $shop
     * @param array $orderList
     * @return int[]
     * <AUTHOR>
     */
    public function assignOrderCancel($shop, array $orderList)
    {
        $shopIds = Shop::getShopIdsByShopIdentifier($shop->id);
        $successCounter = 0;
        $failList = [];
        $tidArr = array_pluck($orderList, 'tid');
        $orderInfos = Order::query()->whereIn('shop_id', $shopIds)->whereIn('tid', $tidArr)->get()->keyBy('tid');
        $user_id = $this->request->auth->user_id;
        $user = User::firstById($user_id);
        foreach ($orderList as $index => $item) {
            $tid = $item['tid'];
            try {
                $orderInfos[$tid] ?? null;
                if (empty($orderInfo)) {
                    throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
                }
                if ($orderInfo->factory_id == 0) {
                    throw new ApiException(ErrorConst::ORDER_NOT_ASSIGN);
                }
                if (!empty($orderInfo->send_at)) {
                    throw new ApiException(ErrorConst::ORDER_SHIPPED);
                }
                $factory_id = $orderInfo->factory_id;
                $orderInfo->factory_id = 0;
                $orderInfo->save();
                OrderExtra::whereOrderId($orderInfo['id'])->update([
                    'assign_cancel_at' => Carbon::now()->toDateTimeString(),
                    'assign_cancel_reason' => $item['reason'] ?? '',
                ]);

                event((new OrderFactoryAssignCancelEvent($user, $shop, time(), $orderInfo, $factory_id))->setClientInfoByRequest($this->request));
                $successCounter++;
            } catch (\Exception $e) {
                $failList[] = [
                    'tid' => $tid,
                    'code' => $e->getCode(),
                    'msg' => $e->getMessage(),
                ];
            }
        }
        return [$successCounter, $failList];

    }

    public function getShipmentsOrderList($shopId, array $input)
    {
        $offset = array_get($input, 'offset', 0);
        $limit = array_get($input, 'limit', 20);
        $time_start = array_get($input, 'time_start');
        $time_end = array_get($input, 'time_end');
        $keyword = array_get($input, 'keyword');
        $wp_code = array_get($input, 'wp_code');
        $shop_id_list = array_get($input, 'shop_id_list');

        $shopIds = Shop::getShopIdsByShopIdentifier($shopId);
        $query = \App\Models\Fix\Order::query()->whereIn('factory_id', $shopIds);
        $query->leftJoin('order_extras', 'order_extras.order_id', '=', 'orders.id');
        $query->where('order_extras.is_factory_shipped', 1);

        if (!empty($time_start)) {
            $query->where('pay_at', '>=', $time_start);
        }
        if (!empty($time_end)) {
            $query->where('pay_at', '<=', $time_end);
        }
        if (!empty($shop_id_list)) {
            $query->whereIn('shop_id', $shop_id_list);
        }
        if (!empty($keyword)) {
            $query->where(function ($query) use ($keyword) {
                $query->where('tid', $keyword);
                $query->orWhere('express_no', $keyword);
            });
        }
        if (!empty($wp_code)) {
            $wp_code_arr = getWpCodesByUnionWpCode($wp_code);
            $query->whereIn('express_code', $wp_code_arr);
        }

        $count = $query->count();
        $query->offset($offset)->limit($limit);
        $list = $query->with(['shop:id,shop_name', 'factoryShop:id,shop_name'])->get([
            'orders.tid',
            'orders.send_at',
            'orders.express_no',
            'orders.shop_id',
            'orders.factory_id',
        ]);
        return [$count, $list];
    }

    public function setRequest(\Illuminate\Http\Request $request)
    {
        $this->request = $request;
    }
}
