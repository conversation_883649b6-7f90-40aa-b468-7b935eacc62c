<?php

namespace App\Services\AfterSale;

use App\Constants\TaobaoConst;
use App\Models\Order;
use App\Models\Shop;
use App\Services\AfterSale\Request\AfterSaleRequest;
use App\Services\Client\TaoBaoClient;
use App\Services\Client\TbClient;
use App\Services\CommonResponse;
use App\Utils\ObjectUtil;
use LogisticsAddressSearchRequest;
use RefundGetRequest;
use RefundRefusereasonGetRequest;
use RefundRefuseRequest;
use RpRefundsAgreeRequest;
use RpReturngoodsAgreeRequest;
use RpReturngoodsRefuseRequest;
use TopClient\request\RefundsReceiveGetRequest;
use TopClient\TopClient;

/**
 * 淘宝售后相关的
 */
class TaobaoAfterSaleServiceImpl extends AbstractAfterSaleService
{
    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;
//'updateTimeStart' => 'required|string',
//'updateTimeEnd' => 'required|string',
//'pageSize' => 'required|int|between:1,50',
//'pageIndex' => 'required|int|between:1,100',
    const FIELDS_4_REFUND_APPLY_LIST = "refund_id, tid, title, buyer_nick, seller_nick, total_fee, status,
        created, refund_fee, oid, good_status, company_name, sid, payment, reason, desc, has_good_return, modified,
        order_status,refund_phase,sku";
    const FIELDS_4_REFUND_DETAIL = "refund_id, alipay_no, tid, oid, buyer_nick, seller_nick, total_fee, status,
        created, refund_fee, good_status, has_good_return, payment, reason, desc, num_iid, title, price,
        num, good_return_time, company_name, sid, address, shipping_type, refund_remind_timeout, refund_phase,
        refund_version, operation_contraint, attribute, outer_id, sku";

    /**
     * @return TopClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        return topClient();
    }

    /**
     * @return TbClient
     * @throws \App\Exceptions\ApiException
     */
    protected function getTbClient()
    {
        $appKey = config('socialite.taobao_top.client_id');
        $secretKey = config('socialite.taobao_top.client_secret');
        $tbClient = new TbClient($appKey, $secretKey, $this->getAccessToken());
        $tbClient->setAccessToken($this->getAccessToken());
        return $tbClient;
    }

    public function batchGetRefundApplyList($accessToken,$data)
    {
        $refundsReceiveGetRequest=new RefundsReceiveGetRequest();
        $refundsReceiveGetRequest->setPageSize($data['pageSize']);
        $refundsReceiveGetRequest->setUseHasNext("true");
        $refundsReceiveGetRequest->setPageNo($data['pageIndex']);
        $refundsReceiveGetRequest->setStartModified($data["updateTimeStart"]);
        $refundsReceiveGetRequest->setEndModified($data["updateTimeEnd"]);
        $refundsReceiveGetRequest->setFields(self::FIELDS_4_REFUND_APPLY_LIST);
        $topClient = topClient();
        $resp = $topClient->execute($refundsReceiveGetRequest, $accessToken);
        $resp = TaoBaoClient::handleResp($resp);

        return ["refundList"=>$this->formatToRefundApplyList($resp['refunds']['refund']),"hasNext"=>$resp['has_next']];
        // TODO: Implement batchGetRefundApplyList() method.
    }

    public   function formatToRefundApplyList($applyList):array{
        $list = [];
        foreach ($applyList as $index => $apply) {
            $list[] = $this->formatToRefundApply($apply);
        }
        return $list;

    }

    public function formatToRefundApply($refund)
    {
        $orderData =[
            "refundId"=>$refund['refund_id'],
            "orderSn"=>$refund['tid'],
//            "order"=>$refund['tid'],
            'totalFee'=>$refund['total_fee'],
            'created'=>$refund['created'],
            'modified'=>$refund['modified']??null,
            'orderStatus'=>isset($refund['order_status'])?TaobaoConst::formatOrderStatus($refund['order_status']):null,
            'refundStatus'=>TaobaoConst::formatRefundStatus($refund['status']),
            'goodStatus'=>$refund['good_status'],
            'hasGoodReturn'=>$refund['has_good_return'],
            'refundFee'=>$refund['refund_fee'],
            'reason'=>$refund['reason'],
            'desc'=>$refund['desc'],
            'goodsTitle'=>$refund['title'],
            'num'=>$refund['num']??null,
            'companyName'=>$refund['company_name']??null,
            'refundWaybillCode'=>$refund['sid']??null,
            'refundPhase'=>$refund['refund_phase'],
            'sku'=>$refund['sku']??null,
//            'attribute'=>$refund['attribute'],
            'outerId'=>$refund['outer_id']??null,
            'shippingType'=>$refund['shipping_type']??null,
            'address'=>$refund['address']??null,
            'num_iid'=>$refund['num_iid']??null,
            'goodReturnTime'=>$refund['good_return_time']??null,

//            'operationContraint'=>$refund['operation_contraint'],
        ];
       return $orderData;

    }

    public function getRefundApply($accessToken, $data)
    {
        $refundId=$data['refundId'];
        $refundGetRequest=new RefundGetRequest();
        $refundGetRequest->setFields(self::FIELDS_4_REFUND_DETAIL);
        $refundGetRequest->setRefundId($refundId);
        $topClient = topClient();
        $resp = $topClient->execute($refundGetRequest, $accessToken);
        $resp = TaoBaoClient::handleResp($resp);
        return $this->formatToRefundApply($resp['refund']);
    }

    /**
     * @param int $startTime
     * @param int $endTime
     * @return array
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\OrderException
     */
    public function getRefundOrdersList(int $startTime, int $endTime)
    {
        $this->hasNext = false;
        $refundsReceiveGetRequest=new RefundsReceiveGetRequest();
        $refundsReceiveGetRequest->setPageSize($this->pageSize);
        $refundsReceiveGetRequest->setUseHasNext("true");
        $refundsReceiveGetRequest->setPageNo($this->page);
        $refundsReceiveGetRequest->setStartModified(date('Y-m-d H:i:s', $startTime));
        $refundsReceiveGetRequest->setEndModified(date('Y-m-d H:i:s', $endTime));
        $refundsReceiveGetRequest->setFields(self::FIELDS_4_REFUND_APPLY_LIST);
        $topClient = topClient();
        $resp = $topClient->execute($refundsReceiveGetRequest, $this->getAccessToken());
        $resp = TaoBaoClient::handleResp($resp);

        if (isset($resp['has_next']) && $resp['has_next'] == true) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        $refundList = $resp['refunds']['refund'] ?? [];
        return $this->formatToRefundList($refundList);
    }

    private function formatToRefundList($refund)
    {
        $list = [];
        foreach ($refund as $index => $apply) {
            $list[] = $this->formatToRefund($apply);
        }
        return $list;
    }

    private function formatToRefund($apply)
    {
        return [
            'tid'               => $apply['tid'],
            'oid'               => $apply['oid'],
            'refund_id'         => $apply['refund_id'],
            'refund_price'      => $apply['refund_fee'], //退款金额
            'refund_status'     => TaobaoConst::$refundStatusTextMap[$apply['status']], //退款状态
            'refund_reason'     => $apply['reason'], //退款原因
            'refund_created_at' => $apply['created'],
            'refund_updated_at' => $apply['modified'],
        ];
    }

    /**
     * @param $data
     * @return array|mixed
     */
    public function agreeRefund($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchAgreeRefund($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchAgreeRefund($afterSaleRequestList)
    {
        $requestData = [];
        $tbClient = $this->getTbClient();
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $req = new RpRefundsAgreeRequest;
            //$req->setCode("839212");
            $refundInfo = $afterSaleRequest->refund_id .'|'. ($afterSaleRequest->refund_price*100). '|'. strtotime($afterSaleRequest->refund_updated_at);
            $req->setRefundInfos($refundInfo);
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                //$tbClient->handleResp($result);
                $result = json_decode(json_encode($result), true);
                if (isset($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['succ'])) {
                    $commonResponse->setSuccess($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['succ']);
                    $commonResponse->setMessage($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['message']);
                } else {
                    $commonResponse->setSuccess(false);
                }
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * 拒绝退款
     * @param $data
     * @return mixed
     */
    public function refuseRefund($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchRefuseRefund($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchRefuseRefund($afterSaleRequestList)
    {
        $requestData = [];
        $tbClient = $this->getTbClient();
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $req = new RefundRefuseRequest;
            $req->setRefundId($afterSaleRequest->refund_id);
            $req->setRefuseMessage($afterSaleRequest->remark);
            $req->setRefundPhase("aftersale");
            $req->setRefundVersion(strtotime($afterSaleRequest->refund_updated_at));
            $req->setRefuseReasonId($afterSaleRequest->reject_reason_code);
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                //$tbClient->handleResp($result);
                $result = json_decode(json_encode($result), true);
                if (isset($result['is_success'])) {
                    $commonResponse->setSuccess($result['is_success']);
                } else {
                    $commonResponse->setSuccess(false);
                }
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws \App\Exceptions\ApiException
     */
    public function agreeReturn($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchAgreeReturn($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchAgreeReturn($afterSaleRequestList)
    {
        $requestData = [];
        $tbClient = $this->getTbClient();
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $req = new RpReturngoodsAgreeRequest;
            $req->setRefundId($afterSaleRequest->refund_id);
            $req->setRefundPhase("aftersale");
            $req->setRefundVersion(strtotime($afterSaleRequest->refund_updated_at));
            $req->setRemark("没有问题，同意退货");
            $req->setAddress($afterSaleRequest->address);
            $req->setSellerAddressId($afterSaleRequest->address_id);
            $req->setMobile($afterSaleRequest->mobile_phone);
            $req->setName($afterSaleRequest->name);
            $req->setPostFeeBearRole(0);
            $req->setVirtualReturnGoods(false);
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                //$tbClient->handleResp($result);
                $result = json_decode(json_encode($result), true);
                if (isset($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['succ'])) {
                    $commonResponse->setSuccess($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['succ']);
                    $commonResponse->setMessage($result['rp_refunds_agree_response']['results']['refund_mapping_result'][0]['message']);
                } else {
                    $commonResponse->setSuccess(false);
                }
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws \App\Exceptions\ApiException
     */
    public function refuseReturn($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchRefuseReturn($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchRefuseReturn($afterSaleRequestList)
    {
        $requestData = [];
        $tbClient = $this->getTbClient();
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $req = new RpReturngoodsRefuseRequest;
            $req->setRefundId($afterSaleRequest->refund_id);
            $req->setRefundPhase("aftersale");
            $req->setRefundVersion(strtotime($afterSaleRequest->refund_updated_at));
            $req->setRefuseReasonId($afterSaleRequest->reject_reason_code);
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                //$tbClient->handleResp($result);
                $result = json_decode(json_encode($result), true);
                if (isset($result['result'])) {
                    $commonResponse->setSuccess($result['result']);
                } else {
                    $commonResponse->setSuccess(false);
                }
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $shopId
     * @param $refundId
     * @return array|mixed
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\OrderException
     */
    public function getRefundRejectReason($shopId, $refundId)
    {
        $list = [];
        $shop = Shop::query()->find($shopId);
        $this->setShop($shop);
        $topClient = topClient();
        $req = new RefundRefusereasonGetRequest;
        $req->setRefundPhase("aftersale");
        $req->setRefundId($refundId);
        $req->setFields("reason_id,reason_text,reason_tips");
        $resp = $topClient->execute($req, $this->getAccessToken());
        $resp = TaoBaoClient::handleResp($resp);

        foreach ($resp['data']['items'] as $item) {
            $list[] = [
                'reason' => $item['reason_text'],
                'reject_reason_code' => $item['reason_id']
            ];
        }

        return $list;
    }

    /**
     * @param $shopId
     * @return array|mixed
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\OrderException
     */
    public function getRefundRejectAddress($shopId)
    {
        $list = [];
        $shop = Shop::query()->find($shopId);
        $this->setShop($shop);
        $topClient = topClient();
        $req = new LogisticsAddressSearchRequest;
        $req->setRdef("cancel_def");
        $resp = $topClient->execute($req, $this->getAccessToken());
        $resp = TaoBaoClient::handleResp($resp);

        foreach ($resp['addresses']['address_result'] as $item) {
            $list[] = [
                'addressId' => $item['contact_id'],
                'addressInfo' => $item['province'] . $item['city'] . $item['country'] . $item['addr'],
                'phone' => $item['phone'],
                'name' => $item['contact_name'],
                'mobile_phone' => $item['mobile_phone']
            ];
        }

        return $list;
    }
}
