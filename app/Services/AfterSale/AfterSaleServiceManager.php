<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 20:32
 */

namespace App\Services\AfterSale;


use App\Constants\PlatformConst;
use InvalidArgumentException;

class AfterSaleServiceManager
{
    protected static $initMap = [
        PlatformConst::TAOBAO => TaobaoAfterSaleServiceImpl::class,
        PlatformConst::DY     => DyAfterSaleServiceImpl::class,
        PlatformConst::KS     => KsAfterSaleServiceImpl::class,
        PlatformConst::WX     => WxAfterSaleServiceImpl::class,
        PlatformConst::JD     => JdAfterSaleServiceImpl::class,
    ];

    /**
     * 创建一个订单server
     * @param $name
     * @return AbstractAfterSaleService
     */
    public static function create($name = ''): AbstractAfterSaleService
    {
        if (empty($name)){
            $name = config('app.platform');
        }
        if (isset(self::$initMap[$name])) {
            return new self::$initMap[$name]();
        }
        throw new InvalidArgumentException('不存在的 AfterSaleService:' . $name);
    }
}
