<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2021/12/23
 * Time: 11:44
 */

namespace App\Services\Waybill\KS;

use App\Constants\ErrorConst;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Models\Company;
use App\Models\Shop;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Client\KsClient;
use App\Services\Waybill\AbstractWaybillService;
use Illuminate\Support\Facades\Log;

class K<PERSON>pi extends AbstractWaybillService
{

    const INSURE_VAS_TYPE=["SVC-INSURE","INSURED","SVC-YT-INSURE","VALUE_INSURED"];

    const EXCLUDE_VAS_TYPE = ['product_type'];

    public function __construct(string $accessToken = '')
    {
        $this->accessToken  = $accessToken;
    }
    /**
     * @return KsClient
     */
    protected function getClient()
    {
        $appKey = config('socialite.ks.client_id');
        $secretKey = config('socialite.ks.client_secret');
        return new KsClient($appKey, $secretKey);
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws ClientException
     */
    public function waybillSubscriptionQuery(string $wpCode = '',string  $serviceId = ''):array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);

        $params = [];
        if ($wpCode) {
            $params = ['expressCompanyCode' => $wpCode];
        }

        $response = $client->execute('post', '/open/express/subscribe/query', $params);
//        Log::info('快手 获取电子面单：'.json_encode($response));

        if (!isset($response['result']) || $response['result'] != 1 || !isset($response['data']) || empty($response['data'])) {
            return [];
        }
        $serviceInfoColsMap = collect(config('ks_service_attributes'))->all();
        $waybillsInfo = [];
        foreach ($response['data'] as $value){
            $branchAccounts = $addresses = [];
            foreach ($value['senderAddress'] as $item){
                $addresses[] = [
                    'city'           => $item['cityName'] ?? '',
                    'detail'         => $item['detailAddress'] ?? '',
                    'province'       => $item['provinceName'] ?? '',
                    'district'       => $item['districtName'] ?? '',
                    'street'         => $item['streetName'] ?? '',// 兼容名字
                    'street_name'    => $item['streetName'] ?? ''
                ];
            }

            //直营快递必须有客户编码
            $serviceInfoCols = [];
            $expressCompanyCode = $value['expressCompanyCode'];
            if (array_key_exists($expressCompanyCode, $serviceInfoColsMap)) {
                $serviceInfoCols = $serviceInfoColsMap[$expressCompanyCode];
            }
            $expressCompanyType = $value['expressCompanyType'];
            $branchAccounts[] = [
                'branch_code'        => $value['netSiteCode'] ?? 0,
                'branch_name'        => $value['netSiteName'] ?? '',
                //直营返回的是-1，就是无限
                'quantity'           =>$expressCompanyType==1?-1: $value['availableQuantity'] ?? 0,
                'cancel_quantity'    => $value['cancelQuantity'] ?? 0,
                'recycled_quantity'  => $value['recycledQuantity'] ?? 0,
                'allocated_quantity' => $value['usedQuantity'] ?? 0,
                'shipp_addresses'    => $addresses,
                'service_info_cols'  => $serviceInfoCols,
                'settle_account'     => $value['settleAccount'] ?? '',
                'settlement_code'    => $value['settleAccount'] ?? '',
            ];


            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code'             => $expressCompanyCode,
                'wp_type'             => $expressCompanyType,

            ];
        }

        $data = [];
        foreach ($waybillsInfo as $item) {
            $data[$item['wp_code']]['branch_account_cols'][] = $item['branch_account_cols'][0];
            $data[$item['wp_code']]['wp_code'] = $item['wp_code'];
            $data[$item['wp_code']]['wp_type'] = $item['wp_type'];
        }

        return array_values($data);
    }

    /**
     * 单个订单获取面单
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return mixed
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $msg_type = '/open/express/ebill/get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);

        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $client->execute('post', $msg_type, $info);
                if (!isset($waybill['data']['data']) || empty($waybill['data']['data'])) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                }
                $waybillsData = [];
                foreach ($waybill['data']['data'] as $item) {
                    $waybillsData = [
                        'object_id'           => $waybill['data']['requestId'],
                        'waybill_code'        => $item['waybillCode'],
                        'print_data'          => $item['print_data'],
                        'sign'                => $item['signature'],
                        'parent_waybill_code' => $item['parentWaybillCode'],
                        'version'             => $item['version'],
                        'key'                 => $item['key'],
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                Log::error("获取电子面单失败" . $msg_type, [$e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 多个订单获取面单
     * @param $sender
     * @param $orders
     * @param $template
     * @param $packageNum
     * @return mixed
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        //非一单多包情况，并行异步请求
        $type   = '/open/express/ebill/get';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);
            $client = $this->getClient();
            $client->setAccessToken($this->accessToken);
            foreach ($applyInfo as $index => $info) {
                $tempIdStr = $idStr .'|'. $index;
                $data[$tempIdStr] = [
                    'params' => $client->getRequestData($type, $info),
                    'url'    => $client->gatewayUrl . $type,
                ];
            }
        }

        $response = $this->poolCurl($data, 'post', false, true);

        Log::info('ks 获取单号：'.json_encode($response));
        foreach ($response as $orderIdStr => $waybill) {
            $waybill = json_decode(json_encode($waybill), true);

            if (isset($waybill['result']) && $waybill['result'] == 1) {
                if (empty($waybill['data'][0]['data'])){
                    Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                    $result[$orderIdStr][] = $waybill['data'][0]['baseResponse']['message'] ?? '未知错误';
                    continue;
                }
                $waybillsData = [
                    'object_id'           => $waybill['data'][0]['requestId'],
                    'waybill_code'        => $waybill['data'][0]['data'][0]['waybillCode'],
                    'print_data'          => $waybill['data'][0]['data'][0]['printData'],
                    'parent_waybill_code' => $waybill['data'][0]['data'][0]['parentWaybillCode'],
                    'signature' => $waybill['data'][0]['data'][0]['signature'],
                    'key' => $waybill['data'][0]['data'][0]['key'],
                    'version' => $waybill['data'][0]['data'][0]['version']
                ];
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    /**
     * 作废面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return mixed
     * @throws ClientException
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode,string $platformWaybillId = '')
    {
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);
        $msg_type = '/open/express/ebill/cancel';

        $data = [
            'waybillCode'        => $waybillCode,
            'expressCompanyCode' => $cpCode,
        ];
        $result   = $client->execute('get', $msg_type, $data);
        Log::info($msg_type, [$result]);

        if (!isset($result['result']) || $result['result'] != 1) {
            throw new \Exception($result['sub_msg'] ?? "取消失败");
        }

        return true;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @return mixed
     * @throws ClientException
     */
    public function getCloudPrintStdTemplates(string $wpCode = ''):array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);
        $params = ['expressCompanyCode'=>$wpCode];
        $response = $client->execute('post','/open/express/standard/template/list/get', $params);

        $standardTemplates = [];
        if (!isset($response['result']) || $response['result'] != 1) {
            return $standardTemplates;
        }
        foreach ($response['data'] as $template) {
            if ($wpCode == 'JD') {
                if ($template['templateCode'] == 'EBSTO27') {
                    $standardTemplates[$template['waybillType']] = [
                        'standard_waybill_type'  => $template['waybillType'],
                        'standard_template_name' => $template['templateName'],
                        'standard_template_url'  => $template['templateUrl'],
                    ];
                }
            } if ($wpCode == 'SF') {
                if ($template['templateCode'] == 'EBSTO28') {
                    $standardTemplates[$template['waybillType']] = [
                        'standard_waybill_type'  => $template['waybillType'],
                        'standard_template_name' => $template['templateName'],
                        'standard_template_url'  => $template['templateUrl'],
                    ];
                }
            } else {
                $standardTemplates[$template['waybillType']] = [
                    'standard_waybill_type'  => $template['waybillType'],
                    'standard_template_name' => $template['templateName'],
                    'standard_template_url'  => $template['templateUrl'],
                ];
            }
        }

        return $standardTemplates;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return mixed
     * @throws ClientException
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);
        $params = ['expressCompanyCode'=>$wpCode];
        $response = $client->execute('post','/open/express/standard/template/list/get', $params);

        $standardTemplates = [];
        if (!isset($response['result']) || $response['result'] != 1) {
            return $standardTemplates;
        }
        foreach ($response['data'] as $template) {
//            if ($wpCode == 'JD') {
//                if ($template['templateCode'] == 'EBSTO27') {
//                    $standardTemplates[$template['waybillType']] = [
//                        'standard_waybill_type'  => $template['waybillType'],
//                        'standard_template_name' => $template['templateName'],
//                        'standard_template_url'  => $template['templateUrl'],
//                    ];
//                }
//            } if ($wpCode == 'SF') {
//                if ($template['templateCode'] == 'EBSTO28') {
//                    $standardTemplates[$template['waybillType']] = [
//                        'standard_waybill_type'  => $template['waybillType'],
//                        'standard_template_name' => $template['templateName'],
//                        'standard_template_url'  => $template['templateUrl'],
//                    ];
//                }
//            } else {
//                $standardTemplates[$template['waybillType']] = [
//                    'standard_waybill_type'  => $template['waybillType'],
//                    'standard_template_name' => $template['templateName'],
//                    'standard_template_url'  => $template['templateUrl'],
//                ];
//            }

            $standardTemplates[] = [
                'standard_waybill_type'  => $template['waybillType'],
                'standard_template_name' => $template['templateName'],
                'standard_template_url'  => $template['templateUrl'],
            ];
        }

        return $standardTemplates;
    }

    /**
     * auth link
     * @param $shopId
     * @return mixed
     */
    public function getLoginUrl($shopId)
    {
        // TODO: Implement getLoginUrl() method.
    }

    /**
     * access token
     * @param string $code
     * @return mixed
     */
    public function getAccessToken(string $code)
    {
        return $this->accessToken;
    }

    /**
     * 订阅物流轨迹
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        // TODO: Implement sendOrderLogisticsTraceMsg() method.
    }



    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        $template = [
            'wp_code' => $wpCode,
        ];
        $template['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);
        //非一单多包情况，并行异步请求
        $type   = '/open/express/ebill/get';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            //$applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);
            $client = $this->getClient();
            $client->setAccessToken($this->accessToken);
            foreach ($applyInfo as $index => $info) {
                $tempIdStr = $idStr;
                $data[$tempIdStr] = [
                    'params' => $client->getRequestData($type, $info),
                    'url'    => $client->gatewayUrl . $type,
                ];
            }
        }

        $response = $this->poolCurl($data, 'post', false, true);

        Log::info('ks 对外接口获取单号：'.json_encode($response));
        foreach ($response as $orderIdStr => $waybill) {
            $waybill = json_decode(json_encode($waybill), true);

            if (isset($waybill['result']) && $waybill['result'] == 1) {
                $insertData = [
                    'encryptedData'       => $waybill['data'][0]['data'][0]['printData'],
                    'signature'           => $waybill['data'][0]['data'][0]['signature'],
                    'key'                 => $waybill['data'][0]['data'][0]['key'],
                    'ver'                 => $waybill['data'][0]['data'][0]['version'],
                    'templateURL'         => $template['template_url'],
                    'addData'             => [
                        'senderInfo' => [
                            'address' => [
                                'provinceName' => $sender['province'],
                                'cityName'     => $sender['city'],
                                'districtName' => $sender['district'],
                                'detailAddress'   => $sender['address'],
                                'town'     => '',
                                'countryCode' => 'CHN'
                            ],
                            'contact' => [
                                'name'    => $sender['sender_name'],
                                'mobile'  => $sender['mobile'],
                                'phone'   => '',
                            ]
                        ]
                    ]
                ];
                $waybillsData = [
                    'object_id'           => $waybill['data'][0]['requestId'],
                    'waybill_code'        => $waybill['data'][0]['data'][0]['waybillCode'],
                    'print_data'          => json_encode($insertData),
                    'parent_waybill_code' => $waybill['data'][0]['data'][0]['parentWaybillCode'],
                ];
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result ?? [];
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);
        $msg_type = '/open/express/ebill/update';
        $applyInfo = $this->getWayBillUpdateRequestData($sender, $order->toArray(), $template, $waybillCode);

        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $client->execute('post', $msg_type, $info);
                Log::info('ks_update_response:'.json_encode($waybill));
                if (!isset($waybill['result']) || $waybill['result'] != 1) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                    return $result;
                }
                $waybillsData = [
                    'object_id'           => $waybill['requestId'],
                    'waybill_code'        => $waybill['data'][0]['waybillCode'],
                    'print_data'          => $waybill['data'][0]['printData'],
                    'parent_waybill_code' => $waybill['data'][0]['parentWaybillCode'],
                    'signature' => $waybill['data'][0]['signature'],
                    'key' => $waybill['data'][0]['key'],
                    'version' => $waybill['data'][0]['version']
                ];
                $result = $waybillsData;
            } catch (\Exception $e) {
                Log::error("更新电子面单失败" . $msg_type, [$e->getTraceAsString()]);
            }
        }

        return $result;
    }

    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        $shop = Shop::query()->where('id', $order['shop_id'])->first();
        $isGrayscale = false;
        if (ksEncryptSwitch($order['shop_id']) && isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $isGrayscale = true;
        }
        //取真实包裹数量
        if ($packageNum < 0 ) {
            $packageNum = $order['packageNum'];
        }

        while ($num <= $packageNum) {
            //发件人联系方式
            $senderContract = [
                'name'   => $sender['sender_name'],
                'mobile' => $sender['mobile']
            ];
            //发件人地址
            $senderAddress = [
                'provinceName'  => $sender['province'],
                'cityName'      => $sender['city'],
                'districtName'  => $sender['district'],
                'detailAddress' => $sender['address']
            ];
            //收件人联系方式
            $receiverContract = [
                'name'   => $isGrayscale ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                'mobile' => $isGrayscale ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                'telephone' => empty($order['receiver_tel'])? '' : $order['receiver_tel'],
            ];
            //收件人地址
            $receiverAddress = [
                'cityName'      => $order['receiver_city'],
                'detailAddress' => $isGrayscale ? $order['order_cipher_info']['receiver_address_ciphertext'] :  stripslashes($order['receiver_address']),
                'districtName'  => $order['receiver_district'],
                'streetName'    => $order['receiver_town'],
                'provinceName'  => $order['receiver_state'] ?? $order['receiver_province'],
            ];
            //商品信息列表
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['itemQuantity'] = $good['goods_num'];
                    $temp['itemTitle']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'itemQuantity' => is_int($order['num']) ? $order['num'] : 1,
                    'itemTitle'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }
            //只有直营店才有客户编码
            $settleAccount = "";
            if ($template['service_list']) {
                $serviceList = json_decode($template['service_list'], true);
                foreach ($serviceList as $serviceName => $serviceInfo) {
                    if ($serviceName == 'settleAccount') {
                        $settleAccount = $serviceInfo['value'];
                    }
                    if($serviceName=='noneValue'){
                        //如果serviceName=noneValue，意思就是没有value值
                    }
                }
            }

            $data = [];
            $data[] = [
                'merchantCode'     => $shop->identifier,
                'packageCode'      => isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] .'_' . rand(1111, 9999),
                'netSiteName'      => $template['company']['branch_name'],
                'netSiteCode'      => $template['company']['branch_code'],
                'itemList'         => $items,
                'senderAddress'    => $senderAddress,
                'senderContract'   => $senderContract,
                'receiverAddress'  => $receiverAddress,
                'receiverContract' => $receiverContract,
                'expressCompanyCode' => $template['wp_code'],
                'orderChannel'       => 'KUAI_SHOU',
                'sourceCode'         => '快手',
                'merchantName'       => $shop->shop_name,
                'tradeOrderCode'     => $order['tid'] ?? $order['id'],
                'requestId'          => isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] .'_' . rand(1111, 9999),
                'settleAccount'      => $settleAccount,
                'expressProductCode' => $template['time_delivery'] ?? '',//产品类型
                'totalPackageQuantity' => 1,
                'payMethod'            => 1,
                'reserveTime'          => strtotime('+1 days') * 1000,
                'reserveEndTime'       => strtotime('+2 days') * 1000,
                'extData'              => $template['wp_code'] == 'SF' ? json_encode(['isvClientCode' => 'test_code_123']) : (in_array($template['wp_code'], ['POSTB','POST_DSBK', 'EMS']) ? json_encode(['oneBillFeeType'=>1]) : '')
            ];
            $returnArr[] = ['getEbillOrderRequest' => $data];
            ++$num;
        }

        return $returnArr;
    }

    private function getWayBillRequestDataForOpenApi($sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        $shop = Shop::query()->where('id', $order['shop_id'])->first();
        while ($num <= $packageNum) {
            //发件人联系方式
            $senderContract = [
                'name'   => $sender['sender_name'],
                'mobile' => $sender['mobile']
            ];
            $isGrayscale = false;
            if (isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
                $isGrayscale = true;
            }
            //发件人地址
            $senderAddress = [
                'provinceName'  => $sender['province'],
                'cityName'      => $sender['city'],
                'districtName'  => $sender['district'],
                'detailAddress' => $sender['address']
            ];
            //收件人联系方式
            $receiverContract = [
                'name'   => $isGrayscale ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                'mobile' => $isGrayscale ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone']
            ];
            //收件人地址
            $receiverAddress = [
                'cityName'      => $order['receiver_city'],
                'detailAddress' => $isGrayscale ? $order['order_cipher_info']['receiver_address_ciphertext'] :  stripslashes($order['receiver_address']),
                'districtName'  => $order['receiver_district'],
                'streetName'    => $order['receiver_town'],
                'provinceName'  => $order['receiver_state'] ?? $order['receiver_province'],
            ];
            //商品信息列表
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['itemQuantity'] = $good['goods_num'];
                    $temp['itemTitle']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'itemQuantity' => !empty($order['num']) ? (int)$order['num'] : 1,
                    'itemTitle'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            //只有直营店才有客户编码
            $settleAccount = $sender['settle_account'] ?? '';
            $data = [];
            $orderId = $order['tid'] ?? ($order['order_no'] ?? $order['id']);
            $orderIdSuffix = rand(1000, 9999);
            !empty($order['request_id']) && $orderIdSuffix = $order['request_id'];
            $data[] = [
                'merchantCode'     => $shop->identifier,
                'packageCode'      => $orderIdSuffix,
                'netSiteName'      => $sender['branch_name'],
                'netSiteCode'      => $sender['branch_code'],
                'itemList'         => $items,
                'senderAddress'    => $senderAddress,
                'senderContract'   => $senderContract,
                'receiverAddress'  => $receiverAddress,
                'receiverContract' => $receiverContract,
                'expressCompanyCode' => $sender['wp_code'],
                'orderChannel'       => !empty($order['tid']) ? 'KUAI_SHOU' : 'OTHERS',
                'sourceCode'         => '快手',
                'merchantName'       => $shop->shop_name,
                'tradeOrderCode'     => $orderId,
                'requestId'          => isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] .'_' . rand(1111, 9999),
                'settleAccount'      => $settleAccount,
//                'expressProductCode' => $template['time_delivery'] ?? '',//产品类型
                'totalPackageQuantity' => 1,
                'payMethod'            => 1,
                'reserveTime'          => strtotime('+1 days') * 1000,
                'reserveEndTime'       => strtotime('+2 days') * 1000,
                'extData'              => $sender['wp_code'] == 'SF' ? json_encode(['isvClientCode' => 'test_code_123']) : (in_array($sender['wp_code'], ['POSTB', 'EMS',['POST_DSBK']]) ? json_encode(['oneBillFeeType'=>1]) : '')
            ];
            $returnArr[] = ['getEbillOrderRequest' => $data];
            ++$num;
        }

        return $returnArr;
    }

    private function getWayBillUpdateRequestData($sender, $order, $template, $waybillCode)
    {
        $shop = Shop::query()->where('id', $order['shop_id'])->first();
        //发件人联系方式
        $senderContract = [
            'name'   => $sender['sender_name'],
            'mobile' => $sender['mobile']
        ];
        //发件人地址
        $senderAddress = [
            'provinceName'  => $sender['province'],
            'cityName'      => $sender['city'],
            'districtName'  => $sender['district'],
            'detailAddress' => $sender['address']
        ];
        $isGrayscale = false;
        if (ksEncryptSwitch($order['shop_id']) && isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $isGrayscale = true;
        }
        //收件人联系方式
        $receiverContract = [
            'name'   => $isGrayscale ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
            'mobile' => $isGrayscale ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone']
        ];
        //收件人地址
        $receiverAddress = [
            'cityName'      => $order['receiver_city'],
            'detailAddress' => $isGrayscale ? $order['order_cipher_info']['receiver_address_ciphertext'] :  stripslashes($order['receiver_address']),
            'districtName'  => $order['receiver_district'],
            'streetName'    => $order['receiver_town'],
            'provinceName'  => $order['receiver_state'] ?? $order['receiver_province'],
        ];
        //商品信息列表
        $items = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                $items[]       = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $items[] = [
                'count' => is_int($order['num']) ? $order['num'] : 1,
                'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
            ];
        }

        $data = [
            'merchantCode'     => $shop->identifier,
            'packageCode'      => isset($order['request_id']) ? $order['request_id'][0] : $order['id'] .'_' . rand(1111, 9999),
            'netSiteName'      => $template['company']['branch_name'],
            'netSiteCode'      => $template['company']['branch_code'],
            'itemList'         => $items,
            'senderAddress'    => $senderAddress,
            'senderContract'   => $senderContract,
            'receiverAddress'  => $receiverAddress,
            'receiverContract' => $receiverContract,
            'expressCompanyCode' => $template['wp_code'],
            'orderChannel'       => 'KUAI_SHOU',
            'merchantName'       => $shop->shop_name,
            'tradeOrderCode'     => $order['tid'],
            'requestId'          => isset($order['request_id']) ? $order['request_id'][0] : $order['id'] .'_' . rand(1111, 9999),
            'waybillCode'        => $waybillCode
        ];
        $returnArr[] = $data;

        return $returnArr;
    }

    /**
     * 请求厂家电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBos
     * @param $template
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }

    /**
     * 微信视频号的拆单取号
     * @inerhitDoc
     * @throws ErrorCodeException
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        /**
         * @var array{orderId:string,errorCode:string,errorMsg:string} $errorInfos
         */
        $errorInfos = [];
        $requestParamsList = [];
        $client = $this->getClient();
        $client->setAccessToken($this->accessToken);
        $requestList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);
        $type = "/open/express/ebill/get";
//        $requestChunks = array_chunk($requestList, 1); // 支持批量取号（一次最多取10个）

        foreach ($requestList as $index => $item) {
            $requestParamsList[$index]=[
                'params' => $client->getRequestData($type, ['getEbillOrderRequest' => [$item]]),
                'url' => $client->gatewayUrl . $type,
            ];
        }

//        Log::info('preCreateData：', $requestParamsList);
        //电子面单取号
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responseBodys = $this->poolCurl($requestParamsList, 'post', false, true);

//        Log::info('ks 获取单号：',[$responseBodys]);
        $printDataPackBoList = [];
        foreach ($responseBodys as $index => $waybill) {
            $printPackBo = $printPackBoList[$index];

            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            try {
                $client::handleErrorCode($waybill);
                $waybill = json_decode(json_encode($waybill), true);

                if (isset($waybill['result']) && $waybill['result'] == 1) {
                    if (empty($waybill['data'][0]['data'])){
                        Log::error($type . '取号错误', [$index => $waybill]);
                        $msg = $waybill['data'][0]['baseResponse']['message'] ?? '未知错误';
                        $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR, $msg);
                    }else{
                        $waybillsPrintDataBo = new WaybillsPrintDataBo();
                        $key = $waybill['data'][0]['data'][0]['key'];
                        $waybillsPrintDataBo->package_id = $printPackBo->package_id;
                        $waybillsPrintDataBo->waybill_code = $waybill['data'][0]['data'][0]['waybillCode'];
                        $waybillsPrintDataBo->encrypted_data = $waybill['data'][0]['data'][0]['printData'];
                        $waybillsPrintDataBo->parent_waybill_code = $waybill['data'][0]['data'][0]['parentWaybillCode'];
                        $waybillsPrintDataBo->sign = $waybill['data'][0]['data'][0]['signature'];
                        $waybillsPrintDataBo->param_str = "key=$key";
                        $waybillsPrintDataBo->version = $waybill['data'][0]['data'][0]['version'];
                        $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
                        $printDataPackBo->waybill_code = $waybillsPrintDataBo->waybill_code;
                        $printDataPackBo->wp_code = $template['wp_code'];
                    }
//                $result[$index][] = $waybillsData;
                } else {
                    Log::error($type . '取号错误', [$index => $waybill]);
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR, json_encode($waybill));
//                $result[$orderIdStr][] = $waybill;
                }
            }catch (\Exception $e){
                Log::error($type . '取号错误:'.$e->getMessage(), [$index , $waybill,$e->getTrace()]);
                $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR, $e->getMessage());
            }
            $printDataPackBoList[] = $printDataPackBo;
        }

        return $printDataPackBoList;
    }

    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum)
    {
        $isPtOrder = true;
        $total_pack_count = 1;
//      // 子母件
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
            $total_pack_count = $packageNum;
        }
        $shop = $this->shop;

        $list = [];
        foreach ($printPackBoList as $printDataPackBo) {
            $isPtOrder = $printDataPackBo->isPlatformOrder();
            $orderObj = $printDataPackBo->master_order_info;
            $order = $orderObj->toArray();
            //发件人联系方式
            $senderContract = [
                'name'   => $branchAddressBo->sender_name,
                'mobile' => $branchAddressBo->mobile
            ];
            //发件人地址
            $senderAddress = [
                'provinceName'  => $branchAddressBo->province,
                'cityName'      => $branchAddressBo->city,
                'districtName'  => $branchAddressBo->district,
                'detailAddress' => $branchAddressBo->address
            ];
            //收件人联系方式
            $receiverContract = [
                'name'   => $isPtOrder ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                'mobile' => $isPtOrder ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                'telephone' => empty($order['receiver_tel'])? '' : $order['receiver_tel'],
            ];
            //收件人地址
            $receiverAddress = [
                'cityName'      => $order['receiver_city'],
                'detailAddress' => $isPtOrder ? $order['order_cipher_info']['receiver_address_ciphertext'] :  stripslashes($order['receiver_address']),
                'districtName'  => $order['receiver_district'],
                'streetName'    => $order['receiver_town'],
                'provinceName'  => $order['receiver_state'] ?? $order['receiver_province'],
            ];
            //商品信息列表
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['itemQuantity'] = $good['goods_num'];
                    $temp['itemTitle']  = $good['goods_title'] ?: '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'itemQuantity' => is_int($order['num']) ? $order['num'] : 1,
                    'itemTitle'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }
            //只有直营店才有客户编码
            $settleAccount = "";
            if ($template['service_list']) {
                $serviceList = json_decode($template['service_list'], true);
                foreach ($serviceList as $serviceName => $serviceInfo) {
                    if ($serviceName == 'settleAccount') {
                        $settleAccount = $serviceInfo['value'];
                    }
                }
            }
            if (empty($settleAccount)){
                $settleAccount =  $template['company']['settlement_code'] ?? '';
            }

            $data = [
                'merchantCode'     => $shop->identifier,
                'packageCode'      => $printDataPackBo->request_id.'',
                'netSiteName'      => $template['company']['branch_name'],
                'netSiteCode'      => $template['company']['branch_code'],
                'itemList'         => $items,
                'senderAddress'    => $senderAddress,
                'senderContract'   => $senderContract,
                'receiverAddress'  => $receiverAddress,
                'receiverContract' => $receiverContract,
                'expressCompanyCode' => $template['wp_code'],
                'orderChannel'       => 'KUAI_SHOU',
                'sourceCode'         => '快手',
                'merchantName'       => $shop->shop_name,
                'tradeOrderCode'     => $order['tid'] ?? $order['id'],
                'requestId'          => $printDataPackBo->request_id.'',
                'settleAccount'      => $settleAccount,
                'expressProductCode' => $template['time_delivery'] ?? '',//产品类型
                'totalPackageQuantity' => $total_pack_count,
                'payMethod'            => 1,
                'reserveTime'          => strtotime('+1 days') * 1000,
                'reserveEndTime'       => strtotime('+2 days') * 1000,
                'extData'              => $template['wp_code'] == 'SF' ? json_encode(['isvClientCode' => 'test_code_123']) : (in_array($template['wp_code'], ['POSTB','POST_DSBK', 'EMS']) ? json_encode(['oneBillFeeType'=>1]) : '')
            ];
            if (!empty($template['service_list'])) {
                $expressServices = $this->buildOrderVasList($template['service_list'], $printDataPackBo);
                $data['expressServices'] = $expressServices;
            }
            $list[] = $data;
        }
        return $list;
    }

    private function buildOrderVasList($service_list, $printDataPackBo): array
    {
        $newServiceList = [];
        if(empty($service_list)){
            return $newServiceList;
        }
        $serviceList = json_decode($service_list, true);
        Log::info("增值服务",["serviceList"=>$serviceList]);
        foreach ($serviceList as $key => $item) {
            if(in_array($key, self::EXCLUDE_VAS_TYPE)){
                continue;
            }

            //保价有两种
            if (in_array($key, self::INSURE_VAS_TYPE)) {
                $insureAmount = $printDataPackBo->getInsureAmount();
                Log::info("处理保价",["insureAmount"=>$insureAmount,"item"=>$item]);
                //-1 是订单金额保价
                if ($item['value'] != -1) {
                    Log::info("保价金额不是-1,按设定的金额处理",["item"=>$item]);
                }else{
                    if(bccomp($insureAmount,"0")>0) {
                        Log::info("保价金额是-1,订单金额>0 按订单金额处理",["insureAmount"=>$insureAmount]);
                        $item['value'] = round_bcmul($insureAmount, "100",0); //  (string)($insureAmount * 100);
                    }else{
                        Log::info("保价金额是-1,订单金额=0 不处理这个保价",["insureAmount"=>$insureAmount]);
                        continue;
                    }
                }
            }
            $newService=[];
            if(array_key_exists('noneValue',  $item)){
                $newService['code']=$key;
            }else {
                $itemValue = json_encode($item);
                $newService= [
                    'code' => $key,
                    'value' => $itemValue
                ];
            }
            $newServiceList[] = $newService;
        }
        return $newServiceList;
    }
}
