<?php

namespace App\Services\Waybill\Wx;

use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\Company;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Models\WaybillAuth;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Client\WxClient;
use App\Services\Waybill\AbstractWaybillService;
use App\Services\Waybill\PlatformTemplate;
use App\Utils\ArrayUtil;
use App\Utils\OrderUtil;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

/**
 * 微信视频号的电子面单
 */
class WxspApi extends AbstractWaybillService
{

    /**
     * 跳过不处理的增值服务的Code
     */
    const EXCLUDE_VAS_TYPE = ['product_type', 'temperature_range'];

    /**
     * 保价服务对于的Code
     */
    const INSURE_VAS_TYPE = ["INSURE", "IN160", "IN159", "IN160", "IN159", "IN67", "VA002", "insuranceValue", "INSURE SERVICE", "ed-a-0002"];

    public function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
    }

    public const OrderTraceMap = [
        "0" => OrderTraceList::STATUS_SHIPPED,//待揽件
        "1" => OrderTraceList::STATUS_GOT, //揽件
        "2" => OrderTraceList::STATUS_DEPARTURE, //运输中
        "4" => OrderTraceList::STATUS_SEND, //派件
        "5" => OrderTraceList::STATUS_SIGN, //签收 已退回签收 6
        "6" => OrderTraceList::STATUS_RETURN,//已退回
        "7" => OrderTraceList::STATUS_OTHER,//转寄
        "8" => OrderTraceList::STATUS_OTHER,//异常
        "9" => OrderTraceList::STATUS_OUT_CABINET,//已出柜
        "99" => OrderTraceList::STATUS_OTHER,//其他未知
    ];


    /**
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {

        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);
        $params = [
            'need_balance' => true,
            'limit' => 100
        ];
        if (!empty($wpCode)) {
            $params['delivery_id'] = $wpCode;
        }
        $body = $wxClient->execute('post', '/channels/ec/logistics/ewaybill/biz/account/get', $params);

        $serviceInfoColsMap = collect(config('wxsp_service_attributes'))->all();
        $waybillsInfo = [];
        Log::info('微信视频号电子面单查询结果', [$body]);
        Log::info("视频号增值服务", $serviceInfoColsMap);
        foreach ($body['account_list'] as $value) {
            $wpCompanyCode = Arr::get($value, 'site_info.delivery_id', Arr::get($value, 'delivery_id', ''));
            if ($value['status'] != 3) {
                continue;
            }

            $branchAccounts = [];
            $branchAccounts[] = [
                'branch_code' => Arr::get($value, 'site_info.site_code', ''),
                'branch_name' => Arr::get($value, 'site_info.site_name', ''),
                //如果是直营就-1，如果是加盟就是available
                'quantity' => $value['company_type'] == 2 ? -1 : $value['available'],
                'cancel_quantity' => $value['cancel'] ?? -1,
                'recycled_quantity' => $value['recycled'] ?? -1,
                'allocated_quantity' => $value['allocated'] ?? -1,
                'platform_account_id' => $value['acct_id'] ?? null,  //平台电子面单账户ID
                'platform_shop_id' => $value['shop_id'] ?? null, //平台电子面单店铺ID

                // site_info 可能没有，取 sender_address
                'shipp_addresses' => [[
                    "city" => Arr::get($value, 'site_info.address.city_name', Arr::get($value, 'sender_address.city', '')),
                    "detail" => Arr::get($value, 'site_info.address.detail_address', Arr::get($value, 'sender_address.address', '')),
                    "province" => Arr::get($value, 'site_info.address.province_name', Arr::get($value, 'sender_address.province', '')),
                    "district" => Arr::get($value, 'site_info.address.district_name', Arr::get($value, 'sender_address.county', '')),
                    "street_name" => Arr::get($value, 'site_info.address.street_name', ''),
                ]],
                'service_info_cols' => get_array_value($serviceInfoColsMap, $wpCompanyCode, [])

            ];


            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $wpCompanyCode,
                'wp_type' => 1
            ];
        }

        $data = [];
        foreach ($waybillsInfo as $item) {
            $data[$item['wp_code']]['branch_account_cols'][] = $item['branch_account_cols'][0];
            $data[$item['wp_code']]['wp_code'] = $item['wp_code'];
            $data[$item['wp_code']]['wp_type'] = $item['wp_type'];
        }
        //        \Log::info("微信视频号电子面单查询结果", $result);
        return array_values($data);

    }

    /**
     * 生成取号的数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @param $idStr
     * @return array
     */

    private function getWayBillRequestData($sender, $order, $template, $packageNum, $idStr): array
    {
        $returnArr = [];
        $num = 1;
        //去除A 并判断是否是抖音平台订单
        $isShare = false;
        $orderId = $order['tid'] ?? $order['id'];
        $orderCode = $order['order_code'];
        $waybillShopAppId = $this->shop->identifier;

        //是否是平台订单
        $isPlatformOrder = isset($order['tid']);
        //是否共享面单
        $company = $template['company'];
        if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
            $isShare = true;
        }
        //是否共用面单
        $orderShopId = $order['shop_id'];
        if ($template['shop_id'] != $orderShopId) {
            $orderShop = Shop::query()->where('id', $orderShopId)->first();
            $waybillShopAppId = $orderShop->identifier;
            $isShare = true;
            if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
                $waybillShop = Shop::query()->where('identifier', $company['owner_id'])->first();
                $this->accessToken = $waybillShop->access_token;
            } else {
                $waybillShop = Shop::query()->where('id', $template['shop_id'])->first();
                $this->accessToken = $waybillShop->access_token;
            }
        }

        //取真实包裹数量
        if ($packageNum < 0) {
            $packageNum = $order['packageNum'];
        }

        while ($num <= $packageNum) {
            $tempIdStr = $idStr . '|' . ($num - 1);
            //发件人信息
            $senderInfo['city'] = $sender['city'];
            $senderInfo['county'] = $sender['district'];
            $senderInfo['address'] = $sender['address'];
            $senderInfo['province'] = $sender['province'];
            $senderInfo['street'] = $sender['street'] ?? '';
            $senderInfo['mobile'] = $sender['mobile'];
            $senderInfo['name'] = $sender['sender_name'];
            //面单信息
//            $tradeOrderInfoDto['ec_order_id'] = intval($orderId);
            $tradeOrderInfoDto['ewaybill_order_code'] = $orderCode;
            $tradeOrderInfoDto['ewaybill_order_appid'] = $waybillShopAppId;

            //面单收件人信息
            if (!empty($order['order_cipher_info']['receiver_phone_ciphertext'])) {
                $mobile = OrderUtil::removeVirtualPhoneDashSuffix($order['order_cipher_info']['receiver_phone_ciphertext']);
            } else {
                $mobile = $order['order_cipher_info']['receiver_phone_mask'];
            }
            if (!empty($order['order_cipher_info']['receiver_name_ciphertext'])) {
                $name = OrderUtil::removeVirtualPhoneSuffix($order['order_cipher_info']['receiver_name_ciphertext']);
            } else {
                $name = $order['order_cipher_info']['receiver_name_mask'];
            }
            if (!empty($order['order_cipher_info']['receiver_address_ciphertext'])) {
                $address = OrderUtil::removeVirtualPhoneSuffix($order['order_cipher_info']['receiver_address_ciphertext']);
            } else {
                $address = $order['order_cipher_info']['receiver_address_mask'];
            }

            $receiverInfo = [
                'city' => $order['receiver_city'],
                'county' => $order['receiver_district'],
                'address' => $address,
                'province' => $order['receiver_state'] ?? $order['receiver_province'],
                'street' => $order['receiver_town'] ?? '',
                'mobile' => $mobile,
                'name' => $name,

            ];
            $items = [];
            if (!empty($order['order_item'])) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['good_count'] = $good['goods_num'];
                    $temp['good_name'] = $good['goods_title'] ?? '';
                    $temp['product_id'] = intval($good['num_iid']);
                    $temp['sku_id'] = intval($good['sku_id']);

                    $items[] = $temp;
                }
            }


            $tradeOrderInfoDto['goods_list'] = $items;
            $tradeOrderInfoDtos = [];
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['delivery_id'] = $company['wp_code'];
            $data['ewaybill_acct_id'] = $company['platform_account_id'] ?? '';
            if (!empty($company['branch_code'])) {
                $data['site_code'] = $company['branch_code'];
            }
            if ($isPlatformOrder) {
                $data['ec_order_list'] = $tradeOrderInfoDtos;
            }
            $data['sender'] = $senderInfo;
            $data['shop_id'] = $company['platform_shop_id'] ?? '';
            $data['receiver'] = $receiverInfo;
            $data['pack_id'] = isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] . '_' . rand(1111, 9999);; //这个是从订单中获取的，实际上就是package的Id

            $returnArr[$tempIdStr] = $data;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * @throws ClientException|GuzzleException
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {

        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);

        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $responseBody = $wxClient->execute("post", "/channels/ec/logistics/ewaybill/biz/order/precreate", $info);
                $ewaybillorderId = $responseBody['ewaybill_order_id'];
                $info['ewaybill_order_id'] = $ewaybillorderId;
                //如果有设置time_delivery，就通过接口的order_type字段来设置
                if (!empty($template->time_delivery) && intval($template->time_delivery) > 0) {
                    $info['order_type'] = intval($template->time_delivery);
                }
                \Log::info("微信视频号参数", [$info]);
                $waybill = $wxClient->execute("/channels/ec/logistics/ewaybill/biz/order/create", $info);
                \Log::info('response:' . json_encode($waybill));
                $waybillCode = $waybill['waybill_id'];


                $waybillPrint = [
                    "ewaybill_order_id" => $ewaybillorderId,
                    "template_id" => $template->template_id,
                ];
                $res = $wxClient->execute('/channels/ec/logistics/ewaybill/biz/print/get', $waybillPrint);
//                \Log::info('抖音获取加密打印数据  result：',[$res]);

                $result[] = [
                    'waybill_code' => $waybillCode,
                    'print_data' => $res['print_info'],
                    'parent_waybill_code' => ''
                ];

            } catch (\Exception $e) {
                $result[] = $e->getMessage();
            }
        }

    }

    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        $result = [];
        $applyInfos = [];
        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfos = array_merge($applyInfos, $this->getWayBillRequestData($sender, $order->toArray(), $template,
                $packageNum, $idStr));
        }
        \Log::info('微信视频号获取加密打印数据  applyInfos：', $applyInfos);
        //电子面单预取号
        $url = "/channels/ec/logistics/ewaybill/biz/order/precreate";
        $requestArr = [];
        /**
         * @var string $tempIdStr 是订单号加包裹号的索引
         */
        foreach ($applyInfos as $tempIdStr => $info) {
            $requestData = $wxClient->buildRequestData($url, $info);

            $requestArr[$tempIdStr] = [
                'params' => $requestData['apiParams'],
                'url' => $requestData['url']
            ];
        }
        $responseBody = $this->poolCurl($requestArr, "json", false, true);
        //预取号成功，把ewaybill_order_id放入参数中，进行正式取号
        $url = "/channels/ec/logistics/ewaybill/biz/order/create";
        $waybillOrderIds = [];
        foreach ($responseBody as $tempIdStr => $response) {

            if (isset($response->ewaybill_order_id)) {
                $ewaybillOrderId = $response->ewaybill_order_id;
                $requestArr[$tempIdStr]['params']['ewaybill_order_id'] = $ewaybillOrderId;
                $requestArr[$tempIdStr]['params']['template_id'] = "single";
                if (!empty($template['time_delivery']) && intval($template['time_delivery']) > 0) {
                    $requestArr[$tempIdStr]['params']['order_type'] = intval($template['time_delivery']);
                }
                $requestArr[$tempIdStr]['url'] = $wxClient->buildRequestUrl($url);
                $waybillOrderIds[] = $ewaybillOrderId;
            } else {
                //如果预取号失败，删除该订单
                unset($requestArr[$tempIdStr]);
            }
        }


        //正式取号，获取电子面单号
        \Log::info('正式取号参数', $requestArr);
        $responseBody = $this->poolCurl($requestArr, "json", false, true);


        foreach ($responseBody as $tempIdStr => $response) {
            \Log::info("正式取号的结果", [$response]);
            if (!isset($response->waybill_id)) {
                //考虑失败的情况
                $errMsg = '获取打印数据错误';
                if (isset($response->errmsg)) {
                    $errMsg = $response->errmsg;
                }
                if (isset($response->delivery_error_msg)) {
                    $errMsg = $response->delivery_error_msg;
                }
                $result[$tempIdStr][] = '获取打印数据错误：' . $errMsg;
            } else {
                $waybillOrderId = $requestArr[$tempIdStr]['params']['ewaybill_order_id'];
                $result[$tempIdStr][] = [
                    "waybill_code" => $response->waybill_id,
                    'print_data' => $response->print_info,
                    'object_id' => $requestArr[$tempIdStr]['params']['pack_id'],
                    'parent_waybill_code' => '',
                    'platform_waybill_id' => $waybillOrderId,
                ];
            }
        }
        \Log::info('微信视频号获取加密打印数据  result：', $result);
        return $result;
    }

    /**
     * 回收电子面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return bool
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = ''): bool
    {
        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);
        $wxClient->execute('post', '/channels/ec/logistics/ewaybill/biz/order/cancel', [
            'waybill_id' => $waybillCode,
            'delivery_id' => $cpCode,
            'ewaybill_order_id' => $platformWaybillId
        ]);
        return true;
    }

    /**
     * 返回商家自定义的模板（微信视频号提供了模板编辑器，商家可以自定义模板）
     * @param string $wpCode
     * @return array
     * @throws GuzzleException
     */
    public function getCustomizeWaybillTemplates(string $wpCode = ''): array
    {
        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);

        $result = [];

        try {
            $param = [];
            if ($wpCode) {
                $param['delivery_id'] = $wpCode;
            }
//            \Log::info('微信视频号获取模板列表', $param);
            $responseBody = $wxClient->execute('post', "/channels/ec/logistics/ewaybill/biz/template/get", $param);
//            \Log::info('微信视频号获取模板列表', $responseBody);
            $standardTemplates = [];
            if (empty($responseBody['total_template'])) {
                return $standardTemplates;
            }
            foreach ($responseBody['total_template'] as $totalTemplate) {
                $wpCode = $totalTemplate['delivery_id'];
                foreach ($totalTemplate['template_list'] as $template) {
                    $platformTemplate = new PlatformTemplate();
                    $platformTemplate->templateId = $template['template_id'];
                    $platformTemplate->templateName = $template['template_name'];
                    $platformTemplate->templateType = $template['template_type'];
                    $platformTemplate->wpCode = $wpCode;
                    $platformTemplate->shopId = $this->shop->id;
                    $platformTemplate->ownerId = $this->shop->identifier;
                    $platformTemplate->shopName = $this->shop->shop_name;
                    $standardTemplates[] = $platformTemplate;
                }


            }

            return $standardTemplates;

        } catch (\Exception $e) {
            \Log::error('微信视频号获取模板列表失败' . $e->getMessage(), [$e]);
            $result[] = $e->getMessage();
        }
        return $result;
    }

    /**
     * 微信视频号标准模板
     * @param string $wpCode
     * @return array
     * @throws GuzzleException
     */
    public function getCloudPrintStdTemplates(string $wpCode = ''): array
    {

        $wxClient = WxClient::newInstance($this->accessToken, $this->shop->service_id, $this->shop->specification_id);

        $result = [];

        try {
            $param = [];
//            \Log::info('微信视频号获取模板列表', $param);
            $responseBody = $wxClient->execute('post', "/channels/ec/logistics/ewaybill/biz/template/config", $param);

            \Log::info('微信视频号获取模板列表', $responseBody);
            $standardTemplates = [];
            foreach ($responseBody['config'] as $wpCode => $templates) {
                foreach ($templates as $type => $template) {
                    $platformTemplate = new PlatformTemplate();
//                    $platformTemplate->templateId = $template['template_id'];
                    $platformTemplate->templateName = $template['desc'];
                    $platformTemplate->templateType = $template['type'];
                    $platformTemplate->wpCode = $wpCode;
                    $platformTemplate->shopId = $this->shop->id;
                    $platformTemplate->ownerId = $this->shop->identifier;
                    $platformTemplate->shopName = $this->shop->shop_name;
                    $standardTemplates[] = $platformTemplate;
                }
            }

            return $standardTemplates;

        } catch (\Exception $e) {
            \Log::error('微信视频号获取模板列表失败' . $e->getMessage(), [$e]);
            $result[] = $e->getMessage();
        }
        return $result;

    }

    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        // TODO: Implement getCloudPrintStdTemplatesNew() method.
    }

    public function getLoginUrl($shopId)
    {
        // TODO: Implement getLoginUrl() method.
    }

    public function getAccessToken(string $code)
    {
        // TODO: Implement getAccessToken() method.
    }

    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        // TODO: Implement sendOrderLogisticsTraceMsg() method.
    }


    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1, $productType = null)
    {
        // TODO: Implement assemWaybillPackagesForOpenApi() method.
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }


    /**
     * 构建的是V2的区号参数
     * @param array $template
     * @param int $packageNum
     * @param PrintPackBo $printPackBo
     * @param WaybillAuth $waybillAuth
     * @param SenderAddressBo $branchAddressBo
     * @param $company
     * @return array
     * @throws ErrorCodeException
     */
    public function buildWaybillCodeRequest(array $template, int $packageNum, PrintPackBo $printPackBo, WaybillAuth $waybillAuth, SenderAddressBo $branchAddressBo, $company): array
    {
        $childMotherOrder = false;
        $total_pack_count = 1;
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
//            if (!in_array($template['wp_code'], self::ZIMUJIANMAP)){
//                throw new BusinessException('该快递公司不支持子母件');
//            }
            $total_pack_count = $packageNum;
            $childMotherOrder = true;
        }
        $isPlatformOrder = $printPackBo->isPlatformOrder();
        $tid = $printPackBo->getMasterOrderId();
//        if (!$isPlatformOrder) {
//            throw_error_code_exception(StatusCode::PARAMS_ERROR, "视频号不支持自定义订单取号");
//        }
        $orderCode = $printPackBo->getMasterOrderCode();
        $masterOrder = $printPackBo->getMasterOrder();
        $masterOrderArray = $printPackBo->getMasterOrderArray();
        $orderShop = $masterOrder->shop;
        /**
         * 发件人信息
         */
        $senderInfo['city'] = $branchAddressBo->city;
        $senderInfo['county'] = $branchAddressBo->district;
        $senderInfo['address'] = $branchAddressBo->address;
        $senderInfo['province'] = $branchAddressBo->province;
        $senderInfo['street'] = $branchAddressBo->street ?? '';
        $senderInfo['mobile'] = $branchAddressBo->mobile;
        $senderInfo['name'] = $branchAddressBo->sender_name;

        $tradeOrderInfoDto['ewaybill_order_code'] = $orderCode;
        $tradeOrderInfoDto['ewaybill_order_appid'] = $orderShop->identifier;
//        $mobile = get_array_value($masterOrderArray, 'order_cipher_info.receiver_phone_ciphertext', get_array_value($masterOrderArray, '', 'receiver_phone'));
//        $mobile = OrderUtil::removeVirtualPhoneDashSuffix($mobile);
        //面单收件人信息
        if (!empty($masterOrderArray['order_cipher_info']['receiver_phone_ciphertext'])) {
            $mobile = OrderUtil::removeVirtualPhoneDashSuffix($masterOrderArray['order_cipher_info']['receiver_phone_ciphertext']);
        } else {
            $mobile = $masterOrderArray['order_cipher_info']['receiver_phone_mask'];
        }
        if (!empty($masterOrderArray['order_cipher_info']['receiver_name_ciphertext'])) {
            $name = OrderUtil::removeVirtualPhoneSuffix($masterOrderArray['order_cipher_info']['receiver_name_ciphertext']);
        } else {
            $name = $masterOrderArray['order_cipher_info']['receiver_name_mask'];
        }
        if (!empty($masterOrderArray['order_cipher_info']['receiver_address_ciphertext'])) {
            $address = OrderUtil::removeVirtualPhoneSuffix($masterOrderArray['order_cipher_info']['receiver_address_ciphertext']);
        } else {
            $address = $masterOrderArray['order_cipher_info']['receiver_address_mask'];
        }

        $receiverInfo = [

            'city' => get_array_value($masterOrderArray, 'receiver_city'),
            'county' => get_array_value($masterOrderArray, 'receiver_district'),
            'address' => $address,
            'province' => get_array_value($masterOrderArray, 'receiver_state', get_array_value($masterOrderArray, 'receiver_province')),
            'street' => get_array_value($masterOrderArray, 'receiver_town', '无'),
            'mobile' => $mobile,
            'name' => $name,

        ];
        $items = [];

        if (!empty($printPackBo->print_order_item_bo_list)) {
            /**
             * @var PrintOrderItemBo $printOrderItemBo
             */
            foreach ($printPackBo->print_order_item_bo_list as $printOrderItemBo) {
                //  /** @var PrintOrderItemBo $printOrderItemBo */
                //                $items[] = [
                //                    'item_name' => $printOrderItemBo->order_item_info['sku_value'],
                //                    'item_count' => $printOrderItemBo->num,
                //                ];
                $temp = [];
                $temp['good_count'] = $printOrderItemBo->num;
                $orderItem = $printOrderItemBo->order_item_info;
                $goodsName = $orderItem->sku_value;
                if (empty($goodsName)) {
                    $goodsName = $orderItem->goods_title;
                }
                $temp['good_name'] = $goodsName;
                $temp['product_id'] = intval($orderItem->num_iid);
                $temp['sku_id'] = intval($orderItem->sku_id);
                $items[] = $temp;
            }
        }
        $tradeOrderInfoDto['goods_list'] = $items;
        $tradeOrderInfoDtos = [];
        $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
        //设置主体信息
        $data = [];
        $data['delivery_id'] = $company['wp_code'];
        $data['ewaybill_acct_id'] = $company['platform_account_id'] ?? '';
        if (!empty($company['branch_code'])) {
            $data['site_code'] = $company['branch_code'];
        }
        $data['ec_order_list'] = $tradeOrderInfoDtos;
        $data['sender'] = $senderInfo;
        $data['shop_id'] = $company['platform_shop_id'] ?? '';
        $data['receiver'] = $receiverInfo;
        $data['pack_id'] = $printPackBo->request_id;
        return $data;

    }

    /**
     * 微信视频号的拆单取号
     * @inerhitDoc
     * @throws ErrorCodeException
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        Log::info("微信视频号拆单取号开始");
        /**
         * @var array{orderId:string,errorCode:string,errorMsg:string} $errorInfos
         */
        $errorInfos = [];
        $preCreateData = [];
        $company = Company::query()->where('id', $template['company_id'])->first();
        $serviceId = $this->shop->getServiceId();
        $sepeciationId = $this->shop->getSpecificationId();
        $waybillAuth = $this->shop;
        $wxClient = WxClient::newInstance($this->accessToken, $serviceId, $sepeciationId);
        $url = "/channels/ec/logistics/ewaybill/biz/order/precreate";
        foreach ($printPackBoList as $index => $printPackBo) {
            $masterOrderId = $printPackBo->getMasterOrderId();
            $params = $this->buildWaybillCodeRequest($template, $packageNum, $printPackBo, $waybillAuth, $branchAddressBo, $company);
            $requestData = $wxClient->buildRequestData($url, $params);
            $preCreateData[$index] = [
                'params' => $requestData['apiParams'],
                'url' => $requestData['url'],
                'orderId' => $masterOrderId,
            ];
        }

        \Log::info('微信视频号获取加密打印数据  preCreateData：', $preCreateData);
        //电子面单预取号
        $responseBody = $this->poolCurl($preCreateData, "json", false, true);
        //预取号成功，把ewaybill_order_id放入参数中，进行正式取号
        $url = "/channels/ec/logistics/ewaybill/biz/order/create";
        $waybillOrderIds = [];
        $createData = [];
        foreach ($responseBody as $index => $response) {
            $printPackBo = $printPackBoList[$index];
            $preCreateItem = $preCreateData[$index];
            if (isset($response->ewaybill_order_id)) {
                $ewaybillOrderId = $response->ewaybill_order_id;
                $createData[$index] = $preCreateData[$index];
                $createData[$index]['params']['ewaybill_order_id'] = $ewaybillOrderId;
                $createData[$index]['params']['template_id'] = "single";
                if (!empty($template['time_delivery']) && intval($template['time_delivery']) > 0) {
                    $createData[$index]['params']['order_type'] = intval($template['time_delivery']);
                }
                $serviceListStr = $template['service_list'];
                if (!empty($serviceListStr)) {
                    $createData[$index]['params']['order_vas_list'] = self::buildOrderVasList($serviceListStr, $printPackBo->getInsureAmount());
                }
                $temperatureRange = self::buildTemperatureRange($serviceListStr);
                if ($temperatureRange) {
                    $createData[$index]['params']['ext_info'] = ['temperature_range' => $temperatureRange];
                }

                $requestData = $wxClient->buildRequestData($url, $createData);
                $createData[$index]['url'] = $requestData['url'];

            } else {
                //如果预取号失败，删除该订单
                unset($preCreateData[$index]);
                $errCode = $response->errcode;
                $errMsg = $response->errmsg;
                $errorInfos[] = [
                    'orderId' => $preCreateItem['orderId'],
                    'errorCode' => $errCode,
                    'errorMsg' => $errMsg
                ];
            }
        }


        //正式取号，获取电子面单号
        \Log::info('正式取号参数', $createData);
        $responseBody = $this->poolCurl($createData, "json", false, true);
        /**
         * @var  array{waybillCode:string,printData:string,orderId:string} $successPrintData
         */
        $successPrintData = [];
        /**
         * @var array{waybillCode:string,errorMsg:string,orderId:string} $errorPrintData
         */
        $errorPrintData = [];
        foreach ($responseBody as $index => $response) {
            \Log::info("正式取号的结果", ["index" => $index, "response" => $response]);
            $createDataItem = $createData[$index];
            if (!isset($response->waybill_id)) {
                //考虑失败的情况
                $errMsg = '获取打印数据错误';
                if (isset($response->errmsg)) {
                    $errMsg = $response->errmsg;
                }
                if (isset($response->delivery_error_msg)) {
                    $errMsg = $response->delivery_error_msg;
                }
                $errorInfos[$index] = [
                    'orderId' => $createDataItem['orderId'],
                    'errorCode' => $createDataItem['errorCode'] ?? 0,
                    'errorMsg' => $errMsg,
                ];
            } else {
                $waybillOrderId = $response->ewaybill_order_id;
                $successPrintData[$index] = [
                    "waybill_code" => $response->waybill_id,
                    'print_data' => $response->print_info,
                    'object_id' => $preCreateData[$index]['params']['pack_id'],
                    'parent_waybill_code' => '',
                    'platform_waybill_id' => $waybillOrderId,
                ];
            }
        }
        $printDataPackBoList = [];
        \Log::info("打印结果", ["successPrintData" => $successPrintData, "errorInfos" => $errorInfos]);
        foreach ($printPackBoList as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            $orderId = $printPackBo->getMasterOrderId();

            if (isset($successPrintData[$index])) {
                $waybillsPrintData = new WaybillsPrintDataBo();
                $waybillCode = $successPrintData[$index]['waybill_code'];
                $waybillsPrintData->waybill_code = $waybillCode;
                $waybillsPrintData->package_id = $printPackBo->package_id;
                $waybillsPrintData->encrypted_data = $successPrintData[$index]['print_data'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintData);
                $waybillsPrintData->platform_waybill_id = $successPrintData[$index]['platform_waybill_id'];
                $printDataPackBo->waybill_code = $waybillCode;
                $printDataPackBo->wp_code = $template['wp_code'];
            } else {
                $error = $errorInfos[$index] ?? null;
                if ($error) {
                    $printDataPackBo->setError([$error['errorCode'], $error['errorMsg']]);
                } else {
                    $printDataPackBo->setError([0, '获取打印数据失败，请重试']);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;

        }
//        \Log::info("返回取号数据", $printDataPackBoList);
        return $printDataPackBoList;
    }

    /**
     * 解析增值服务,组装成order_vas_list， 模板的service_list里面放了全部的增值服务数据，但只有部分会用做order_vas_list
     * 其他部分会做其他字段
     *
     * $template['service_list'] 包含的json字符串格式参考，{"SVC-TEM":{"value":"7"}}
     */
    static function buildOrderVasList(?string $serviceListStr, string $insureAmount): array
    {
        $orderVasList = [];
        if (empty($serviceListStr)) {
            return $orderVasList;
        }
        $serviceList = json_decode($serviceListStr, true);
        foreach ($serviceList as $key => $item) {
            //排除掉一些特殊的增值服务
            if (in_array($key, self::EXCLUDE_VAS_TYPE)) {
                continue;
            }
            $service = ["vas_type" => $key];
            $value = ArrayUtil::getArrayValue($item, "value");
            $desc = get_array_value($item, "desc", null);
            //保价有两种
            if (in_array($key, self::INSURE_VAS_TYPE)) {
                \Log::info("处理保价", ["insureAmount" => $insureAmount, "item" => $item]);
                //对保价金额进行处理，如果是-1，就是按订单金额保价，除此以外按设定金额保价
                if ($value != -1) {
                    \Log::info("保价金额不是-1,按设定的金额处理", ["item" => $item]);

                } else {
                    if (bccomp($insureAmount, "0") > 0) {
                        \Log::info("保价金额是-1,订单金额>0 按订单金额处理", ["insureAmount" => $insureAmount]);
                        $value = round_bcmul($insureAmount, "100", 0); //  (string)($insureAmount * 100);
                    } else {
                        \Log::info("保价金额是-1,订单金额=0 不处理这个保价", ["insureAmount" => $insureAmount]);
                        continue;
                    }
                }

            }
            if (!$value) {
                $service["vas_value"] = $value;
            }
            if (!$desc) {
                $service["vas_detail"] = $desc;
            }
            $orderVasList[] = $service;
        }
        \Log::info("处理增值服务", $orderVasList);
        return $orderVasList;
    }


    /**
     * 获取温层信息
     * @param string|null $serviceListStr
     * @return int|null
     */
    static function buildTemperatureRange(?string $serviceListStr): ?int
    {
        if (empty($serviceListStr)) {
            return null;
        }
        $serviceList = json_decode($serviceListStr, true);
        $temperatureRangeConfig = get_array_value($serviceList, 'temperature_range');
        if (empty($temperatureRangeConfig)) {
            return null;
        }
        return get_array_value($temperatureRangeConfig, 'value');

    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }

    /**
     * 获取物流状态
     * @param $status
     * @return int|mixed
     */
    public static function formatOrderTraceStatus($status)
    {
        return self::OrderTraceMap[$status] ?? OrderTraceList::STATUS_OTHER;
    }
}
