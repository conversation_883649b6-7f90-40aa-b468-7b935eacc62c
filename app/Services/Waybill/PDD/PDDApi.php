<?php

namespace App\Services\Waybill\PDD;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ClientException;
use App\Models\OrderTraceList;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Client\CurlResult;
use App\Services\Client\DyClient;
use App\Services\Client\PddClient;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\ArrayUtil;
use App\Utils\WaybillUtil;
use Illuminate\Support\Facades\Log;
use TopClient\domain\Order;

class PDDApi extends AbstractWaybillService
{
    const EXCLUDE_VAS_TYPE = [];

    const INSURE_VAS_TYPE=["SVC-INSURE","INSURE"];


    protected $baseUrl = 'http://gw-api.pinduoduo.com/api/router';
    protected $apiUrl = '';
    protected $dataType = 'JSON';
    protected $version = 'V1';
    private $authConfig;
    private $clientID;
    private $clientSecret;
    private $timestamp;
    protected $is_test = false;
    protected $waybillPlatformType = PlatformConst::WAYBILL_PDD; // 电子面单平台类型

    public function __construct(string $accessToken = '')
    {
        $this->authConfig = config('waybill.pdd');
        $this->clientID = $this->authConfig['client_id'];
        $this->clientSecret = $this->authConfig['client_secret'];
        $this->accessToken = $accessToken;
        $this->timestamp = time();
    }

    public function isTest()
    {
        $this->is_test = true;
    }

    //物流状态码
    const TRACE_ARR = [
        'GOT' => OrderTraceList::STATUS_GOT,
        'SEND' => OrderTraceList::STATUS_SEND,
        'SIGN' => OrderTraceList::STATUS_SIGN,
        'ARRIVAL' => OrderTraceList::STATUS_ARRIVAL,
        'DEPARTURE' => OrderTraceList::STATUS_DEPARTURE,
        'FAIL' => OrderTraceList::STATUS_FAIL,
        'REJECTION' => OrderTraceList::STATUS_REJECTION,
        'STAY_IN_WAREHOUSE' => OrderTraceList::STATUS_STAY_IN_WAREHOUSE,
        'SIGN_ON_BEHALF' => OrderTraceList::STATUS_SIGN_ON_BEHALF,
        'OTHER' => OrderTraceList::STATUS_OTHER,
        'RETURN' => OrderTraceList::STATUS_RETURN,
        'IN_CABINET' => OrderTraceList::STATUS_IN_CABINET,
        'OUT_CABINET' => OrderTraceList::STATUS_OUT_CABINET,
    ];

    public function getLoginUrl($shopId)
    {
        return null;
    }

    public function getAccessToken(string $code)
    {
        return [];
    }

    /**
     * 请求接口
     * @param $apiName
     * @param array $data
     * @return array|string
     * @throws \Exception
     */
    public function request($apiName, $data = array())
    {
        $param = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->apiUrl, $param, 'POST');

//	    Log::info(var_export($response, true));

        return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array $data
     * @return array
     */
    public function createRequestParam(string $apiName, array $data = array())
    {
        $params = [
            'type' => $apiName,
            'client_id' => $this->clientID,
            'data_type' => $this->dataType,
            'version' => $this->version,
//            'access_token' => $this->accessToken,
            'timestamp' => $this->timestamp,
        ];
        if ($accessToken = $this->accessToken) { // 无需授权的接口，该字段不参与sign签名运算
            $params['access_token'] = $accessToken;
        }

        $signature = $this->sign(array_merge($params, $data));
        $params['sign'] = $signature;
        $this->apiUrl = '';
        $this->apiUrl = $this->baseUrl . '?';
        foreach ($params as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);

        $this->setRequestData($data);
        return $data;
    }

    /**
     * 签名
     * @param string $params
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);
        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $apiMethod = 'pdd.waybill.search';
        $apiParams = [];
        if (!empty($wp_code)) {
            $apiParams['wp_code'] = $wpCode;
        }
        $pddClient = PddClient::newInstance($this->accessToken,$this->shop->id);
        $result = $pddClient->execute($apiMethod, $apiParams);
        $waybillsInfo = [];
        $waybill_apply_subscription_cols = array_get($result, 'pdd_waybill_search_response.waybill_apply_subscription_cols', []);
        foreach ($waybill_apply_subscription_cols as  $waybill_apply_subscription_col) {
            $branchAccounts = [];
            $wpType = $waybill_apply_subscription_col['wp_type'];
            //wp_type枚举值:1,"加盟型"2,"直营且月结账号必填” 3,"直营且月结账号非必填”4,"直营且无月结账号
            $isSelfOperated=($wpType!=1);

            foreach ($waybill_apply_subscription_col['branch_account_cols'] as $key => $account_col) {
                $branchAccounts[$key] = [
                    'branch_code' => $account_col['branch_code'] ?? 0,
                    'branch_name' => $account_col['branch_name'] ?? '',
                    'quantity' => $isSelfOperated?-1:( $account_col['quantity'] ?? 0),
                    'cancel_quantity' => $account_col['cancel_quantity'] ?? 0,
                    'recycled_quantity' => $account_col['recycled_quantity'] ?? 0,
                    'allocated_quantity' => $account_col['allocated_quantity'] ?? 0,
                    'shipp_addresses' => $account_col['shipp_address_cols'],
                    'service_info_cols' => array_get($account_col, 'service_info_cols')
                ];
            }


            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $waybill_apply_subscription_col['wp_code']
            ];
        }
        Log::info("拼多多面单查询结果：", [ "shopId"=>$this->shop->id, "result"=>  $waybillsInfo]);
        return $waybillsInfo;
    }


    /**
     * @param SenderAddressBo $branchAddressBo
     * @param array $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return array|PrintDataPackBo[]
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);

        $pddClient = PddClient::newInstance($this->accessToken,$this->shop->id);
        $data=$pddClient->poolCurl('pdd.waybill.get',$requestDataList);

        $printDataPackBoList=[];
        foreach ($printPackBoList as $index => $printPackBo) {

            $curlResult=$data[$index];
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if ($curlResult->isSuccess()) {

                $modules=$curlResult->arrayGet('pdd_waybill_get_response.modules');
                $masterModule=array_filter($modules,function($module){
                    //没有设置parent_waybill_code 或者 waybill_code==parent_waybill_code 就是主面单
                    return !isset($module['parent_waybill_code'])||$module['waybill_code']==$module['parent_waybill_code'];
                })[0];
                $masterWaybillsPrintDataBo=new WaybillsPrintDataBo();
                $masterWaybillsPrintDataBo->copyByPrintDataPackBo($printDataPackBo);
                $masterWaybillsPrintDataBo->waybill_code=$masterModule['waybill_code'];
                $printData = $masterModule['print_data'];
                $printData = $this->parsePrintData($printData);
                $masterWaybillsPrintDataBo->encrypted_data= $printData['encryptedData'];
                $masterWaybillsPrintDataBo->sign=$printData['signature'];
                $masterWaybillsPrintDataBo->version=$printData['ver'];
                $templateUrl = $template['template_url'];
                $masterWaybillsPrintDataBo->templateURL= $templateUrl;
                $printDataPackBo->setWaybillsPrintData($masterWaybillsPrintDataBo);
                if(sizeof($modules)>1){
                    //把子件都找出来，通过parent_waybill_code不为空来判断
                    $subModules=array_filter($modules,function($module){
                        return isset($module['parent_waybill_code']);
                    });
                    $subWaybillCodeArr=array_column($subModules,'waybill_code');
                    /**
                     * @var WaybillsPrintDataBo[] $subWaybillsPrintDataArr
                     */
                    $subWaybillsPrintDataArr=[];
                    foreach ($subModules as $subModule){
                        $waybillsPrintDataBo=new WaybillsPrintDataBo();
                        $waybillsPrintDataBo->copyByPrintDataPackBo($printDataPackBo);
                        $printData = $subModule['print_data'];
                        $printData = $this->parsePrintData($printData);
                        $waybillsPrintDataBo->waybill_code=$subModule['waybill_code'];
                        $waybillsPrintDataBo->encrypted_data=$subModule['print_data'];
                        $waybillsPrintDataBo->sign=$printData['signature'];
                        $waybillsPrintDataBo->templateURL= $templateUrl;
                        $waybillsPrintDataBo->version=$printData['version']??'';
                        $waybillsPrintDataBo->userId=$this->shop->identifier;


                    }
                    $printDataPackBo->sub_waybill_code_arr = $subWaybillCodeArr;
                    $printDataPackBo->setSubWaybillsPrintDataArr($subWaybillsPrintDataArr);

                    //如果是子母件
                }


                $printDataPackBo->waybill_code =$masterModule['waybill_code'];
                $printDataPackBo->wp_code = $template['wp_code'];

            } else{
                Log::info("拼多多取号失败：", [ "shopId"=>$this->shop->id, "result"=> $requestDataList[$index],"curlResult"=>$curlResult,"datapack"=> $printDataPackBo ]);
                $printDataPackBo->setError(ErrorConst::PLATFORM_PDD_ERROR, $curlResult->errorMsg);
            }
            $printDataPackBoList[] = $printDataPackBo;
        }

        return $printDataPackBoList;

    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        //一单多包，普通取号
        $result = [];
        if ($packageNum > 1) {
            foreach ($orders as $order) {
                $idStr = handleOrderIdStr($order);
                //单个订单获取面单的详情数组
                $result[$idStr] = $this->waybillGet($sender, $order->toArray(), $template, $packageNum);
            }

            return $result;
        }

        //非一单多包情况，并行异步请求
        $type = 'pdd.waybill.get';
        $data = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url' => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'POST');

        Log::debug('pdd_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    /**
     * 获取面单号
     * @param     $sender
     * @param     $order
     * @param     $template
     * @param int $packageNum
     * @return array|bool|string
     */
    public function waybillGet($sender, $order, $template, $packageNum = 1)
    {
        $type = 'pdd.waybill.get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($type, $info);
                if (!isset($waybill->modules)) {
                    Log::error('API=>' . $type, [$waybill]);

                    return false;
                }
                $waybillsData = [];
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                Log::error("获取电子面单失败" . $type, [$e->getMessage(), $e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 组装面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        $returnArr = [];
        $num = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile'] = $sender['mobile'];
            $senderInfo['phone'] = '';
            $senderInfo['name'] = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city'] = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town'] = '';
            $senderInfo['address']['detail'] = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id'] = $order['tid'] ?? $order['id'] . '-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info'] = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list' => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id' => "",
                'goods_description' => "",
                'items' => $items,
                'volume' => "",
                'weight' => "",
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient'] = [
                'address' => [
                    'city' => $order['receiver_city'],
                    'detail' => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town' => "",
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile' => $order['receiver_phone'],
                'name' => $order['receiver_name'],
                'phone' => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id'] = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['wp_code'] = $template['wp_code'];
            $data['need_encrypt'] = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender'] = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 取消面单
     * @param string $wpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return array|bool|mixed|string
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $wpCode, string $waybillCode, string $platformWaybillId = '')
    {
        $apiMethod = 'pdd.waybill.cancel';
        $data = array(
            'wp_code' => $wpCode,
            'waybill_code' => $waybillCode
        );
        $shopId = $this->shop->id;
        $pddClient = PddClient::newInstance($this->accessToken, $shopId);
        $result=$pddClient->execute($apiMethod, $data);
        Log::info('取消面单',["waybillCode"=>$waybillCode,"wp_code"=>$wpCode,"shopId"=>$shopId,"result"=>$result]);
        return  array_get($result, 'pdd_waybill_cancel_response.cancel_result', false);

    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplates(string $wpCode = ''):array
    {
        $type = 'pdd.cloudprint.stdtemplates.get';
        $data = array(
            'wp_code' => $wpCode,
        );
        $pddClient = PddClient::newInstance($this->accessToken);
        $result=$pddClient->execute($type, $data);

        $datas = array_get($result, 'pdd_cloudprint_stdtemplates_get_response.result.datas');
        if (empty($datas)) {
            return [];
        }
        return array_column($datas[0], null, 'standard_waybill_type');
    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '', ?string $extendedInfo = null)
    {
        $type = 'pdd.cloudprint.stdtemplates.get';
        $data = array(
            'wp_code' => $wpCode,
        );
        $result = $this->request($type, $data);
        if (!isset($result->result)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->result->datas['0'])) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception('面单服务查询失败!');
        }

        return array_column($result->result->datas['0']->standard_templates, null);
    }

    /**
     * 物流轨迹订阅
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return bool|mixed
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        $type = 'pdd.logistics.isv.trace.notify.sub';
        $data = array(
            'ship_code' => $expressCode,
            'tel' => $receiverPhone,
            'track_no' => $expressNo,
        );
        $result = $this->request($type, $data);

        \Log::debug('消息订阅返回值', ['express_no' => $expressNo, 'result' => $result]);

        if (isset($result->is_success)) {
            return true;
        }
        return false;
    }

    /**
     * @param $printData
     * @return array{encryptedData:string,signature:string,templateUrl:string}
     */
    public function parsePrintData($printData): array
    {
        return json_decode($printData, true);
    }

    protected function getClient()
    {
        return new PddClient(config('waybill.pddwb.client_id'), config('waybill.pddwb.client_secret'));
    }

    /**
     * 获取订单物流详情
     * @param array $waybill
     * @return mixed
     * @throws ClientException
     */
    public function sendGetOrderTraceList(array $waybill):array
    {
        $client = PddClient::newInstance($this->accessToken,$this->shop->id);
        $params = [
            'company_code' => $waybill['express_code'],
            'mail_no' => $waybill['express_no'],
            'cache' => true, //是否缓存
        ];
        $response = $client->execute('pdd.logistics.ordertrace.get', $params);
        Log::info('pdd.logistics.ordertrace.get', $response);

        return $this->formatToOrderTrace(array_merge($waybill, [
            'trace_list' => $response['logistics_ordertrace_get_resposne']['trace_list'],
        ]));
    }

    /**
     * 物流数据整理
     * @param array $orderTrace
     * @return array
     */
    public function formatToOrderTrace(array $orderTrace): array
    {
        $latest = collect($orderTrace['trace_list'])->sortByDesc('time')->first();
//		$orderTraceMap = array_combine(self::TRACE_ARR, OrderTraceList::STATUS_ARR);
        $status = self::TRACE_ARR[$latest['action']] ?? OrderTraceList::STATUS_OTHER;

        return [
            "type" => $orderTrace['type'],
            'tid' => $orderTrace['tid'],
            'express_code' => $orderTrace['express_code'],
            'express_no' => $orderTrace['express_no'],
            'status' => $status,
            'action' => $latest['action'],
            'receiver_province' => $orderTrace['receiver_province'],
            'receiver_name' => $orderTrace['receiver_name'],
            'send_at' => $orderTrace['send_at'],
            'latest_updated_at' => $latest['status_time'],
            'latest_trace' => $latest['desc'],
            'trace_list' =>[],
        ];
    }

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1, $productType = null)
    {
        //并行异步请求
        $type = 'pdd.waybill.get';
        $data = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url' => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'POST');

        Log::debug('pdd_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' => $printData['encryptedData'],
                        'templateUrl' => $printData['templateUrl'],
                        'signature' => $printData['signature'],
                        'ver' => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile'] = $sender['mobile'];
            $senderInfo['phone'] = '';
            $senderInfo['name'] = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city'] = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town'] = '';
            $senderInfo['address']['detail'] = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id'] = $order['package_id'] ?? $order['tid'] ?? $order['id'] . '-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info'] = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list' => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id' => "",
                'goods_description' => "",
                'items' => $items,
                'volume' => "",
                'weight' => "",
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient'] = [
                'address' => [
                    'city' => $order['receiver_city'],
                    'detail' => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town' => "",
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile' => $order['receiver_phone'],
                'name' => $order['receiver_name'],
                'phone' => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp);
            $tradeOrderInfoDto['user_id'] = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['wp_code'] = $wpCode;
            $data['need_encrypt'] = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender'] = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    public function assemFactoryWaybillPackages($sender, $factoryOrders, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }

    /**
     *
     *
     * @param SenderAddressBo $sender
     * @param PrintDataPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return array
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, array $printPackBoList, array $template, int $packageNum): array
    {

        Log::info('电子面单取号请求', [$sender,$template]);
        $list = [];
        //发件人信息
        $senderInfo = [
            'address' => [
                'province' => $sender->province,
                'city' => $sender->city,
                'district' => $sender->district,
                'detail' => $sender->address,
                'town' => $sender->street,
            ],

            'name' => $sender->sender_name,
            'mobile' => $sender->mobile,
            'phone' => ''

        ];
        $order_infos = [];
        $isPtOrder = true;
        $total_pack_count = 1;
        $wpCode = $template['wp_code'];
        //子母件 目前支持shunfeng、jd、debangwuliu、shunfengkuaiyun、annengwuliu
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
//            if (!in_array($template['wp_code'], self::ZIMUJIANMAP)){
//                throw new BusinessException('该快递公司不支持子母件');
//            }
            $total_pack_count = $packageNum;
        }
        $result=[];
        foreach ($printPackBoList as $printDataPackBo) {
            $items = [];
            $count=1;
            $trade_order_info_dtos=[];
            $trade_order_info_dto=[];
            foreach ($printDataPackBo->print_order_item_bo_list as $printOrderItemBo) {
                /** @var PrintOrderItemBo $printOrderItemBo */
                $items[] = [
                    'name' => $printOrderItemBo->order_item_info['sku_value'],
                    'count' => $printOrderItemBo->num,
                ];
                $count++;
                //多余100个就不添加了
                if ($count>100){
                    break;
                }
            }
            $order = $printDataPackBo->master_order_info;
            $orderArr = $order->toArray();
            $tid = $this->getTidByOrder($orderArr);
            $isPtOrder = $printDataPackBo->isPlatformOrder();

            $order_info = [
                'trade_order_list' =>[ $tid],
                'order_channels_type'=>$isPtOrder?'PDD':'OTHERS'
            ];
            $trade_order_info_dto['order_info']=$order_info;
            $trade_order_info_dto['object_id']=$printDataPackBo->request_id."-".time()   ;
            $package_info=[
                'id' => $printDataPackBo->package_id,
                'items'=>$items,
            ];

            if ($total_pack_count > 1) {
                $package_info['total_packages_count'] = $total_pack_count;
            }
            $trade_order_info_dto['package_info']=$package_info;
            $trade_order_info_dto['template_url']=$template['template_url'];
            $trade_order_info_dto['user_id']=$this->shop->identifier;
            $recipient=[
                'address' => [
                    'province' => $orderArr['receiver_state'] ?? $orderArr['receiver_province'],
                    'city' => $orderArr['receiver_city'],
                    'district' => $orderArr['receiver_district'],
                    'detail' => !empty($orderArr['order_cipher_info']) ? $orderArr['order_cipher_info']['receiver_address_ciphertext'] : stripslashes($orderArr['receiver_address']),
                    'town' => '',
                ],
                'mobile' =>!empty($orderArr['order_cipher_info']) ? $orderArr['order_cipher_info']['receiver_phone_ciphertext'] : $orderArr['receiver_phone'],
                'name' =>  !empty($orderArr['order_cipher_info']) ? $orderArr['order_cipher_info']['receiver_name_ciphertext'] : $orderArr['receiver_name'],
                'phone' => !empty($orderArr['order_cipher_info']) ? $orderArr['order_cipher_info']['receiver_telephone_ciphertext'] : '',
            ];
            $trade_order_info_dto['recipient']=$recipient;


            //增值服务
            if (!empty($template['service_list'])) {

                $newServiceList = $this->buildOrderVasList($template['service_list'], $printDataPackBo);
                $trade_order_info_dto['logistics_services'] = $newServiceList;
            }
//            if ($packageNum > 1){
//                $order_infos[] = $order_info;
//            }
            $trade_order_info_dtos[] = $trade_order_info_dto;
            $param_waybill_cloud_print_apply_new_request=[];
            $param_waybill_cloud_print_apply_new_request['wp_code']=$wpCode;
            $param_waybill_cloud_print_apply_new_request['sender']=$senderInfo;
            $param_waybill_cloud_print_apply_new_request['trade_order_info_dtos']=$trade_order_info_dtos;
            $result[]=["param_waybill_cloud_print_apply_new_request"=>json_encode($param_waybill_cloud_print_apply_new_request)];
        }

        return $result;

    }

    /**
     * 构建增值服务
     * @param  string|null  $serviceListStr
     * @param  PrintPackBo  $printDataPackBo
     * @return string
     */
    public function buildOrderVasList(?string $serviceListStr, PrintPackBo $printDataPackBo): string
    {
        $orderVasList = [];
        if(empty($serviceListStr)){
            return "";
        }
        Log::info("增值服务",["service_list"=> $serviceListStr]);
        $serviceList = json_decode($serviceListStr, true);
        foreach ($serviceList as $key => $item) {
            //排除掉一些特殊的增值服务
            if(in_array($key, self::EXCLUDE_VAS_TYPE)){
                continue;
            }
            $value=ArrayUtil::getArrayValue($item,"value");
            $desc=get_array_value($item,"desc",null);
            //保价有两种
            if ( in_array( $key, self::INSURE_VAS_TYPE)) {
                $insureAmount = $printDataPackBo->getInsureAmount();
                Log::info("处理保价",["insureAmount"=>$insureAmount,"item"=>$item]);
                //对保价金额进行处理，如果是-1，就是按订单金额保价，除此以外按设定金额保价
                if($value!=-1){
                    Log::info("保价金额不是-1,按设定的金额处理",["item"=>$item]);

                }else{
                    if(bccomp($insureAmount,"0")>0) {
                        Log::info("保价金额是-1,订单金额>0 按订单金额处理",["insureAmount"=>$insureAmount]);
                        $value = round_bcmul($insureAmount, "100",0); //  (string)($insureAmount * 100);
                    }else{
                        Log::info("保价金额是-1,订单金额=0 不处理这个保价",["insureAmount"=>$insureAmount]);
                        continue;
                    }
                }

            }


            $orderVasList[$key]= ["value"=>strval($value)];
        }
        if(empty($orderVasList)) {
            $vasOrderListString = "";
        }else {
            $vasOrderListString = json_encode($orderVasList, JSON_UNESCAPED_UNICODE);
        }
        Log::info("处理增值服务",[$vasOrderListString]);
        return $vasOrderListString;
    }

}
