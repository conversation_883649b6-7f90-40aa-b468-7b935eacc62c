<?php

namespace App\Services\Printing\Stage\CustomizeOrder;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\PrintException;
use App\Models\Company;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\PrintRecord;
use App\Models\Shop;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\PrintDataService;
use App\Services\Printing\Payload\CustomizeOrderPrintPayload;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use Illuminate\Support\Facades\Log;

/**
 * 自由打印新取号
 * 基于\App\Models\CustomizeOrder::newWaybillHistories重构
 */
class CustomizeOrderFreshPrintDataStage
{
    public function __invoke(CustomizeOrderPrintPayload $payload)
    {
        $template = $payload->getTemplateInfo();
        $userId = $payload->getUserId();
        $company = $template['company'];
        $printData = [];
        $shopId = $payload->getCurrentShopId();
        $failedOrders = [];
        $batchNo = $payload->getBatchNo();
        $newOrderIds = $payload->getOrderIds();
        $waybillAuth = $payload->getWaybillAuth();
        $accessToken = $waybillAuth->access_token;
        $packageNum = $payload->getPackageNum();
        $branchAddress = $payload->getBranchAddress();
        $senderAddress = $payload->getSenderAddress();
        $orderIds = $payload->getOrderIds();
        $orders = CustomizeOrder::whereIn('id', $orderIds)->get();
        if (empty($orders)) {
            throw new ApiException([], ErrorConst::ORDER_DATA_EMPTY[0]);
        }
        foreach ($orders as $order) {
            //抖音关联订单 查询加密数据
            if (isset($order->order_no)) {
                $platformOrder = Order::query()->with('OrderCipherInfo')->where('tid', $order->order_no)->first();
                if ($platformOrder->OrderCipherInfo) {
                    $order->order_cipher_info = $platformOrder->OrderCipherInfo;
//                    $order->receiver_address = $platformOrder->receiver_address;
//                    $order->receiver_name = $platformOrder->receiver_name;
//                    $order->receiver_phone = $platformOrder->receiver_phone;
                }
                // 微信加密
                if (Environment::isWx() && !empty($order->order_cipher_info) && !empty($order->order_cipher_info->receiver_phone_ciphertext)) {
                    $order->receiver_phone = appDecrypt($order->order_cipher_info->receiver_phone_ciphertext);
                    $order->receiver_name = appDecrypt($order->order_cipher_info->receiver_name_ciphertext);
                    $order->receiver_address = appDecrypt($order->order_cipher_info->receiver_address_ciphertext);
                    unset($order->order_cipher_info);
                }
            }
            //每次取号判断是否是虚拟网点，且正常状态为0，面单余额不为'=

            $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
            if($waybillAuth instanceof Shop){
                $waybillService->setShop($waybillAuth);
            }
            if(!Company::hasEnoughQuantity($company->id,$packageNum)){
                throw new PrintException([], 1001, '电子面单余额不足，请充值');
            }
            $packages= $waybillService->assemCompanyWaybillPackages($company,$branchAddress[$order->id],[$order],$template, $packageNum);
            $waybillCount = $waybillService::countWaybillCode($packages);
//            \Log::info("取号的结果数据",["company"=>$company,"packags"=>$packages,"count"=>$waybillCount]);
            if($waybillCount>0) {
                Company::dealUseQuantity($company->id, $waybillCount);
            }


//            if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
////                    if($ordersCount > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
////                        throw new ApiException(ErrorConst::WAYBILL_INSUFFICIENT_BALANCE);
////                      /  throw new \Exception('打印订单数大于可用单号余额!');
////                    }
//                if ($company['source_status'] == Company::SOURCE_COMPANY_STATUS_OPEN && $company['quantity'] !== Company::INIT_QUANTITY) {
//                    $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
//                    $packages = $waybillService->assemWaybillPackages($branchAddress[$order->id], [$order], $template, $packageNum);
//                    $company->dealUseQuantity();
//                    //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
////                        if($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
////                            $query = Company::where('id',$company['id']);
////                            $query->decrement('quantity');
////                            $query->increment('allocated_quantity');
////                        }else{
////                            //无限量的不用减少电子面单余额数量，已用面单数量增加
////                            Company::incrementAllocatedQuantity($company['id']);
////                        }
//                } else {
//                    throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
////                        throw new \Exception('电子面单余额为0，请联系您的单号分享者!');
//                }
//            } else {
//                //非虚拟网点，正常取号
//                $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
//                $packages = $waybillService->assemWaybillPackages($branchAddress[$order->id], [$order], $template, $packageNum);
//            }

            foreach ($packages as $idStr => $package) {
                if (isset($order->order_cipher_info)) {
                    unset($order->order_cipher_info);
                }
                Log::info('package', [$package]);
                //错误信息
                if (!is_string($package[0]) && is_array($package[0]) && !empty($package[0]) && (isset($package[0]['err_no']) ? $package[0]['err_no'] == 0 : true)
                    && (Waybill::AUTH_SOURCE_DY == $template['auth_source'] ? (!isset($package[0]['data']['ebill_infos'])) : true)
                    && (Waybill::AUTH_SOURCE_KS == $template['auth_source'] ? (!isset($package[0]['result'])) : true)) {

                    foreach ($package as $key => $item) {
                        //查询一个订单是否存在旧单号
                        $tempWaybillCode = $item['parent_waybill_code'] ? $item['parent_waybill_code'] : $item['waybill_code'];
                        $waybill_code_Arr = isset($order->waybill_code) ? explode(',', $order->waybill_code) : '';
                        $expressNoArr = isset($order->waybill_code) && $waybill_code_Arr ? array_unique(array_merge($waybill_code_Arr, [$tempWaybillCode])) : $tempWaybillCode;
                        $ret = $order->update([
                            'template_id' => $template['id'],
                            'wp_code' => $template['wp_code'],
                            'parent_waybill_code' => $item['parent_waybill_code'] ?? '',
                            'waybill_code' => isset($order->waybill_code) && $expressNoArr ? implode(',', $expressNoArr) : $tempWaybillCode,
                            'recycled_at' => null,
                        ]);
                        if (!$ret) {
                            throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
                            //                        throw new \Exception('订单更新面单信息失败！');
                        }

                        $printData[] = PrintDataService::templateData($senderAddress[$order->id], $order, $template,
                            $item, [], true, $key + 1, $packageNum);

                        $printDataItems = '';
                        if ($template['auth_source'] != Waybill::AUTH_SOURCE_JD) {
                            $printDataItems = json_encode($printData[0]['contents'][1]['data']['printNextItemBeans'] ?? '');
                        }
                        if($waybillAuth instanceof Shop){
                            $waybillShopId=$waybillAuth['id'];
                        }else{
                            $waybillShopId=$waybillAuth['shop_id'];
                        }
                        $history = WaybillHistory::create([
                            'user_id' => $userId,
                            'shop_id' => $waybillShopId,//$waybillAuth->id,
                            'order_id' => $order->id,
                            'order_no' => $order['order_no'] ?? '',
                            'template_id' => $template['id'],
                            'auth_source' => $template['auth_source'],
                            'source' => $company['source'] ?? '',
                            'source_shopid' => $company['source_shopid'] ?? '',
                            'parent_waybill_code' => $item['parent_waybill_code'],
                            'waybill_code' => $item['waybill_code'],
                            'wp_code' => $template['wp_code'],
                            'print_data' => in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS]) ? json_encode($item) : array_get($item, 'print_data'),
                            'receiver_province' => $order->receiver_province,
                            'receiver_city' => $order->receiver_city,
                            'receiver_district' => $order->receiver_district,
                            'receiver_name' => $order->receiver_name,
                            'receiver_phone' => $order->receiver_phone,
                            'receiver_address' => $order->receiver_address,
                            'print_data_items' => $printDataItems,
                            'batch_no' => $batchNo,
                            'outer_order_no' => $order['outer_order_no'] ?? "",
                        ]);
                        PrintRecord::create([
                            'user_id' => $userId,
                            'shop_id' => $shopId,
                            'order_id' => $order->id,
                            'history_id' => $history->id,
                            'order_no' => $order['order_no'] ?? '',
                            'waybill_code' => $item['waybill_code'],
                            'wp_code' => $template['wp_code'],
                            'receiver_province' => $order->receiver_province,
                            'receiver_city' => $order->receiver_city,
                            'receiver_district' => $order->receiver_district,
                            'receiver_town' => $order->receiver_town,
                            'receiver_address' => $order->receiver_address,
                            'receiver_name' => $order->receiver_name,
                            'receiver_zip' => $order->receiver_zip,
                            'receiver_phone' => $order->receiver_phone,
                            'buyer_remark' => $order->buyer_message,
                            'batch_no' => $batchNo,
                            'outer_order_no' => $order['outer_order_no'] ?? "",
                        ]);


                    }
                } else {
                    //$failedOrders[] = $packages[$order['id']][0];
                    //取号错误信息
                    $obj = new \stdClass();
                    $obj->text = '取号错误';
                    $obj->tid = $order['tid'] ?? null;
                    $obj->name = $order['receiver_name'] ?? null;
                    if (Waybill::AUTH_SOURCE_DY == $template['auth_source']) {
                        $obj->info = $package[0]['data']['err_infos'][0]['err_msg'] ?? "未知错误";
                    } else {
                        $obj->info = $package[0];
                    }
                    $failedOrders[] = json_encode($obj, 320); //失败数据
                }
            }
        }

        $payload->setPrintData($printData);
        $payload->setFailedData($failedOrders);
        return $payload;
    }
}
