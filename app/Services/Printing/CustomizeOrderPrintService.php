<?php

namespace App\Services\Printing;

use App\Services\Printing\Payload\CustomizeOrderPrintPayload;
use App\Services\Printing\Stage\BaseCheckWaybillBalanceStage;
use App\Services\Printing\Stage\CustomizeOrder\CustomizeOrderCheckWaybillBalanceStage;
use App\Services\Printing\Stage\CustomizeOrder\CustomizeOrderPrepareSenderAddressAndBranchInfoStage;
use App\Services\Printing\Stage\CustomizeOrder\CustomizeOrderPrintDataStage;
use League\Pipeline\Pipeline;

/**
 * 自由订单打印服务
 */
class CustomizeOrderPrintService extends BasePrintService
{

    private $customizeOrderPrintDataStage;
    private $customizeOrderPrepareSenderAddressAndBranchInfoStage;
    private $checkWaybillBalanceStage;
    private $customizeOrderPrintPipeline;
    public function __construct()
    {
        //初始化公共的内容s
        parent::__construct();

        //初始化自由打印相关的
        {
            $this->customizeOrderPrintDataStage=new CustomizeOrderPrintDataStage();
            $this->customizeOrderPrepareSenderAddressAndBranchInfoStage = new CustomizeOrderPrepareSenderAddressAndBranchInfoStage();
            $this->checkWaybillBalanceStage=new CustomizeOrderCheckWaybillBalanceStage();
            $this->customizeOrderPrintPipeline = (new Pipeline)
            ->pipe($this->prepareCompanyAndTemplateInfoStage)
            ->pipe($this->prepareWaybillAuthStage)
            ->pipe($this->checkWaybillBalanceStage)
            ->pipe($this->customizeOrderPrepareSenderAddressAndBranchInfoStage)
            ->pipe($this->customizeOrderPrintDataStage);
        }
    }

    public function  print(CustomizeOrderPrintPayload  $payload){
        $this->customizeOrderPrintPipeline->process($payload);
        \Log::info("自由打印Pipeline执行成功");
    }

}
