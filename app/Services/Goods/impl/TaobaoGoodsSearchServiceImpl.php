<?php

namespace App\Services\Goods\impl;

use App\Exceptions\ApiException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\OrderItem;
use App\Services\Goods\AbstractGoodsService;

/**
 * 淘宝的商品搜索服务实现类，因为淘宝没有开放批量查询的api，
 * 所以这个地方查询订单里面的商品信息来代替查询接口，然后把查到的商品信息再通过单个的接口查询商品详情
 */
class TaobaoGoodsSearchServiceImpl extends AbstractGoodsService
{

    /**
     * @var TaobaoGoodsServiceImpl $taobaoGoodsService;
     */
    private $taobaoGoodsService;
    public function __construct()
    {
        $this->taobaoGoodsService = new TaobaoGoodsServiceImpl();

    }

    public function setAccessToken($accessToken): AbstractGoodsService
    {
        parent::setAccessToken($accessToken);
        $this->taobaoGoodsService->setAccessToken($accessToken);
        return $this;
    }

    public function setShop($shop): void
    {
        parent::setShop($shop);
        $this->taobaoGoodsService->setShop($shop);
    }

    /**
     * 查询订单中的商品数据
     * @param GoodsSearchRequest $request
     * @return GoodsSearchResponse
     * @throws ApiException
     */

    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $response=new GoodsSearchResponse();
        $builder = OrderItem::query();

        $shop = $this->getShop();
        $builder->where('shop_id', $shop['id']);
        $builder->groupBy(['num_iid']);
        if($request->numIid){
            $builder->where('num_iid', $request->numIid);
        }
        if($request->goodsTitle){
            $builder->where('goods_title', 'like', '%' . $request->goodsTitle . '%');
        }
        $lengthAwarePaginator = $builder->paginate($request->pageSize, ['num_iid'], 'page', $request->currentPage);
        $lengthAwarePaginator->total();
        $items = $lengthAwarePaginator->items();
        if(sizeof($items)==$request->pageSize){
            $response->hasNext=true;
        }
        $numIids=array_column($items,'num_iid');
        $goods=$this->sendGetGoodsByGoodsId($numIids);
        $response->data=$goods;
        return $response;

    }

    public function formatToGoods(array $goods): array
    {
        return $this->taobaoGoodsService->formatToGoods($goods);
    }

    function formatToGood(array $goods): array
    {
        $formatToGood = $this->taobaoGoodsService->formatToGood($goods);
        return $formatToGood;
    }

    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        return $this->taobaoGoodsService->sendGetGoodsByGoodsId($goodsId);
    }
}
