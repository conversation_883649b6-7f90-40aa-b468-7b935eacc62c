<?php

namespace App\Services\Goods\impl;

use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Services\Client\KsClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

/**
 * 快手的商品服务
 */
class KsGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    protected $platformType = PlatformConst::PLATFORM_TYPE_KS;
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    /**
     * 商品批量格式转换
     * @param  array  $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods['items'] as $index => $good) {
            if(!empty($good)) {
                $list[] = $this->formatToGood($good);
            }
        }

        return $list;
    }

    /**
     * 商品构建
     * @param  array  $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['skuList'] as $index => $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['kwaiSkuId'],
                "sku_value" => $item['specification'],
                "outer_id" => $item['relSkuId'],
                "outer_goods_id" => $good['relItemId'],
                "sku_pic" => $item['imageUrl'],
                "is_onsale" => 0,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['kwaiItemId'],
            'outer_goods_id' => $good['relItemId'],
            'goods_title' => $good['title'],
            'goods_pic' => $good['mainImageUrl'],
            'is_onsale' => $good['shelfStatus'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => date('Y-m-d H:i:s', $good['createTime'] / 1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $good['updateTime'] / 1000),
            'skus' => $skus
        ];
    }

    /**
     * 获取商品列表
     * @param  int int $pageSize,
     * @param  int  $currentPage
     * @return array
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        return $this->callOpenItemListGet($pageSize, $currentPage);
    }

//    function sendGetSingleGoods(string $numIid)
//    {
//        $goods = $this->callOpenItemListGet(10, 1, $numIid);
//        if (empty($goods['items'])) {
//            return [];
//        }
//        return $goods['items'][0];
//    }

    /**
     * @param  int  $pageSize
     * @param  int  $currentPage
     * @param  null  $numIid
     * @return array|mixed
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     */
    protected function callOpenItemListGet(int $pageSize, int $currentPage, $numIid = null)
    {
        $client = KsClient::newInstance($this->getAccessToken());
        $params = [
//            'sellerId' => $this->getShopId(),
            'pageSize' => $pageSize,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => $currentPage,
        ];
        if (isset($numIid)) {
            $params['kwaiItemId'] = $numIid;
        }
        $goods = $client->execute('get', '/open/item/list/get', $params);

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }
        KsClient::handleErrorCode($goods);

//        \Log::info('ks_goods_result', [$goods]);
        if ($goods['result'] != 1 || !isset($goods['data'])) {
            if ($goods['result'] == 100026 || $goods['error_msg'] == '页数不合法') {
                return [];
            }
            Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            return [];
        } elseif ($goods['data']['currentPageNumber'] < $goods['data']['totalPage']) {
            $this->hasNext = true;
        }
        return $goods['data'];
    }


    /**
     * 发送获取商品列表请求
     * @param  array  $goodsId
     * @return mixed
     * @throws ApiException
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        Log::info('ks_goods_search_by_goods_id', $goodsId);
        $client = KsClient::newInstance($this->getAccessToken());
        $res=$paramsArr=[];
        foreach ($goodsId as $datum) {
            $params = [
                'kwaiItemId' => $datum
            ];
            $paramsArr[] = $params;
        }
        $curlResults= $client->executeAsync('/open/item/list/get', $paramsArr);

        $result = [];
        foreach($curlResults as $curlResult) {
            if($curlResult->isSuccess()){
                Log::info('ks_goods_search_by_goods_id_result', [$curlResult]);
                $result[] = $curlResult->arrayGet('data.items')[0];
            }else{
                Log::error('获取商品列表接口失败!', [$curlResult]);
            }
        }

        $res['items'] = $result;
        return $res;
    }

    /**
     * @param  GoodsSearchRequest  $request
     * @return GoodsSearchResponse
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $response = new GoodsSearchResponse();
        $pageSize = $request->pageSize;
        $currentPage = $request->currentPage;
        $numIid = $request->numIid;
        $client = KsClient::newInstance($this->getAccessToken());
        $params = [
//            'sellerId' => $this->getShopId(),
            'pageSize' => $pageSize,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => $currentPage,
        ];
        if (!empty($numIid)) {
            $params['kwaiItemId'] = $numIid;
        }
        Log::info('ks_goods_search_params', [$params]);
        $goods = $client->execute('post', '/open/item/list/get', $params);

        if (isset($goods['data']['totalItemCount'])) {
            $response->total = $goods['data']['totalItemCount'];
        } else {
            $response->total = 0;
        }


        KsClient::handleErrorCode($goods);

//        \Log::info('ks_goods_result', [$goods]);
        if ($goods['result'] != 1 || !isset($goods['data'])) {
            if ($goods['result'] == 100026 || $goods['error_msg'] == '页数不合法') {
                $response->hasNext = false;
                $response->data = [];
            }
            Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            $response->data = [];
            $response->hasNext = false;
            return $response;
        } elseif ($goods['data']['currentPageNumber'] < $goods['data']['totalPage']) {
            $response->hasNext = true;
        }
        $response->data = $goods['data'];
//        return $goods['data'];

        return $response;
    }
}
