<?php

namespace App\Services\Goods\impl;

use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\AlbbClient;
use App\Services\Goods\AbstractGoodsService;
use App\Utils\DateTimeUtil;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

/**
 * 1688商品服务实现类
 */
class AlbbGoodsServiceImpl extends AbstractGoodsService
{

    protected $platformType = PlatformConst::PLATFORM_TYPE_ALBB;
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            if (!empty($good)) {
                $list[] = $this->formatToGood($good);
            }
        }

        return $list;
    }

    function formatToGood(array $goods): array
    {
        $skus = [];
        //商品可能没有SKU
        foreach ($goods['skuInfos']??[] as $index => $item) {
            $skuValue = '';
            $skuImage = '';
            foreach ($item['attributes'] as $attribute) {
                $skuValue .= $attribute['attributeValue'];
                if(empty($skuImage)){
                    $skuImage = $attribute['skuImageUrl']??'';
                }
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => strval($item['skuId']),
                "sku_value" => $skuValue,
                "sku_pic" =>self::fullImageUrl($skuImage),
                "is_onsale" => 0,
            ];
        }

//        $onsaleArr = [Goods::IS_ONSALE_YES, Goods::IS_ONSALE_NO, Goods::IS_ONSALE_NO];
        $images = array_get($goods, 'image.images');
        return [
            "type" => $this->platformType,
            'num_iid' =>strval( $goods['productID']),
            'goods_title' => $goods['subject'],
            'goods_pic' => self::fullImageUrl(array_shift($images)),
            'is_onsale' => $goods['status'] == 'published'? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => DateTimeUtil::fromDateTimeTimezone($goods['createTime'])->toDateTimeString(),
            'goods_updated_at' => DateTimeUtil::fromDateTimeTimezone($goods['lastUpdateTime'])->toDateTimeString(),
            'skus' => $skus
        ];
    }

    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    /**
     * 组装图片完整URL
     * @param string $imageUrl
     * @return string
     */
    public static function fullImageUrl(string $imageUrl): string{
        return 'https://cbu01.alicdn.com/'.$imageUrl;
    }

    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $params = [
            'pageNo' => 1,
            'pageSize' => sizeof($goodsId)
        ];
        $params['productIds']=json_encode($goodsId);
        $client = $this->getClient();
        $method = 'com.alibaba.product:alibaba.product.list.get';
        $result = $client->execute($method, $params);
        $client::handleResp($result);
        return array_get($result,'result.pageResult.resultList',[]);

    }


    protected function getClient(): AlbbClient
    {
        return AlbbClient::newInstance($this->getAccessToken());
    }

    /**
     * 搜索商品
     * @param GoodsSearchRequest $request
     * @return GoodsSearchResponse
     * @throws ApiException
     * @throws OrderException
     * @throws GuzzleException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $goodsSearchResponse=new GoodsSearchResponse();

        $params = [
            'pageNo' => $request->currentPage,
            'pageSize' => $request->pageSize,

        ];
        if($request->goodsTitle){
            $params['subjectKey'] = $request->goodsTitle;
        }
        if($request->numIid){
            $params['productIds']=json_encode([$request->numIid]);
        }

        $client = $this->getClient();
        $method = 'com.alibaba.product:alibaba.product.list.get';
        $result = $client->execute($method, $params);
        $client::handleResp($result);
        $goodsSearchResponse->total=array_get($result,'result.pageResult.totalRecords',0);
        $goodsSearchResponse->data=array_get($result,'result.pageResult.resultList',[]);
        if(sizeof($goodsSearchResponse->data) == $request->pageSize){
            $goodsSearchResponse->hasNext=true;
        }

        return $goodsSearchResponse;

    }


}
