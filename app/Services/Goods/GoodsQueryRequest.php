<?php

namespace App\Services\Goods;

/**
 * 商品查询
 */
class GoodsQueryRequest
{
    public $ownerIdList = [];
    public $beginAt;
    public $endAt;
    public $timeField;
    public $includeAllGoods;
    public $includeLocked;
    public $order_status;
    public $refund_status;

    /**
     * @param array $ownerIdList
     * @param $beginAt
     * @param $endAt
     * @param $timeField
     * @param $includeAllGoods
     * @param $includeLocked
     * @param $order_status
     * @param $refund_status
     */
    public function __construct(array $ownerIdList, $beginAt, $endAt, $timeField, $includeAllGoods, $includeLocked,
                                      $order_status,$refund_status)
    {
        $this->ownerIdList = $ownerIdList;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
        $this->timeField = $timeField;
        $this->includeAllGoods = $includeAllGoods;
        $this->includeLocked = $includeLocked;
        $this->order_status=$order_status;
        $this->refund_status=$refund_status;


    }

}
