<?php

namespace App\Services\Goods;

use App\Models\Goods;
use App\Models\Fix\Order;
use App\Models\Fix\OrderItem;
use App\Models\Shop;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use function Clue\StreamFilter\fun;

/**
 * 商品查询请求
 */




/**
 * 待发货的Sku信息
 */
//class ToDeliverySkuInfo{
//    public
//}


/**
 * 商品查询服务
 */
class GoodsQueryService
{
    /**
     * 查询待发货的商品的数量
     * @param GoodsQueryRequest $request
     * @return array
     */
    public function queryToDeliverySkuCount(GoodsQueryRequest $request): array
    {
        $ownerIdList = $request->ownerIdList;
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $shopIds = collect($shops)->pluck('id')->toArray();
        Log::info("shopIds=", $shopIds);
        $timeField = $request->timeField;
        $beginAt = $request->beginAt;
        $endAt = $request->endAt;
        $condition = [];
        $orderStatus = $request->order_status;
        $itemOrderStatus=OrderUtil::mappingOrderStatus2OrderItemStatus($orderStatus);
        $refundStatus = $request->refund_status;
        $itemRefundStatus=OrderUtil::mappingOrderRefundStatus2OrderItemStatus($refundStatus);
        if ($timeField && $beginAt && $endAt) {
            $condition[] = ['orders.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $condition[] = ['orders.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        //订单状态，未发货，已发货
        $query = Order::query()
            ->whereIn('orders.shop_id', $shopIds)
            ->when(!empty($orderStatus),function($query) use($orderStatus) {
                $query->whereIn('orders.order_status', $orderStatus);
            })
            ->when(!empty($refundStatus),function ($query) use($refundStatus){
                $query->whereIn('orders.refund_status', $refundStatus);
            })
            ->where($condition);
        //是否显示锁定订单,是否含锁定
        if (!$request->includeLocked) {
            $query->whereNull('orders.locked_at');
        }
        $maxLimit = 5000; //订单筛出规格最大限制数量
        $list = $query->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->when(!empty($itemOrderStatus),function ($query) use($itemOrderStatus){
                $query->whereIn('order_items.status', $itemOrderStatus);
            })
            ->when(!empty($itemRefundStatus),function ($query) use($itemRefundStatus){
                $query->whereIn('order_items.refund_status',$itemRefundStatus);
            })
            ->whereIn('order_items.shop_id', $shopIds)
            ->groupBy(['shop_id','sku_id'])
            ->orderBy('to_shipped_count', 'desc')
            ->limit($maxLimit)
            ->get([
                'order_items.sku_id',
                'order_items.shop_id',
                DB::raw('max(order_items.id) as latest_order_item_id'),  //最大的订单项id取出来
                DB::raw('max(order_items.sku_value) as sku_value'),
                DB::raw('max(order_items.num_iid) as num_iid'),
                DB::raw('max(order_items.goods_title) as goods_title'),
                DB::raw('max(order_items.goods_pic) as goods_pic '),
                DB::raw('max(order_items.outer_iid) as outer_iid '),
                DB::raw('count(*) as to_shipped_count')
            ])->toArray();
        //查询最后一笔订单的商品名称，图片，把最大的一笔订单项ID取出来，然后再到订单项表里面查询
//        //再把查询的结果合并到list里面
//        $latestOrderItems = OrderItem::query()->whereIn('id', array_column($list, 'latest_order_item_id'))->get([
//            'id',
//            'goods_title',
//            'goods_pic',
//            'sku_value',
//            'num_iid'
//        ])->toArray()->keyBy('id')->toArray();
//        foreach ($list as &$item) {
//            $item['goods_title'] = $latestOrderItems[$item['latest_order_item_id']]['goods_title'];
//            $item['goods_pic'] = $latestOrderItems[$item['latest_order_item_id']]['goods_pic'];
//            $item['sku_value'] = $latestOrderItems[$item['latest_order_item_id']]['sku_value'];
//            $item['num_iid'] = $latestOrderItems[$item['latest_order_item_id']]['num_iid'];
//        }



        log::info("queryGoodsName ", ["sql"=>$query->toSql(),'bindings'=>$query->getBindings()]);
        return $list;
    }


    /**
     * 查询商品订购信息
     * 需要考虑几种case
     * case1: 商品下降了，但订单还有
     * case2：商品在，sku下架或者删除了，订单还有
     * case3：商品和sku都在的情况
     * @param GoodsQueryRequest $request
     * @return array
     */
    public function queryGoodsName(GoodsQueryRequest $request): array
    {
        Log::info("onSaleGoodsList.begin", []);
        $ownerIdList = $request->ownerIdList;
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $shopIds = collect($shops)->pluck('id')->toArray();
        $toDeliverySkuList = $this->queryToDeliverySkuCount($request);

        $onSaleGoodsList = $this->queryOnSaleGoods($shopIds);
        $onSaleGoodsListKeyByNumIid=$onSaleGoodsList->keyBy('num_iid');
        Log::info("获取在售商品信息");
//        \log::info("onSaleGoodsList=", [$onSaleGoodsList]);
//        \log::info("toDeliverySkuList=", $toDeliverySkuList);
        $arr = [];
        //提取出在售商品的商品ID
        $onSaleNumIids = $onSaleGoodsList->map(function ($good) {
            return $good['num_iid'];
        })->all();
        //in_array $onSaleNumIids 性能太差，所以用数组翻转的方式
        $onSaleNumIidsFlipped=array_flip($onSaleNumIids);
        Log::info("onSaleNumIids.count", [count($onSaleNumIids)]);
        //提取出订单商品的商品ID
        $toDeliveryNumIids = array_unique(array_column($toDeliverySkuList, "num_iid"));
        Log::info("toDeliveryNumIids.count", [count($toDeliveryNumIids)]);
        //合并在售商品和订单商品
        $allNumIids = array_unique(array_merge($onSaleNumIids, $toDeliveryNumIids));
        Log::info("allNumIids.count", [count($allNumIids)]);
        //所有在售的SKU
        $toDeliverySkuListIndexBySkuId = array_column($toDeliverySkuList, null, 'sku_id');
        $toDeliverySkuListGroupByNumIid=ArrayUtil::array_group_by($toDeliverySkuList, 'num_iid');
        Log::info("toDeliverySkuListGroupByNumIid.count", [count($toDeliverySkuListGroupByNumIid)]);

        Log::info("排序在售Sku");
        $result = [];
        foreach ($allNumIids as $num_iid) {
//            \Log::info("num_iid". $num_iid);
            //获取商品的在售sku信息
            $onDeliveryNumIidSkus=$toDeliverySkuListGroupByNumIid[$num_iid]??[];
            $temp = [];
            $toShippedCount = 0;
//            \log::info("exists",[$num_iid, $onSaleNumIids,in_array($num_iid, $onSaleNumIids)]);
            //if (in_array($num_iid, $onSaleNumIids)) {
            //in_array 的方式性能太差所以反转
            if (array_key_exists($num_iid, $onSaleNumIidsFlipped)) {
//                foreach ($onSaleGoodsList as $tempGoods) {
//                    if ($tempGoods['num_iid'] === $num_iid) {
//                        $goods = $tempGoods;
//                        break;
//                    }
//                }
                $goods=$onSaleGoodsListKeyByNumIid->get($num_iid);
//                \log::info("获取商品信息");
                foreach ($goods->skus as $sku) {
                    $tempToShippedCount = $toDeliverySkuListIndexBySkuId[$sku['sku_id']]['to_shipped_count'] ?? 0;
                    $temp[] = [
                        'label' => $sku['custom_sku_value'] ? $sku['custom_sku_value'] : ($sku['sku_value'] ?? ''),
                        'sku_value' => $sku['sku_value'] ?? '',
                        'shop_id' => $sku['shop_id'] ,
                        'sku_id' => $sku['sku_id'] ?? '',
                        'custom_sku_value' => $sku['custom_sku_value'] ?? '',
                        'outer_sku_id' => $sku['outer_id'] ?? '',
                        'goods_pic' => $sku['sku_pic'] ?? ($toDeliverySkuListIndexBySkuId[$sku['sku_id']]['goods_pic'] ?? ''),
                        'to_shipped_count' => $tempToShippedCount
                    ];
                    $toShippedCount += $tempToShippedCount;
                }
                $tempIndexBySkuId = array_column($temp, null, 'sku_id');

                foreach ($onDeliveryNumIidSkus as $sku) {
//                    log::info("todelivery ",[$num_iid,$tempIndexBySkuId,$sku['sku_id']]);
                    if (!array_key_exists($sku['sku_id'], $tempIndexBySkuId)) {
                        $temp[] = [
                            'label' => $sku['sku_value'] ?? '',
                            'sku_value' => $sku['sku_value'] ?? '',
                            'shop_id' => $sku['shop_id'] ,
                            'sku_id' => $sku['sku_id'] ?? '',
                            'custom_sku_value' => $sku['custom_sku_value'] ?? '',
                            'outer_sku_id' => $sku['outer_id'] ?? '',
                            'goods_pic' => $sku['goods_pic'],
                            'to_shipped_count' => $sku['to_shipped_count']
                        ];
                        $toShippedCount += $sku['to_shipped_count'];
                    }
                }
                $result[] = [
                    'label' => $goods['custom_title'] ? $goods['custom_title'] : ($goods['goods_title'] ?? ''),
                    'goods_title' => $goods['goods_title'] ?? '',
                    'custom_title' => $goods['custom_title'] ?? '',
                    'shop_id' => $goods['shop_id'],
                    'outer_goods_id' => $goods['outer_goods_id'] ? $goods['outer_goods_id'] : '',
                    'children' => $temp,
                    'to_shipped_count' => $toShippedCount,
                    'goods_id' => $num_iid ?? '',
                    'goods_pic' => $goods['goods_pic'],
                ];
            } else {
                foreach ($onDeliveryNumIidSkus as $sku) {

                    $temp[] = [
                        'label' => $sku['sku_value'] ?? '',
                        'sku_value' => $sku['sku_value'] ?? '',
                        'custom_sku_value' => '',
                        'sku_id' => $sku['sku_id'] ?? '',
                        'outer_sku_id' => $sku['outer_sku_id'] ?? '',
                        'goods_pic' => $sku['goods_pic'],
                        'to_shipped_count' => $sku['to_shipped_count']
                    ];
                    $toShippedCount += $sku['to_shipped_count'];
                }
//                \log::info("onDeliveryNumIidSkus",[$onDeliveryNumIidSkus,$num_iid]);
                $result[] = [
                    'label' => $onDeliveryNumIidSkus[0]['goods_title'] ?? '',
                    'goods_title' => $onDeliveryNumIidSkus[0]['goods_title'] ?? '',
                    'shop_id' => $onDeliveryNumIidSkus[0]['shop_id'],
                    'custom_title' => '',
                    'outer_goods_id' => $onDeliveryNumIidSkus[0]['outer_iid']??'',
                    'children' => $temp,
                    'to_shipped_count' => $toShippedCount,
                    'goods_id' => $num_iid,
                    'goods_pic' => $onDeliveryNumIidSkus[0]['goods_pic']
                ];
            }

        }
        Log::info("begin sort", []);
        $num = array_column($result, 'to_shipped_count');
        array_multisort($num, SORT_DESC, $result);
        Log::info("end sort", []);
        //是否显示全部商品
        if (!$request->includeAllGoods) {
            foreach ($result as $key => $value) {
                if ($value['to_shipped_count'] > 0) {
                    foreach ($value['children'] as $k => $v) {
                        if ($v['to_shipped_count'] <= 0) {
                            unset($result[$key]['children'][$k]);
                        }
                    }
                    $result[$key]['children'] = array_values($result[$key]['children']);
                } else {
                    unset($result[$key]);
                }
            }
            $result = array_values($result);
        }
        return $result;
    }

    /**
     * @param array $shopIds
     * @return Builder[]|Collection
     */
    public function queryOnSaleGoods(array $shopIds)
    {
        return Goods::query()->with(['skus'])
//            ->where('is_onsale', Goods::IS_ONSALE_YES)
            ->whereIn('shop_id', $shopIds)
            ->get();
    }

    public function buildPickingGoodsQuery(PickingGoodsRequest $request)
    {
        $condition = [];
        $shopIds = Shop::shopIdsByidentifier($request->ownerIdList, $request->currentShopId);
        Log::info("shopIds=,request=", [$shopIds, $request]);

        if ($request->timeField && $request->beginAt && $request->endAt) {
            $condition[] = ['orders.' . $request->timeField, '>=', date('Y-m-d H:i:s', strtotime($request->beginAt))];
            $condition[] = ['orders.' . $request->timeField, '<=', date('Y-m-d H:i:s', strtotime($request->endAt))];
        }

        $query = Order::query()
            ->whereIn('orders.shop_id', $shopIds)
            ->where($condition);

        if (isset($request->lockedStatus) && $request->lockedStatus != -1) {
            switch ($request->lockedStatus) {
                case 0:
                    //未锁定订单
                    $query->whereNull('locked_at');
                    break;
                case 1:
                    //已锁定订单
                    $query->whereNotNull('locked_at');
                    break;
                default:
                    $query->whereNull('locked_at');
                    break;
            }
        }

        if (isset($request->orderStatus) && $request->orderStatus != -1) {
            $orderStatusArr = Order::orderStatusFrontMap($request->orderStatus);
            if (Order::ORDER_STATUS_PAYMENT == $request->orderStatus) {
                $query->whereIN('order_items.status', [0, Order::ORDER_STATUS_PAYMENT]);
            } elseif (Order::ORDER_STATUS_DELIVERED == $request->orderStatus) {
                $query->whereIn('order_items.status', [0, Order::ORDER_STATUS_DELIVERED]);
            }
            $query->whereIn('orders.order_status', $orderStatusArr);
        }

        if (isset($request->printStatus) && $request->printStatus != -1) {
            $printStatusArr = Order::printStatusFrontMap($request->printStatus);
            $query->whereIn('orders.print_status', $printStatusArr);
        }

        if ($request->refundStatus == 0) {
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART]);
        } else if ($request->refundStatus > 0) {
            $query->where('orders.refund_status', Order::REFUND_STATUS_YES)
                ->whereHas('orderItem', function ($query) use ($request) {
                    if ($request->refundStatus == 1) {
                        $query->whereIn('refund_status', [OrderItem::REFUND_STATUS_REFUNDING, OrderItem::REFUND_STATUS_WAIT_SELLER, OrderItem::REFUND_STATUS_WAIT_SELLER]);
                    } else if ($request->refundStatus == 2) {
                        $query->where('refund_status', OrderItem::REFUND_STATUS_SUCCESS);
                    } else {
                        $query->where('refund_created_at', '>=', date('Y-m-d H:i:s', strtotime('-1 day')));
                    }
                });
        }

        if ($request->keyword) {
            $query = $query->where(function ($query) use ($request) {
                $query->where('order_items.goods_title', 'like', "%{$request->keyword}%")
                    ->orWhere('order_items.outer_iid', $request->keyword)
                    //->orWhere('order_items.sku_value','like',"%{$keyword}%")
                    //->orWhere('order_items.sku_id', $keyword)
                    ->orWhere('order_items.num_iid', $request->keyword);
                //->orWhere('order_items.outer_sku_iid', $keyword);
            });
        }
        if ($request->skuKeyword) {
            $query = $query->where(function ($query) use ($request) {
                $query->where('order_items.sku_value', 'like', "%{$request->skuKeyword}%")
                    ->orWhere('order_items.sku_id', $request->skuKeyword)
                    ->orWhere('order_items.outer_sku_iid', $request->skuKeyword);
            });
        }
        if(isset($request->preSaled)){
            $query->where('orders.is_pre_sale', $request->preSaled);
        }
        if (!empty($request->skuValueList)) {
            $query->whereIn('order_items.sku_id', $request->skuValueList);
        }
        if (!empty($request->goodsId)) {
            $query->where('num_iid', $request->goodsId);
        }
        $query->leftJoin('order_items', 'orders.id', '=', 'order_items.order_id');

        return $query;
    }

    public function queryPickingGoods(PickingGoodsRequest $request)
    {
        $query = $this->buildPickingGoodsQuery($request);
        if ($request->sort != 'goodsnum desc') {
            $sortArea = explode(' ', $request->sort);
            $query->orderBy($sortArea[0], $sortArea[1]);
        }

        if (Environment::isWxOrWxsp()) {
            $query = $query->groupBy(['sku_id_sku_value_outer_sku_iid']);
        }else {
            $query = $query->groupBy(['sku_id']);
        }

        $list = $query->get([
            'order_items.sku_id',
            'order_items.sku_value',
            'order_items.num_iid',
            'order_items.goods_title',
            'order_items.goods_pic',
            'order_items.goods_price',
            'order_items.outer_iid',
            'order_items.outer_sku_iid',
            'order_items.shop_id',
            DB::raw('min(orders.pay_at) pay_at_min'),
//            DB::raw('sum(goods_num) goods_nums'),
           //如果有send_num>0，那么就是累计send_remain_num，否则就是累计goods_num
            DB::raw('sum(if(send_num>0,send_remain_num,goods_num)) goods_nums'),
            DB::raw('concat(order_items.sku_id, order_items.sku_value, order_items.outer_sku_iid) as sku_id_sku_value_outer_sku_iid'),
        ]);
        if (Environment::isWxOrWxsp()) {
            $list = $list->groupBy(function ($item){
                return $item['num_iid'] . $item['goods_title'];
            })->toArray();
        }else {
            $list = $list->groupBy('num_iid')->toArray();
        }

        Log::info("queryPickingGoods.sql", [$query->toSql()]);
        $arr = [];

        foreach ($list as $index => $item) {
            foreach ($item as $k => $v) {
                if ($v['shop_id'] == 2485 && in_array($v['sku_value'], ['规格', '规格值', '闪电购商品'])) {
                    $item[$k]['sku_value'] = "";
                }
            }
            $item = collect($item)->sortBy('sku_value')->values()->all();
            $arr[] = [
                'goods_pic' => $item[0]['goods_pic'],
                'goods_title' => $item[0]['goods_title'],
                'num_iid' => $item[0]['num_iid'],
                'shop_id' => $item[0]['shop_id'],
                'total' => array_sum(array_column($item, 'goods_nums')),
                'items' => $item,
                'pay_at_min' => $item[0]['pay_at_min']
            ];
        }
//        Log::info("queryPickingGoods.arr", $arr[0]);
        if ($request->sort == 'goodsnum desc') {
            $num = array_column($arr, 'total');
            array_multisort($num, SORT_DESC, $arr);
        }
        if (!empty($request->goodsSort)){
            $sortArea = explode(' ', $request->goodsSort);
            foreach ($arr as $index => $item) {
                $items = $item['items'];
                $num = array_column($items, $sortArea[0]);
                if ($sortArea[1] == 'asc') {
                    array_multisort($num, SORT_ASC, $items);
                }else{
                    array_multisort($num, SORT_DESC, $items);
                }
                $arr[$index]['items'] = array_values($items);
            }
        }
        $arr = collect($arr)->values()->slice($request->offset, $request->limit)->values()->all();

        $numIidArr = array_column($arr, 'num_iid');
        $goodsArr = Goods::query()->whereIn('num_iid', $numIidArr)->get(['num_iid', 'custom_title', 'flag', 'remark'])->toArray();
        $newGoodsArr = array_column($goodsArr, null, 'num_iid');
        $redis = redis('cache');
        foreach ($arr as $index => $item) {
            $arr[$index]['flag'] = '';
            $arr[$index]['remark'] = '';
            $arr[$index]['custom_title'] = '';
            if (array_key_exists($item['num_iid'], $newGoodsArr)) {
                $arr[$index]['flag'] = $newGoodsArr[$item['num_iid']]['flag'];
                $arr[$index]['remark'] = $newGoodsArr[$item['num_iid']]['remark'];
                $arr[$index]['custom_title'] = $newGoodsArr[$item['num_iid']]['custom_title'];
            } else {
                $flagKey = 'goods_flag_' . $item['num_iid'];
                $remarkKey = 'goods_remark_' . $item['num_iid'];
                if ($redis->exists($flagKey)) {
                    $arr[$index]['flag'] = $redis->get($flagKey);
                }
                if ($redis->exists($remarkKey)) {
                    $arr[$index]['remark'] = $redis->get($remarkKey);
                }
            }
        }
        $pagination = [
            'rows_found' => collect($list)->count(),
            'offset' => $request->offset,
            'limit' => $request->limit
        ];
        return ['pagination' => $pagination, $arr];
    }


    public function queryPickingGoodsSummary(PickingGoodsRequest $request)
    {
        $maxLimit = 1000;
        $query = $this->buildPickingGoodsQuery($request)->limit($maxLimit);
        log::info("queryPickingGoodsSummary sql=", [$query->toSql(), $query->getBindings()]);
        $orderList = (clone $query)->get();
        log::info("orderlist");
        $orderCountQuery = clone $query;
        $orderCount = $orderCountQuery->groupBy('address_md5')->get();
        log::info("orderCount sql", [$orderCountQuery->toSql()]);
        $goodsListQuery = clone $query;
        $goodsList = $goodsListQuery->groupBy(['order_items.num_iid'])->get()->count();
        log::info("goodlist sql", [$goodsListQuery->toSql()]);
        $skuListQuery = clone $query;
        $skuList = $skuListQuery->groupBy(['order_items.sku_id'])->get()->count();
        log::info("skulist sql", [$skuListQuery->toSql()]);

        $orderCount = collect($orderCount)->count();
        if ($orderCount < $maxLimit) {
            $ret = [
                'orderNum' => $orderCount,
                'goodsNum' => $goodsList,
                'skuNum' => $skuList,
                'totalCount' => collect($orderList)->sum('num'),
                'totalPayment' => collect($orderList)->sum('payment'),
            ];
        } else {
            if ($goodsList > $maxLimit) {
                $goodsList = $goodsList . '+';
            }

            if ($skuList >= $maxLimit) {
                $skuList = $skuList . '+';
            }

            $ret = [
                'orderNum' => $orderCount . '+',
                'goodsNum' => $goodsList,
                'skuNum' => $skuList,
                'totalCount' => collect($orderList)->sum('num') . '+',
            ];
        }

        return $ret;
    }

    /**
     *  根据指定的字段进行合并
     * @param $ret
     * @param string[] $groupByColumns
     * @return array
     */
    public function handleMerge($ret, $groupByColumns = ['outer_goods_id'])
    {
        if (empty($groupByColumns)) {
            return $ret;
        }

        $groupArr = collect($ret)->groupBy(function ($item, $key) use ($groupByColumns) {
            $groupByString = "";
            foreach ($groupByColumns as $column) {
                $columnValue = $item[$column] ?? null;
                if ($columnValue) {
                    $groupByString = $groupByString . "-" . $columnValue;
                } else {
                    $groupByString = $groupByString . "-" . str_random();
                }
            }
            return $groupByString;
        })->toArray();

        $list = [];

        foreach ($groupArr as $groupItem) {
            $skus = [];
            $idArr = [];
            foreach ($groupItem as $goods) {
                $skus =array_merge($skus, $goods['skus']);
                $idArr[] = $goods['id'];
            }
            $groupItem[0]['id'] = $idArr;
            $groupItem[0]['skus'] = $skus;
            $list[] = $groupItem[0];
        }

//        $groupArr = collect($ret)->groupBy(['goods_title', 'outer_goods_id'])->toArray();
//
//        $list = [];
//        foreach ($groupArr as $goodsTitleArr) {
//            foreach ($goodsTitleArr as $groupItem) {
//                $idArr = [];
//                $skus = [];
//                foreach ($groupItem as $item) {
//                    $idArr[] = $item['id'];
//                    foreach ($item['skus'] as $v) {
//                        $skus[] = $v;
//                    }
//                }
//                $groupItem[0]['id'] = $idArr;
//                $groupItem[0]['skus'] = $skus;
//                $list[] = $groupItem[0];
//            }
        return $list;
    }
}



