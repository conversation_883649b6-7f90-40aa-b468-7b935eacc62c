<?php

namespace App\Services\Goods;

use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\Commodity;
use App\Models\CommoditySku;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * 通用的商品服务
 */
class CommodityService
{
    protected  $goodsService;

    /**
     * @param  GoodsService $goodsService
     */
    public function __construct(GoodsService  $goodsService)
    {
        $this->goodsService = $goodsService;
    }

    /**
     * 根据店铺ID获取全部的商品SKU
     * @param $shopId
     * @return Builder[]|Collection
     */
    public function findManyCommoditySkusByShopId($shopId){
        return CommoditySku::query()->where('shop_id',$shopId)->get();
    }
    /**
     * 保存商品SKU，如果没有平台的商品可以级联创建
     * @param SaveCommoditySkuRequest $saveCommoditySkuRequest
     * @return void
     * @throws ErrorCodeException
     */
    public function saveCommoditySku(SaveCommoditySkuRequest  $saveCommoditySkuRequest){
        if(!$saveCommoditySkuRequest->commoditySkuId&&!$saveCommoditySkuRequest->commoditySkuOuterId){
            throw_error_code_exception([StatusCode::PARAM_ERROR,"缺少商品SKU ID或外部ID"]);
        }

        $commodity=null;
        if($saveCommoditySkuRequest->commodityId){
            $commodity=$this->findCommodityById($saveCommoditySkuRequest->commodityId);
        }
        if(!$commodity&&$saveCommoditySkuRequest->commodityOutId){
            $commodity=$this->findCommodityByOuterId($saveCommoditySkuRequest->commodityOutId);
        }
        //因为小红书没有商品ID或者外部商品ID，所以需要根据SKU ID或者外部SKU ID来获取商品信息
        if(!$commodity&&$saveCommoditySkuRequest->commoditySkuId){
            $commodity=$this->findCommodityBySkuId($saveCommoditySkuRequest->commoditySkuId);
        }
        if(!$commodity&&$saveCommoditySkuRequest->commoditySkuOuterId){
            $commodity=$this->findCommodityBySkuOuterId($saveCommoditySkuRequest->commoditySkuOuterId);
        }
        if(!$commodity&&$saveCommoditySkuRequest->commoditySkuOuterId){

            $goods = $this->goodsService->findGoodsBySkuId($saveCommoditySkuRequest->commoditySkuOuterId);
            \Log::info("没有Commodity信息，通过外部SKU ID创建商品",["commoditySkuOuterId"=>$saveCommoditySkuRequest->commoditySkuOuterId,"goods"=>$goods]);

            if($goods){
                $commodity=$this->findCommodityByOuterId($goods->num_iid);
                if(!$commodity) {
                    $commodity = Commodity::create([
                        "shop_id" => $saveCommoditySkuRequest->shopId,
                        "type" => $saveCommoditySkuRequest->type,
                        "name" => $goods->goods_title,
                        "platform_out_id" => $goods->num_iid
                    ]);
                    \Log::info("创建商品成功", ["commodity" => $commodity]);
                }
                //如果匹配到goods用goods里面的信息更新商品信息
                $saveCommoditySkuRequest->commodityName=$goods->goods_title;
            }else{
                throw_error_code_exception([StatusCode::PARAM_ERROR,"无法获取到商品信息，请先同步商品信息"]);
            }
        }


        //因为发货对账需要用到商品的主图等信息，所以需要能够获取商品信息
        if(!$commodity){
            throw_error_code_exception([StatusCode::PARAM_ERROR,"商品不存在"]);
        }

        $commodityArr = [
            "shop_id" => $saveCommoditySkuRequest->shopId,
            "type" => $saveCommoditySkuRequest->type,

        ];
        if($saveCommoditySkuRequest->commodityName){
            $commodityArr["name" ]= $saveCommoditySkuRequest->commodityName;
        };
        if($saveCommoditySkuRequest->commodityOutId){
            $commodityArr[ "platform_out_id"]= $saveCommoditySkuRequest->commodityOutId;
        };
        if(!$commodity){
            $commodity=new Commodity();
            $commodity->create($commodityArr);
        }else{
            $commodity->update($commodityArr);
        }
        if(!$commodity){
            \Log::info("商品创建失败",$commodityArr);
            throw_error_code_exception([StatusCode::PARAM_ERROR,"商品创建失败"]);
        }

        $commoditySku=null;
        if($saveCommoditySkuRequest->commoditySkuId){
            $commoditySku=$this->findCommoditySkuById($saveCommoditySkuRequest->commoditySkuId);
        }
        if(!$commoditySku){
            $commoditySku=$this->findCommoditySkuByOuterId($saveCommoditySkuRequest->commoditySkuOuterId);
        }
        $commoditySkuArr = [
            "shop_id" => $saveCommoditySkuRequest->shopId,
            "commodity_id"=>$commodity->id,
            "type" => $saveCommoditySkuRequest->type,

        ];
        if($saveCommoditySkuRequest->commoditySkuName){
            $commoditySkuArr["name"]=$saveCommoditySkuRequest->commoditySkuName;
        }
        if($saveCommoditySkuRequest->commoditySkuOuterId){
            $commoditySkuArr ["platform_sku_out_id"]= $saveCommoditySkuRequest->commoditySkuOuterId;
        }
        if($saveCommoditySkuRequest->commodityOutId){
            $commoditySkuArr["platform_commodity_out_id"]=$saveCommoditySkuRequest->commodityOutId;
        }
        if($saveCommoditySkuRequest->weight){
            $commoditySkuArr["weight"]=$saveCommoditySkuRequest->weight;
        }else{
            $commoditySkuArr["weight"]=null;
        }
        if($saveCommoditySkuRequest->netWeight){
            $commoditySkuArr["net_weight"]=$saveCommoditySkuRequest->netWeight;
        }else{
            $commoditySkuArr["net_weight"]=null;
        }
        if($saveCommoditySkuRequest->settlementPrice){
            $commoditySkuArr[ "settlement_price"]=$saveCommoditySkuRequest->settlementPrice;
        }else{
            $commoditySkuArr[ "settlement_price"]=null;
        }
        \Log::info('更新Sku',$commoditySkuArr);
        if($commoditySku){
            $commoditySku->update($commoditySkuArr);
        }else{
            $commoditySku=CommoditySku::create($commoditySkuArr);
        }

    }

    /**
     * 通过ID查找Commodity
     * @param int $id
     * @return Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function findCommodityById(int $id){
        return Commodity::query()->where('id',$id)->first();
    }

    /**
     * 通过外部ID查找Commodity
     * @param string $outerId
     * @return Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function findCommodityByOuterId(string $outerId){
        return Commodity::query()->where('platform_out_id',$outerId)->first();
    }


    /**
     * 通过ID查找CommoditySku
     * @param int $id
     * @return Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public  function findCommoditySkuById(int $id){
        return CommoditySku::query()->where('id',$id)->first();
    }

    public function findCommoditySkuByOuterId(string $outerId){
        return CommoditySku::query()->where('platform_sku_out_id',$outerId)->first();
    }

    /**
     * 通过商品SKUId获取商品，
     * @param int|null $commoditySkuId
     * @return mixed|null
     */
    private function findCommodityBySkuId(?int $commoditySkuId)
    {
       $commoditySku=$this->findCommoditySkuById($commoditySkuId);
       if($commoditySku){
           return $commoditySku->commodity;
       }
       return null;
    }

    private function findCommodityBySkuOuterId(?string $commoditySkuOuterId)
    {
        $commoditySku=$this->findCommoditySkuByOuterId($commoditySkuOuterId);
        if($commoditySku){
            return $commoditySku->commodity;
        }
        return null;
    }

}
