<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/6
 * Time: 22:28
 */

namespace App\Services\Bo;

use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Utils\ArrayUtil;
use Illuminate\Support\Facades\Log;

/**
 * 请求打印的 pack
 */
class PrintPackBo extends BaseBo
{
    public $index = '';
    public $request_id = '';
    public $package_id = 0;
    public $order_infos = [];
    /**
     * 主订单的信息
     * @var Order|CustomizeOrder $master_order_info
     */
    public $master_order_info = null;
    /**
     * @var PrintOrderItemBo[] 子订单列表
     */
    public $print_order_item_bo_list = [];

    /**
     * 返回合单的主订单的订单ID,如果是平台订单就返回
     * @return string
     */
    public function getMasterOrderId():string {

        $masterOrder = $this->master_order_info;

        $orderArray = $masterOrder->toArray();
        $isPlatformOrder = !empty($orderArray['tid']);
        if (!$isPlatformOrder) { // 自由打印
            if (!empty($orderArray['order_no'])) {
                $orderId = $orderArray['order_no'];
            } else {
                $orderId = $orderArray['id'];
            }
        } else {
            $orderId = $orderArray['tid'];
        }
        return $orderId;
    }

    /**
     * 返回订单的Code（视频号专用）
     * @return string|null
     */

    public function getMasterOrderCode():?string{
        $orderArray = $this->master_order_info->toArray();
        return ArrayUtil::getArrayValue($orderArray,'order_code');
    }
    /**
     * 返回主订单的oaid
     * @return string|null
     */
    public function getMasterOaid():?string {
        $masterOrder = $this->master_order_info;
        $isPlatformOrder = !empty($masterOrder->tid);
        if($masterOrder&&$isPlatformOrder) {
            $orderCipherInfo = $masterOrder->orderCipherInfo;
//            Log::info('orderCipherInfo',[$orderCipherInfo]);
            return $orderCipherInfo->oaid;

        }
        return null;
    }

    /**
     * 返回主订单,可能是Order或者CustomizeOrder
     * @return Order|CustomizeOrder
     */
    public function getMasterOrder(){
        return $this->master_order_info;
    }

    /**
     * 返回订单的数组信息
     * @return array
     */
    public function getMasterOrderArray(): array
    {
        return $this->getMasterOrder()->toArray();
    }


    /**
     * 返回是不是平台订单
     * @return bool
     */
    public function isPlatformOrder(): bool
    {
        $order = $this->master_order_info->toArray();
        return !empty($order['tid']) || !empty($order['order_no']);
    }

    /**
     * 返回所有的订单号
     * @return string[];
     */
    public function getOrderIds():array{
        $order = $this->master_order_info->toArray();
        if($this->isPlatformOrder()){
           return [strval($order['tid'])];
       }else{
           return [strval($order['id'])];
       }

    }

    /**
     * 获取订单支付金额合计，用于报价等等
     * @return string
     */
    public function getOrderAmountSum(): string
    {
        $totalFeeAmount="0";
        foreach ($this->print_order_item_bo_list as $printOrderItemBo) {
            $orderItem = $printOrderItemBo->order_item_info;
            if(!$orderItem){
                continue;
            }
            $totalFeeAmount=round_bcadd($totalFeeAmount, $orderItem->total_fee??$orderItem->payment??"0");
        }
       return $totalFeeAmount;

    }

    /**
     * 获取保价金额
     * @return string
     */
    public function getInsureAmount():string {

        $orderAmountSum = $this->getOrderAmountSum();
        return round_bcadd($orderAmountSum,"0");

    }

    public function getUserId():string{
        return strval($this->master_order_info->userId??0);
    }

}
