<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/1/6
 * Time: 22:54
 */

namespace App\Services\Bo;

use App\Services\Traits\ErrorInfoTrait;

/**
 * 电子面单打印数据
 */
class WaybillsPrintDataBo extends BaseBo
{
    use ErrorInfoTrait;

    public $package_id = 0;
    public $waybill_code = '';
    /**
     * @var string 电子面单ID(微信视频号用到)
     */
    public $platform_waybill_id;
    public $sign = '';
    public $order_id = '';
    public $encrypted_data = '';
    public $parent_waybill_code = '';
    public $param_str = '';
    //taobao
    public $templateURL;
    //PDD用
    public $version= '';
    //PDD用
    public $userId= '';

    public function copyByPrintDataPackBo(PrintDataPackBo $printDataPackBo)
    {
        $this->package_id = $printDataPackBo->package_id;
        $this->waybill_code = $printDataPackBo->waybill_code;
    }


}
