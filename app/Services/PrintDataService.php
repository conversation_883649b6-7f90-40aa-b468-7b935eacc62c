<?php

namespace App\Services;

use App\Constants\TemplateURLConst;
use App\Models\Company;
use App\Models\Order;
use App\Models\Shop;
use App\Models\ShopExtra;
use App\Models\Template;
use App\Models\Waybill;
use App\Services\Bo\PrintOrderBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Client\DyClient;
use App\Services\Printing\DyPrintTemplate;
use App\Services\Printing\WxspPrintTemplate;
use App\Utils\Environment;
use Yansongda\Pay\Log;

class PrintDataService
{
    const CUSTOM_TO_PLATFORM_KEY
        = [
            'orderNum' => 'tid',
            'ownerName' => 'seller_nick',
            'buyerMemo' => 'buyer_message',
            'goodsId' => 'num_iid',
            'skuId' => 'sku_id',
            'productName' => 'custom_title',
            'payAmount' => 'payment',
            'totalPrice' => 'total_fee',
        ];
    const MERGE_TEMPLATE = 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/common_merge.xml';
    const PREFIX_GIFT = '【赠品】';

    public static function getItemName($templateShopId,$itemKey, $order, $orderItem, $contentsArr = [],
    $isCustomOrder = false,
                                       $index = 0, $packageNum = 0,int $packageNumSetting=0,int $packageIndex=1,$maxPackageSkuNum=1)
    {
        \Log::info("getItemName" ,["itemKey"=>$itemKey,"index"=>$index,"packageNumSetting"=>$packageNumSetting,"packageIndex"=>   $packageIndex,  "packageNum"=>$packageNum,"maxPackageSkuNum"=>$maxPackageSkuNum]);
        if (!$isCustomOrder) {
            $shop = Shop::find($order['shop_id'])->toArray();

            //给自定义模板字段赋值
            switch ($itemKey) {
                case 'confirmTime':
                    return date('Y-m-d H:i:s', strtotime($order['pay_at']));
                    break;
                case 'orderNum':
                    $key = self::CUSTOM_TO_PLATFORM_KEY[$itemKey];
                    return $order[$key];
                    break;
                case 'ownerName':
                    return $shop['shop_name'];
                    break;
                case 'buyerMemo':
                    $key = self::CUSTOM_TO_PLATFORM_KEY[$itemKey];
                    $unicodeChar = json_decode('"\\u2028"');
                    //                    \Log::info("输出备注",[$order[$key],$replacedStr]);
                    return str_replace($unicodeChar, '', $order[$key]);
                    break;
                case 'mallName':
                    return $order['seller_nick'] ?? $shop['shop_name'];
                    break;
                case 'count':
                    //return '共计：' . $order['num'] . '件';
                    return '共计：' . array_sum(array_column($order['order_item'], 'goods_num')) . '件';
                    break;
                case 'watermark':
                    //return $order['num'] . '件';
                    $goodsSum = array_sum(array_column($order['order_item'], 'goods_num'));
                    //这里改成按照最大sku数量打印
                    $skuTotalPages = 1;
                    \Log::info('按照最大sku数量打印',["goodsSum"=>$goodsSum,   "maxPackageSkuNum"=>$maxPackageSkuNum,"skuTotalPages"=>$skuTotalPages,"packageIndex"=>$packageIndex]);
                    return $goodsSum . '件';
                    break;
                case 'goodsId':
                    $key = self::CUSTOM_TO_PLATFORM_KEY[$itemKey];
                    return $orderItem[$key];
                    break;
                case 'skuId':
                    $key = self::CUSTOM_TO_PLATFORM_KEY[$itemKey];
                    return $orderItem[$key];
                    break;
                case 'productName':
                    $key = self::CUSTOM_TO_PLATFORM_KEY[$itemKey];
                    return "  " . $orderItem[$key];
                    break;
                case 'skuNum':
                    return '✖' . $orderItem['goods_num'];
                    break;
                case 'outerSkuId':
                    return $orderItem['outer_sku_iid'];
                    break;
                case 'outerId':
                    return $orderItem['outer_iid'];
                    break;
                case 'productStandard':
                    //$sku_desc = empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc'];
                    //$desc     = empty($orderItem['custom_sku_value']) ? $sku_desc : $orderItem['custom_sku_value'];
                    $desc = $orderItem['custom_order_sku_value'] ?? ($orderItem['custom_sku_value'] ?? (empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc']));
                    return $desc;
                    break;
                case 'money':
                    return $orderItem['payment'] . '元';
                    break;
                case 'totalPrice':
                    return '总价: ' . $order['total_fee'] . '元';
                    break;
                case 'payAmount':
                    return '总支付金额:' . $order['payment'] . '元';
                    break;
                case 'remark':
                    return is_array(json_decode($order['seller_memo'])) ? implode(',', json_decode($order['seller_memo'])) : $order['seller_memo'];
                    break;
                case 'standardAndSkuNum':
                    //$sku_desc = empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc'];
                    //$desc     = empty($orderItem['custom_sku_value']) ? $sku_desc : $orderItem['custom_sku_value'];
                    $desc = $orderItem['custom_order_sku_value'] ?? ($orderItem['custom_sku_value'] ?? (empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc']));
                    return $desc ? $desc . ',✖' . $orderItem['goods_num'] : $orderItem['sku_id'] . ',✖' . $orderItem['goods_num'];
                    break;
                case 'outerIdAndSkuNum':
                    return ($orderItem['outer_sku_iid'] ? $orderItem['outer_sku_iid'] : '') . ',✖' . $orderItem['goods_num'];
                    break;
                case 'allProductDetail':
                    //$sku_desc = empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc'];
                    //$desc     = empty($orderItem['custom_sku_value']) ? $sku_desc : $orderItem['custom_sku_value'];
                    $desc = $orderItem['custom_order_sku_value'] ?? ($orderItem['custom_sku_value'] ?? (empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc']));
                    return $orderItem['custom_title'] . ',' . $desc . ',✖' . $orderItem['goods_num'];
                    break;
                case 'numOfPackage':
                    return '第' . $index . '/' . $packageNum . '包裹';
                    break;
                case 'contents':
                    //如果前端有传过来 用传过来的；没有就现场生成
                    if (array_key_exists($order['id'], $contentsArr)) {
                        $itemName = $contentsArr[$order['id']];
                    } else {
                        $itemName = '';
                        //店铺打印内容配置
                        $shopId =$templateShopId>0?$templateShopId: \request()->auth->shop_id;
                        $shopExtra = ShopExtra::query()->where('shop_id', $shopId)->first();
                        if (empty($shopExtra->print_contents)) {
                            $shopPrintContents = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);
                        } else {
                            $shopPrintContents = json_decode($shopExtra->print_contents, true);
                        }
                        \Log::info('店铺打印内容配置' . json_encode($shopPrintContents));
                        //是否合并宝贝
                        if ($shopPrintContents['goodsMerge'] == '1') {
                            $mergeGoods = [];
                            foreach ($order['order_item'] as $key => $item) {
                                $mergeGoods[] = $item;
                            }

                            //先对sku进行分组，然后再把礼品订单放到最后
                            $newMergeGoods = self::sortMergeOrderItemGroupGiftLast(collect($mergeGoods)->groupBy(function ($item) {
                                return self::skuGroupBy($item);
                            })->toArray());
                            \Log::info('合并宝贝', [$newMergeGoods]);
                            $count = 0;
                            foreach ($newMergeGoods as $v) {
                                $count++;
//                                foreach ($val as $k => $v) {
                                //第一个宝贝
                                $headOrderItem = $v[0];
                                Log::info('合并宝贝', [$headOrderItem]);
                                $orderNo = $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';
                                if (isset($shopPrintContents['orderNo']) && $shopPrintContents['orderNo'] == '1') {
                                    foreach ($v as $orderTid) {
                                        $orderNo .= str_replace('A', '', $orderTid['tid']) . "\n";
                                    }
                                }

                                if ($shopPrintContents['goodsTitle'] == '1') {
                                    $goodTitle = $headOrderItem['custom_title'] . ' ';
                                }
                                if ($shopPrintContents['outerIid'] == '1') {
                                    $outerId = $headOrderItem['outer_iid'] . ' ';
                                }
                                if ($shopPrintContents['numIid'] == '1') {
                                    $goodsId = $headOrderItem['num_iid'] . ' ';
                                }
                                if ($shopPrintContents['skuDesc'] == '1') {
                                    $customSkuValue = $headOrderItem['custom_sku_value'];
                                    if(Environment::isTaoBao()){
                                        //淘宝的sku名称是拼接的，颜色分类:灰色[深灰色帘头（遮光度96）];窗帘尺寸+加工方式:长3米*高0.22米
                                        //所以需要把sku名称拆分 提出:灰色[深灰色帘头（遮光度96）];长3米*高0.22米
                                        $skuDescs = explode(';', $customSkuValue);
                                        $skuDescItemValues = [];
                                        foreach ($skuDescs as $skuDescItem) {
                                            $skuDescItems = explode(':', $skuDescItem);
                                            $skuDescItemValues[]= $skuDescItems[1] ?? $skuDescItems[0];
                                        }
                                        $skuDesc = implode(';', $skuDescItemValues);
                                    }else{
                                        $skuDesc = $customSkuValue . ' ';
                                    }

                                }
                                if ($shopPrintContents['skuId'] == '1') {
                                    $skuId = $headOrderItem['sku_id'] . ' ';
                                }
                                if ($shopPrintContents['outerSkuIid'] == '1') {
                                    $outerSkuId = $headOrderItem['outer_sku_iid'] . ' ';
                                }
                                if ($shopPrintContents['payment'] == '1') {
                                    \Log::info('打印单价', [$headOrderItem]);
                                    $payment = $headOrderItem['goods_price'] . ' ';
                                }
                                if ($shopPrintContents['goodsNum'] == '1') {
                                    $num = 0;
                                    foreach ($v as $i) {
                                        $num += $i['goods_num'];
                                        \Log::info('打印数量', [$i]);
                                    }
                                    $skuNum =  $num ;


                                    $skuNum=$skuNum. $shopPrintContents['goodsNumCompany'];
                                }
                                \Log::info("打印内容", [$orderNo, $goodTitle, $outerId, $goodsId, $skuDesc, $skuId, $outerSkuId, $payment, $skuNum]);
                                $thisItemName = $orderNo . $goodTitle . $outerId . $goodsId . $skuDesc . $skuId . $outerSkuId . $payment . $skuNum . ($shopPrintContents['goodsLineFeed'] ? "\n" : "");
                                if ($headOrderItem['goods_type'] == 2) {
                                    $thisItemName = self::PREFIX_GIFT . $thisItemName;
                                }
                                $itemName = $itemName . $thisItemName;

                                \Log::info('itemName=' . $itemName);
                            }
//                            }
                        } else {
                            \Log::info('不合并宝贝', $order);

                            foreach ($order['order_item'] as $k => $item) {
                                $orderNo = $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';

                                if (isset($shopPrintContents['orderNo']) && $shopPrintContents['orderNo'] == '1') {
                                    $orderNo .= str_replace('A', '', $item['tid']) . "\n";
                                }
                                if ($shopPrintContents['goodsTitle'] == '1') {
                                    $goodTitle = $item['custom_title'] . ' ';
                                }
                                if ($shopPrintContents['outerIid'] == '1') {
                                    $outerId = $item['outer_iid'] . ' ';
                                }
                                if ($shopPrintContents['numIid'] == '1') {
                                    $goodsId = $item['num_iid'] . ' ';
                                }
                                if ($shopPrintContents['skuDesc'] == '1') {
                                    $skuDesc = $item['custom_sku_value'] . ' ';
                                }
                                if ($shopPrintContents['skuId'] == '1') {
                                    $skuId = $item['sku_id'] . ' ';
                                }
                                if ($shopPrintContents['outerSkuIid'] == '1') {
                                    $outerSkuId = $item['outer_sku_iid'] . ' ';
                                }
                                if ($shopPrintContents['payment'] == '1') {
                                    $payment = $item['payment'] . ' ';
                                }
                                if ($shopPrintContents['goodsNum'] == '1') {
                                    $skuNum = sprintf($shopPrintContents['goodsNumStyle'], $item['goods_num']);


                                    $skuNum=$skuNum. $shopPrintContents['goodsNumCompany'];


                                }

                                $itemName .= $orderNo . $goodTitle . $outerId . $goodsId . $skuDesc . $skuId . $outerSkuId . $payment . $skuNum . ($shopPrintContents['goodsLineFeed'] ? "\n" : "");
                                if ($item['goods_type'] == 2) {
                                    $itemName = self::PREFIX_GIFT . $itemName;
                                }
                            }
                        }
                    }
                    return $itemName;
                    break;
                default:
                    return null;
                    break;
            }
        } else if ($isCustomOrder) {
            $shop = Shop::find($order['shop_id'])->toArray();
            //自由打印时输入商品详情
            $goodsjson = $order['goods_info'];
            $obj = json_decode($goodsjson);
            $goodStr = [];
            $goodTitle = [];
            $goodNum = [];
            $goodNorm = [];
            $goodTotal = 0;
            $totalPrice = 0;
            $goodCode = [];
            $goodPrice = [];
            $goodNormAndNum = [];//规格+数量
            if (isset($obj) && !is_null($obj)) {
                foreach ($obj as $key => $item) {
                    $custom_title = isset($item->title) ? $item->title : '';  //标题
                    $custom_code = isset($item->code) ? $item->code : ' '; //商品编码
                    $custom_norm = isset($item->norm) ? $item->norm : ''; //规格
                    $custom_num = isset($item->num) ? $item->num : 0;  //数量
                    $custom_price = isset($item->price) ? $item->price : 0; //价格

                    $custom_items_num = $custom_num > 0 ? "  ✖ " . $custom_num : '';
                    $custom_items_norm = $custom_norm ? " , " . $custom_norm : '';
                    $custom_items_title = $custom_title ? "、" . $custom_title : '';


                    $goodTitle[] = $custom_title ? ($key + 1) . $custom_items_title : '';
                    $goodNum[] = $custom_items_num;
                    $goodNorm[] = $custom_norm;
                    $goodCode[] = $custom_code;
                    $goodPrice[] = $custom_price > 0 ? $custom_price . "元" : '';

                    $goodStr[] = ($key + 1) . $custom_items_title . $custom_items_norm . $custom_items_num;
                    $goodTotal += intval($custom_num);
                    $totalPrice += floatval($custom_price);
                    $goodNormAndNum[] = $custom_norm . $custom_items_num;
                }
            } else {
                $goodStr = $goodsjson;
            }

            switch ($itemKey) {
                case 'confirmTime':
                    return date('Y-m-d H:i:s', strtotime($order['created_at']));
                    break;
                case 'orderNum':
                    return $order['order_no'];
                    break;
                case 'ownerName':
                    return $shop['name'];
                    break;
                case 'buyerMemo':
                    return '';
                    break;
                case 'mallName':
                    return $shop['shop_name'];
                    break;
                case 'remark':
                    return $order['seller_memo'];
                    break;
                case 'money':
                    return $goodPrice ? $goodPrice : '';
                    break;
                case 'totalPrice':
                    return $totalPrice ? '总价：' . $totalPrice . '元' : '';
                    break;
                case 'productName':
                    return $goodTitle ? $goodTitle : '';
                    break;
                case 'skuNum':
                    return $goodNum ? $goodNum : '';
                    break;
                case 'productStandard':
                    return $goodNorm ? $goodNorm : '';
                    break;
                case 'outerGoodsId':
                    return $goodCode ? $goodCode : '';
                    break;
                case 'count':
                    return $goodTotal > 0 ? '共计：' . $goodTotal . '件' : ' ';
                    break;
                case 'watermark':
                    return $goodTotal > 0 ? $goodTotal . '件' : ' ';
                    break;
                case 'standardAndSkuNum':
                    return $goodNormAndNum ? $goodNormAndNum : '';
                    break;
                case 'allProductDetail':
                    return $goodStr ? $goodStr : '';
                    break;
                case 'numOfPackage':
                    return $index . '/' . $packageNum . '包裹';
                    break;
                case 'contents' :
                    $itemName = '';
                    if (!empty($order['goods_info'])) {
                        $goods_info = trimBlankChars($order['goods_info']);
                        $goodsInfo = json_decode($goods_info, true);
                        //店铺打印内容配置
                        $shopExtra = ShopExtra::query()->where('shop_id', $order['shop_id'])->first();
                        if (empty($shopExtra->print_contents)) {
                            $shopPrintContents = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);
                        } else {
                            $shopPrintContents = json_decode($shopExtra->print_contents, true);
                        }
                        foreach ($goodsInfo as $v) {
                            $orderNo = $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';

                            if (isset($shopPrintContents['orderNo']) && $shopPrintContents['orderNo'] == '1'&&array_key_exists('tid',$v)) {
                                $orderNo .= str_replace('A', '', $v['tid']) . "\n";
                            }
                            if ($shopPrintContents['goodsTitle'] == '1') {
                                $goodTitle = $v['title'] . ' ';
                            }
                            if ($shopPrintContents['outerIid'] == '1') {
                                $outerId = $v['code'] . ' ';
                            }
//                            if ($shopPrintContents['numIid'] == '1') {
//                                $goodsId = $v['num_iid'] . ' ';
//                            }
                            if ($shopPrintContents['skuDesc'] == '1'&&isset($v['norm'])) {
                                $skuDesc = $v['norm'] . ' ';
                            }
//                            if ($shopPrintContents['skuId'] == '1') {
//                                $skuId = $v['sku_id'] . ' ';
//                            }
//                            if ($shopPrintContents['outerSkuIid'] == '1') {
//                                $outerSkuId = $v['outer_sku_iid'] . ' ';
//                            }
                            if ($shopPrintContents['payment'] == '1') {
                                $payment = $v['price'] . ' ';
                            }
                            if ($shopPrintContents['goodsNum'] == '1') {
                                $skuNum = sprintf($shopPrintContents['goodsNumStyle'], $v['num']) . $shopPrintContents['goodsNumCompany'];
                            }

                            $itemName .= $orderNo . $goodTitle . $outerId . $goodsId . $skuDesc . $skuId . $outerSkuId . $payment . $skuNum . ($shopPrintContents['goodsLineFeed'] ? "\n" : "");
                        }
                    }
                    return $itemName;
                    break;
                default:
                    return null;
                    break;
            }
        } else {
            \Log::error('订单数据格式错误', ['item_key' => $itemKey, 'is_custom_order' => $isCustomOrder, 'order' => $order]);
        }
    }

    /**
     * 生成打印的内容
     * @param int $templateShopId 模板的商家id
     * @param array $customConfig 模板表的商家自定义模板信息
     * @param $order
     * @param bool $isCustomOrder
     * @param array $orderItemOId
     * @param int $index
     * @param int $packageNum
     * @param array $contents
     * @return array
     */

    public static function assemPrintItems(int $templateShopId,array $customConfig, $order, bool $isCustomOrder = false,
                                            $orderItemOId = [],
                                           $index = 0, $packageNum = 0,
                                           $contents = [],int $packageIndex=1,
                                           int $maxPackageSkuNum=1,bool $nextPage=false,
                                           $template=null): array
    {
        $packageNumSetting=$packageNum;
//        \Log::info('assemPrintItems', ['customConfig' => $customConfig, 'order' => $order, "maxPackageSkuNum"=>$maxPackageSkuNum,   'isCustomOrder' => $isCustomOrder, 'orderItemOId' => $orderItemOId, 'index' => $index, 'packageNum' => $packageNum, 'contents' => $contents]);
        $printItems = [];
        $totalTop = 0;
        $mergeItems = [];
        $noMergeItems = [];
        $customItems = [];
        $contentsArr = [];
        if (!empty($contents)) {
            $contentsArr = array_column($contents, 'contents', 'id');
        }
        //按照规格或者件数获取单号 临时加入 n/m包裹 打印配置
        if ($packageNum < 0 && !in_array(23, array_column($customConfig, 'id'))) {
            $index = $order->index + 1;
            $packageNum = $order->packageNum;
            $customConfig[] = json_decode(json_encode([
                "id" => 23,
                "aliasName" => "n/m",
                "showName" => "n/m包裹",
                "value" => "<%=data.numOfPackage%>",
                "width" => 140,
                "height" => 28,
                "top" => 122,
                "left" => 46,
                "fontSize" => 18,
                "fontFamily" => "simsun",
                "fontWeight" => "bold",
                "position" => "absolute",
                "custom" => false,
                "isMerge" => false,
                "isChecked" => true
            ]));
        }
        //分了几种情况：
        //1. 平台订单订单合单
        //2. 平台订单非合单
        //3. 自定义订单
        //合单情况
        if (!$isCustomOrder && isset($order->mergeOrders) && count($order->mergeOrders) > 0) {
            \Log::info("进入了平台订单订单合单");
            //合单的情况,$order->mergeOrders是一个数组,合单里面所有的订单（是订单对象）
            $mergeOrders = [];
            //然后把合单的订单放到了一个数组里面
            $mergeOrders = self::extractMergeOrders($order, $mergeOrders);

            //把自定义打印内容分成合单项和非合单项，还有一个$customItems 没用上
            list($mergeItems, $noMergeItems, $customItems) = self::splitItems($customConfig);
            $flag = 1;

            //干掉未被勾选的子订单
            foreach ($mergeOrders as $key => $mergeOrder) {
                foreach ($mergeOrder['order_item'] as $k => $item) {
                    if (array_key_exists($item['order_id'], $orderItemOId) && !in_array($item['id'], $orderItemOId[$mergeOrder['id']])) {
                        unset($mergeOrders[$key]['order_item'][$k]);
                    }
                }
            }
            //合单项
            foreach ($mergeOrders as $mergeOrder) {
                foreach ($mergeOrder['order_item'] as $item) {
                    //对合单项的每个子订单的每个商品进行打印项的组装
                    foreach ($mergeItems as $key => $value) {
                        $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value), $mergeOrder,
                            $item, $contentsArr,$isCustomOrder,$index,$packageNum,$packageNumSetting,$packageIndex,$maxPackageSkuNum);
                        //$top          = $value->top + ($flag - 1) * $value->height;
//						if ($flag > count($mergeOrders)) {
//							$flag = 1;
//						}
                        $top = $value->top + ($flag - 1) * $value->height;
                        //$itemStyle    = '{"fontSize":"' . $value->fontSize . '"}';
                        $fontWeight = isset($value->fontWeight) ? $value->fontWeight : 'bold';
                        $fontFamily = isset($value->fontFamily) ? $value->fontFamily : 'simsun';
                        $itemStyle = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' . $fontWeight . '","fontFamily":"' . $fontFamily . '"}';
                        $printItems[] = [
                            'itemType' => 'goodsinfo',
                            'locationX' => $value->left,
                            'locationY' => $top,
                            'width' => $value->width,
                            'height' => $value->height,
                            'itemName' => $itemName,
                            'itemStyle' => $itemStyle,
                            //'itemName'  => $flag . '.' . $itemName,
                            'tip' => self::getCustomKey($value->value),
                        ];
//						if ($flag >= count($mergeOrders) && $top >= $totalTop) {
//							$totalTop = $top;
//						}
                    }
                    $flag += 1;
                }
            }

            //店铺打印内容配置
            $shopPrintContents = self::getPrintContents($templateShopId);
            //重新排列
            foreach ($mergeOrders as $index1 => $v) {
                $mergeOrders[$index1]['order_item'] = array_values($v['order_item']);
            }
            //非合单项
            foreach ($noMergeItems as $value) {
                //$itemStyle    = '{"fontSize":"' . $value->fontSize . '"}';
                $fontWeight = isset($value->fontWeight) ? $value->fontWeight : 'bold';
                $fontFamily = isset($value->fontFamily) ? $value->fontFamily : 'simsun';
                $itemStyle = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' . $fontWeight . '","fontFamily":"' . $fontFamily . '"}';

                switch (self::getCustomKey($value->value)) {
                    case 'mallName':
                        $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value),
                            $mergeOrders[0], $mergeOrders[0]['order_item'][0]);
                        break;
                    case 'ownerName':
                        $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value),
                            $mergeOrders[0], $mergeOrders[0]['order_item'][0]);
                        break;
                    case 'orderNum':
                        //$itemName = self::getItemName(self::getCustomKey($value->value), $mergeOrders[0], $mergeOrders[0]['order_item'][0]);
                        $tid = [];
                        foreach ($mergeOrders as $mergeOrder) {
                            $tid[] = str_replace('A', '', $mergeOrder['tid']);
                        }
                        $itemName = implode(',', $tid);
                        break;
                    case 'confirmTime':
                        $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value),
                            $mergeOrders[0], $mergeOrders[0]['order_item'][0]);
                        break;
                    case 'count':
                        //$count    = collect($mergeOrders)->sum('num');
                        $count = 0;
                        foreach ($mergeOrders as $mergeOrder) {
                            $count += array_sum(array_column($mergeOrder['order_item'], 'goods_num'));
                        }
                        $itemName = '共计：' . $count . '件';
                        break;
                    case 'totalPrice':
                        //$count    = collect($mergeOrders)->sum('total_fee');
                        $count = 0;
                        foreach ($mergeOrders as $mergeOrder) {
                            $count += array_sum(array_column($mergeOrder['order_item'], 'total_fee'));
                        }
                        $itemName = '总价：' . $count . '元';
                        break;
                    case 'payAmount':
                        //$count    = collect($mergeOrders)->sum('payment');
                        $count = 0;
                        foreach ($mergeOrders as $mergeOrder) {
                            $count += array_sum(array_column($mergeOrder['order_item'], 'payment'));
                        }
                        $itemName = '总支付金额：' . $count . '元';
                        break;
                    case 'watermark':
                        //$count    = collect($mergeOrders)->sum('num');
                        $count = 0;
                        foreach ($mergeOrders as $mergeOrder) {
                            $count += array_sum(array_column($mergeOrder['order_item'], 'goods_num'));
                        }
                        $goodsSum = $count;
                        //

                        $itemName = $goodsSum . '件';
                        \Log::info('水印数量', [$itemName]);



                        break;
                    case 'remark':
                        $itemName = 'remark';
                        break;
                    case 'buyerMemo':
                        $itemName = 'buyerMemo';
                        break;
                    case 'numOfPackage':
                        $itemName = $index . '/' . $packageNum . '包裹';
                        break;
                    case 'contents':
                        $itemName = 'contents';
                        break;
                    default:
                        $itemName = null;
                        break;
                }
                if (!is_null($itemName)) {
                    //提取和排序
                    $newMergeGoods = self::extractAndSortGoods($mergeOrders);

                    //参考合单商品对订单进行排序
                    $sortedMergeOrders = self::sortMergeOrdersReferMergeGoods($mergeOrders, $newMergeGoods, $order);


                    if (self::getCustomKey($value->value) == 'watermark') {

                        //$itemStyle    = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' . $value->fontWeight .'"}';
                        $printItems[] = [
                            'itemType' => 'watermark',
                            'locationX' => $value->left,
                            'locationY' => $nextPage?80: $value->top,
                            'width' => $nextPage?100:$value->width,
                            'height' => $value->height,
                            'itemName' => $itemName,
                            'tip' => self::getCustomKey($value->value),
                            'itemStyle' => $itemStyle,

                        ];
                    } else if ($itemName == 'remark' || $itemName == 'buyerMemo') {
                        //卖家备注 或买家留言
                        $itemName = "";
                        foreach ($sortedMergeOrders as $mergeOrder) {
                            //重新排列orderItem 由于有unset防止出错
                            if (empty($mergeOrder['order_item'])) {
                                continue;
                            }
                            $itemName .= self::getItemName($templateShopId,self::getCustomKey($value->value),
                                    $mergeOrder, $mergeOrder['order_item'][0]) .
                                ($shopPrintContents['goodsLineFeed'] ? "\n" : ",");
                        }

                        $printItems[] = [
                            'itemType' => 'goodsinfo',
                            'locationX' => $value->left,
                            'locationY' => $value->top,
                            'width' => $value->width,
                            'height' => $value->height,
                            'itemStyle' => $itemStyle,
                            'itemName' => $itemName,
                            //'itemName'  => $flag . '.' . $itemName,
                            'tip' => self::getCustomKey($value->value),
                        ];
                    } else if ($itemName == 'contents') {
                        //如果前端有传过来 用传过来的；没有就现场生成
                        if (array_key_exists($mergeOrders[0]['id'], $contentsArr)) {
                            $itemName = $contentsArr[$mergeOrders[0]['id']];
                        } else {
                            $itemName = '';
                            //是否合并宝贝
                            if ($shopPrintContents['goodsMerge'] == '1') {
                                foreach ($newMergeGoods as $v) {
                                    $headOrderItem = $v[0];
                                    \Log::info("合并宝贝", [$headOrderItem]);
//                                    $count ++;
//                                    foreach ($val as $k => $v) {
                                    $orderNo = $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';
                                    if (isset($shopPrintContents['orderNo']) && $shopPrintContents['orderNo'] == '1') {
                                        foreach ($v as $orderTid) {
                                            $orderNo .= str_replace('A', '', $orderTid['tid']) . "\n";
                                        }
                                    }
                                    if ($shopPrintContents['goodsTitle'] == '1') {
                                        $goodTitle = $headOrderItem['custom_title'] . ' ';
                                    }
                                    if ($shopPrintContents['outerIid'] == '1') {
                                        $outerId = $headOrderItem['outer_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['numIid'] == '1') {
                                        $goodsId = $headOrderItem['num_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['skuDesc'] == '1') {
                                        $skuDesc = $headOrderItem['custom_sku_value'] . ' ';
                                    }
                                    if ($shopPrintContents['skuId'] == '1') {
                                        $skuId = $headOrderItem['sku_id'] . ' ';
                                    }
                                    if ($shopPrintContents['outerSkuIid'] == '1') {
                                        $outerSkuId = $headOrderItem['outer_sku_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['payment'] == '1') {
                                        $payment = $headOrderItem['payment'] . ' ';
                                    }
                                    if ($shopPrintContents['goodsNum'] == '1') {
                                        $num = 0;
                                        foreach ($v as $i) {
                                            $num += $i['goods_num'];
                                        }
                                        $skuNum = sprintf($shopPrintContents['goodsNumStyle'], $num) . $shopPrintContents['goodsNumCompany'];

                                    }

                                    $thisItemName = $orderNo . $goodTitle . $outerId . $goodsId . $skuDesc . $skuId .
                                        $outerSkuId . $payment . $skuNum . ($shopPrintContents['goodsLineFeed'] ? "\n" : "");
                                    if ($headOrderItem['goods_type'] == 2) {
                                        $thisItemName = self::PREFIX_GIFT . $thisItemName;
                                    }
                                    $itemName = $itemName . $thisItemName;
                                }
//                                }
                            } else {
                                $sortedMergeOrderItems = self::sortOrderItemGiftLast($mergeOrders);
//                                foreach ($sortedMergeGoods as $mergeOrder) {
                                foreach ($sortedMergeOrderItems as $item) {
                                    $orderNo = $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';
                                    if (isset($shopPrintContents['orderNo']) && $shopPrintContents['orderNo'] == '1') {
                                        $orderNo .= str_replace('A', '', $item['tid']) . "\n";
                                    }

                                    if ($shopPrintContents['goodsTitle'] == '1') {
                                        $goodTitle = $item['custom_title'] . ' ';
                                    }
                                    if ($shopPrintContents['outerIid'] == '1') {
                                        $outerId = $item['outer_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['numIid'] == '1') {
                                        $goodsId = $item['num_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['skuDesc'] == '1') {
                                        $skuDesc = $item['custom_sku_value'] . ' ';
                                    }
                                    if ($shopPrintContents['skuId'] == '1') {
                                        $skuId = $item['sku_id'] . ' ';
                                    }
                                    if ($shopPrintContents['outerSkuIid'] == '1') {
                                        $outerSkuId = $item['outer_sku_iid'] . ' ';
                                    }
                                    if ($shopPrintContents['payment'] == '1') {
                                        $payment = $item['payment'] . ' ';
                                    }
                                    if ($shopPrintContents['goodsNum'] == '1') {
                                        $skuNum = sprintf($shopPrintContents['goodsNumStyle'], $item['goods_num']) . $shopPrintContents['goodsNumCompany'];
                                    }
                                    $thisItemName = $orderNo . $goodTitle . $outerId . $goodsId . $skuDesc . $skuId .
                                        $outerSkuId . $payment . $skuNum . ($shopPrintContents['goodsLineFeed'] ? "\n" : "");
                                    //如果是赠品就在商品名称前面加上赠品标识
                                    if ($item['goods_type'] == 2) {
                                        $thisItemName = self::PREFIX_GIFT . $thisItemName;
                                    }
                                    $itemName = $itemName . $thisItemName;
                                }
//                                }
                            }
                        }

                        $tip = self::getCustomKey($value->value);
                        //打印内容是不是第二页上的发货内容
                        $isNextPageContent=($tip=='contents' && $nextPage);
                        \Log::info('是否是第二页上的发货内容', [$isNextPageContent]);
                        preg_match('/模板(\d+)\*(\d+)mm/', $template['waybill_type'], $ch);
                        $printItems[] = [
                            'tip' => $tip,
                            'itemType' => 'goodsinfo',
                            'locationX' =>$isNextPageContent?0:  $value->left,
                            'locationY' => $isNextPageContent?50: $value->top,
                            'width' => $isNextPageContent?($ch[1]*4-50): $value->width,
                            'height' => $isNextPageContent?($ch[2]*4-50):$value->height,
                            'itemStyle' => $itemStyle,
                            //'itemName'  => $flag . '.' . $itemName,
                            'itemName' => $itemName,
                        ];
                    } else {
                        $printItems[] = [
                            'itemType' => 'goodsinfo',
                            'locationX' => $value->left,
                            //'locationY' => $totalTop + $value->height,
                            'locationY' => $value->top,
                            'width' => $value->width,
                            'height' => $value->height,
                            'itemStyle' => $itemStyle,
                            'itemName' => $itemName,
                            'tip' => self::getCustomKey($value->value),
                        ];
                    }
                } else {
                    $printItems[] = [
                        'itemType' => 'goodsinfo',
                        'locationX' => $value->left,
                        'locationY' => $value->top,
                        'width' => $value->width,
                        'height' => $value->height,
                        'itemName' => $value->value,
                        'itemStyle' => $itemStyle,
                        'tip' => '自定义',
                    ];
                }
            }

        } elseif (!$isCustomOrder && is_null($order->mergeOrders) && isset($order->orderItem) && count($order->orderItem) > 0) {
            //平台订单非合单订单，包含购物车订单
            \Log::info("进入了平台订单非合单");
            $temp = $order->toArray();

            $flag = 1;
            foreach ($customConfig as $value) {
                $fontWeight = isset($value->fontWeight) ? $value->fontWeight : 'bold';
                $fontFamily = isset($value->fontFamily) ? $value->fontFamily : 'simsun';
                $itemStyle = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' . $fontWeight . '","fontFamily":"' . $fontFamily . '"}';
                //去除自定义文字
                if ($value->id != 18) {
                    // 先过滤无效数据
                    foreach ($temp['order_item'] as $key => $item) {
                        // 未选中子订单不处理
                        if (!empty($orderItemOId) && isset($orderItemOId[$temp['id']]) && !in_array($item['id'], $orderItemOId[$temp['id']])) {
                            unset($temp['order_item'][$key]);
                        }
                    }
                    // 再循环
                    foreach ($temp['order_item'] as $key => $item) {
                        //count会重复
                        $next = false;
                        foreach ($printItems as $v) {
                            if ($v['tip'] == self::getCustomKey($value->value)) {
                                $next = true;
                            }
                        }
                        if ($next) {
                            continue;
                        }
                        $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value), $temp,
                            $item, $contentsArr, false, $index, $packageNum,$packageNumSetting,$packageIndex,$maxPackageSkuNum);
                        if (!is_null($itemName)) {
                            if (self::getCustomKey($value->value) == 'watermark') {
                                //$itemStyle    = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' .$value->fontWeight . '"}';
                                $printItems[] = [
                                    'itemType' => 'watermark',
                                    'locationX' => $value->left,
                                    'locationY' => $nextPage?80:$value->top,
                                    'width' => $nextPage?100:$value->width,
                                    'height' => $value->height,
                                    'itemName' => $itemName,
                                    'tip' => self::getCustomKey($value->value),
                                    'itemStyle' => $itemStyle,

                                ];
                            } else if (self::getCustomKey($value->value) == 'contents') {
                                $isNextPageContent=$nextPage;
                                preg_match('/模板(\d+)\*(\d+)mm/', $template['waybill_type'], $ch);
                                $printItems[] = [
                                    'itemType' => 'goodsinfo',
                                    'locationX' =>$isNextPageContent?0: $value->left,
                                    'locationY' => $isNextPageContent?50:$value->top,
                                    'width' => $isNextPageContent&&key_exists(1,$ch)?(intval($ch[1])*4-50): $value->width,
                                    'height' => $isNextPageContent&&key_exists(2,$ch)?(intval($ch[2])*4-50): $value->height,
                                    'itemName' => $itemName,
                                    'itemStyle' => $itemStyle,
                                    //'itemName'  => $flag . '.' . $itemName,
                                    'tip' => self::getCustomKey($value->value),
                                ];
//                                \Log::info('是否是第二页上的发货内容非合单', [$isNextPageContent,intval($ch[1])*4-50,intval($ch[2])*4-50]);
                            } else if (self::getCustomKey($value->value) == 'remark' || self::getCustomKey($value->value) == 'buyerMemo') {
                                $printItems[] = [
                                    'itemType' => 'goodsinfo',
                                    'locationX' => $value->left,
                                    'locationY' => $value->top,
                                    'width' => $value->width,
                                    'height' => $value->height,
                                    'itemName' => $itemName,
                                    'itemStyle' => $itemStyle,
                                    //'itemName'  => $flag . '.' . $itemName,
                                    'tip' => self::getCustomKey($value->value),
                                ];
                            } else {
                                if ($flag > count($temp['order_item'])) {
                                    $flag = 1;
                                }
                                $top = $value->top + ($flag - 1) * $value->height;
//								if (in_array(self::getCustomKey($value->value), ['numOfPackage','totalPrice', 'count', 'confirmTime', 'mallName'])) {
//                                    $top = $value->top;
//                                }
                                $printItems[] = [
                                    'itemType' => 'goodsinfo',
                                    'locationX' => $value->left,
                                    'locationY' => $top,
                                    'width' => $value->width,
                                    'height' => $value->height,
                                    'itemName' => $itemName,
                                    'itemStyle' => $itemStyle,
                                    //'itemName'  => $flag . '.' . $itemName,
                                    'tip' => self::getCustomKey($value->value),
                                ];
                                $flag += 1;
                                if ($flag >= count($temp['order_item']) && $top >= $totalTop) {
                                    $totalTop = $top;
                                }
                            }
                        }
                    }

                } else {
                    $printItems[] = [
                        'itemType' => 'goodsinfo',
                        'locationX' => $value->left,
                        'locationY' => $value->top,
                        'width' => $value->width,
                        'height' => $value->height,
                        'itemName' => $value->value,
                        'itemStyle' => $itemStyle,
                        'tip' => '自定义',
                    ];
                }
            }
        } else {
            //自由打单
            $temp = $order->toArray();
            unset($temp['mergeOrders']);
            if (isset($temp['goods_info'])) {
                $mergeOrders = json_decode($temp['goods_info'], true);
            } else {
                $mergeOrders = array();
            }
            $CustomArr = array("money", "productName", "skuNum", "productStandard", "standardAndSkuNum", "allProductDetail", "outerGoodsId");
            $flag = 1;
            foreach ($customConfig as $value) {
                $fontWeight = isset($value->fontWeight) ? $value->fontWeight : 'bold';
                $fontFamily = isset($value->fontFamily) ? $value->fontFamily : 'simsun';
                $itemStyle = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' . $fontWeight . '","fontFamily":"' . $fontFamily . '"}';
                //去除自定义文字
                if ($value->id != 18) {
                    $itemName = self::getItemName($templateShopId,self::getCustomKey($value->value), $temp, [], [],
                        $isCustomOrder, $index, $packageNum);
                    if (!is_null($itemName)) {
                        if (self::getCustomKey($value->value) == 'watermark') {
                            //$itemStyle    = '{"fontSize":"' . $value->fontSize . '","fontWeight":"' .$value->fontWeight . '","fontFamily":"' .$value->fontFamily . '"}';
                            $printItems[] = [
                                'itemType' => 'watermark',
                                'locationX' => $value->left,
                                'locationY' => $value->top,
                                'width' => $value->width,
                                'height' => $value->height,
                                'itemName' => $itemName,
                                'tip' => self::getCustomKey($value->value),
                                'itemStyle' => $itemStyle,

                            ];
                        } else if ($isCustomOrder && !empty($mergeOrders) &&
                            in_array(self::getCustomKey($value->value), $CustomArr)
                        ) {
                            foreach ($mergeOrders as $key => $mergeOrder) {
                                if ($flag > count($mergeOrders)) {
                                    $flag = 1;
                                }
                                $top = $value->top + ($flag - 1) * $value->height;
                                $printItems[] = [
                                    'itemType' => 'goodsinfo',
                                    'locationX' => $value->left,
                                    'locationY' => $top,
                                    'width' => $value->width,
                                    'height' => $value->height,
                                    'itemStyle' => $itemStyle,
                                    'itemName' => $itemName[$key],
                                    'tip' => self::getCustomKey($value->value),
                                ];
                                $flag += 1;
                            }

                        } else {
                            $printItems[] = [
                                'itemType' => 'goodsinfo',
                                'locationX' => $value->left,
                                'locationY' => $value->top,
                                'width' => $value->width,
                                'height' => $value->height,
                                'itemStyle' => $itemStyle,
                                'itemName' => $itemName,
                                'tip' => self::getCustomKey($value->value),
                            ];
                        }
                    }
                } else {
                    $printItems[] = [
                        'itemType' => 'goodsinfo',
                        'locationX' => $value->left,
                        'locationY' => $value->top,
                        'width' => $value->width,
                        'height' => $value->height,
                        'itemName' => $value->value,
                        'itemStyle' => $itemStyle,
                        'tip' => '自定义',
                    ];
                }
            }
        }

        return $printItems;
    }

    /**
     * 构造打印数据
     * @param      $sender
     * @param      $order
     * @param      $template
     * @param      $waybillHistory
     * @param array $orderItemOId
     * @param bool $isCustomOrder
     * @param int $index
     * @param int $packageNum
     * @param array $contents
     * @param array $printConfig
     * @param int $maxPackageGoodsNum
     * @return array
     */
    public static function templateData($sender, $order, $template, $waybillHistory, $orderItemOId = [],
                                        bool $isCustomOrder = false, $index = 0, $packageNum = 0, $contents = [],
                                        array $printConfig= [],int $packageIndex=1,int $maxPackageGoodsNum = 1):
    array
    {
        //模版对应的店铺ID
        $templateShopId = $template['shop_id'];
        \Log::info('templateData', ["templateShopId"=>$templateShopId,"packageIndex"=>$packageIndex,   "order" => $order,"packageNum"=>$packageNum,"maxPackageGoodsNum"=>$maxPackageGoodsNum, "isCustomOrder" => $isCustomOrder, "orderItemOId" => $orderItemOId, "index" => $index]);
        $orderIdStr = handleOrderIdStr($order, $orderItemOId);
        $customConfig = self::getTemplateCustomConfig($template, $isCustomOrder, $order);
//        \Log::info('customConfig', ["customConfig" => $customConfig,"template"=>$template]);
        //
        $printNextItemBeans = self::assemPrintItems($templateShopId,$customConfig, $order, $isCustomOrder,
            $orderItemOId, $index, $packageNum, $contents,$packageIndex,$maxPackageGoodsNum,false,$template);
        $hasNetPage = false;
        $newPrintNextItemBeans = [];
        //子订单高度太高打在第二页上
        //if ($template['goods_new_page'] && !empty($allProductDetail) && $allProductDetail[0]['height'] * count($allProductDetail) > ($template['height'] / 0.35)) {
        //店铺打印内容配置
        $shopExtra = ShopExtra::query()->where('shop_id', $templateShopId)->first();
        if (empty($shopExtra->print_contents)) {
            $shopPrintContents = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);
        } else {
            $shopPrintContents = json_decode($shopExtra->print_contents, true);
        }
        //把
        $templateAuthSource = $template['auth_source'];
        if ($templateAuthSource != Waybill::AUTH_SOURCE_KS && $shopPrintContents['goodsPaging'] == '1' && !$isCustomOrder) {
            $contentsArr = [];
            if (!empty($contents)) {
                $contentsArr = array_column($contents, 'contents', 'id');
            }

            if (!empty($contents) && array_key_exists($order['id'], $contentsArr)) {
                $contentsCount = count(explode("\n", $contentsArr[$order['id']])) - 1;
            } else {
                $mergeItems = [];
                $newOrder = $order->toArray();
                foreach ($newOrder['order_item'] as $item) {
                    $mergeItems[] = $item;
                }
                if ($newOrder['mergeOrders']) {
                    foreach ($newOrder['mergeOrders'] as $val) {
                        $val = $val->toArray();
                        foreach ($val['order_item'] as $v) {
                            $mergeItems[] = $v;
                        }
                    }
                }
                if ($shopPrintContents['goodsMerge']) {
                    $contentsCount = 0;
                    //按照商品名称分组,获取分组后的商品数量
                    $newMergeItems = collect($mergeItems)->groupBy(function ($item) {
                        return self::skuGroupBy($item);
                    })->toArray();
                    foreach ($newMergeItems as $item) {
                        foreach ($item as $v) {
                            $contentsCount++;
                        }
                    }
                } else {
                    $contentsCount = count($mergeItems);
                }
            }
            \Log::info("contentsCount", ["contentsCount" => $contentsCount, "shopPrintContents" => $shopPrintContents['pageCountNum']]);

//            \Log::info("contentsCount", ["contentsCount" => $contentsCount, "shopPrintContents" => $shopPrintContents['pageCountNum']]);
            //比较商品数量和配置的分页数量，微信视频号电子模板不支持
            if ($templateAuthSource != Waybill::AUTH_SOURCE_WXSP&&($contentsCount > intval($shopPrintContents['pageCountNum']))) {

                $hasNetPage = true;
                $printNextItemBeans = [];
                //修改提示内容以及坐标
                $printNextItemBeans[0] = [
                    'itemType' => 'goodsinfo',
                    'locationY' => 0,
                    'locationX' => 0,
                    'width' => 160,
                    'height' => 178,
                    'itemName' => "商品详情见下一页。"
                ];
                $nextPageConfig = $customConfig;
                foreach ($nextPageConfig as $key => $item) {
                    if ($item->id == 17) {
                        $nextPageConfig[$key]->top = 30;
                        $nextPageConfig[$key]->left = 10;
                    }
                }
                $newPrintNextItemBeans = self::assemPrintItems($templateShopId,$nextPageConfig, $order,
                    $isCustomOrder, $orderItemOId, $index, $packageNum,[],$maxPackageGoodsNum,1,true,$template);
                //修改区域大小 占第二页满屏
                foreach ($newPrintNextItemBeans as $k => $item) {
                    if ($item['itemType'] == 'goodsinfo') {
//                        $newPrintNextItemBeans[$k]['height'] = 365;
                    }
                }
            }
        }
        $data = [
            "template" => [
                "width" => $template['width'],
                "height" => $template['height'],
            ],
            "printNextItemBeans" => $printNextItemBeans,
        ];
        if ($templateAuthSource == Waybill::AUTH_SOURCE_PDD_WB || $templateAuthSource == Waybill::AUTH_SOURCE_PDD) {
            return self::getPDDPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
        } else if ($templateAuthSource == Waybill::AUTH_SOURCE_DY) {
            return DyPrintTemplate::getPrintData($order, $hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data,
                $newPrintNextItemBeans, Environment::isWxOrWxsp());
        } else if ($templateAuthSource == Waybill::AUTH_SOURCE_KS) {
            return self::getKSPrintData($order, $hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
        } else if ($templateAuthSource == Waybill::AUTH_SOURCE_JD) {
            return self::getJDPrintData($order, $hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
        }else if($templateAuthSource == Waybill::AUTH_SOURCE_WXSP){
            return WxspPrintTemplate::getPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
        } else {
            return self::getCNPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
        }
    }
//    public static function wxspTemplateData($sender, $order, $template, $waybillHistory, $orderItemOId = [],
//                                        bool $isCustomOrder = false, $index = 0, $packageNum = 0, $contents = [],
//                                        array $printConfig= []):
//    array
//    {
//
//        //模版对应的店铺ID
//        $templateShopId = $template['shop_id'];
//        \Log::info('微信模板数据生成', ["templateShopId"=>$templateShopId,"order" => $order]);
//        $orderIdStr = handleOrderIdStr($order, $orderItemOId);
//        $customConfig =[];// self::getTemplateCustomConfig($template, $isCustomOrder, $order);
////        \Log::info('customConfig', ["customConfig" => $customConfig,"template"=>$template]);
//        //
//        $printNextItemBeans =self::assemPrintItems($templateShopId,$customConfig, $order, $isCustomOrder,
//            $orderItemOId, $index, $packageNum, $contents);
//        $hasNetPage = false;
//        $newPrintNextItemBeans = [];
////        //子订单高度太高打在第二页上
////        //if ($template['goods_new_page'] && !empty($allProductDetail) && $allProductDetail[0]['height'] * count($allProductDetail) > ($template['height'] / 0.35)) {
////        //店铺打印内容配置
////        $shopExtra = ShopExtra::query()->where('shop_id', $templateShopId)->first();
////        if (empty($shopExtra->print_contents)) {
////            $shopPrintContents = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);
////        } else {
////            $shopPrintContents = json_decode($shopExtra->print_contents, true);
////        }
////        //把
////        $templateAuthSource = $template['auth_source'];
////        if ($templateAuthSource != Waybill::AUTH_SOURCE_KS && $shopPrintContents['goodsPaging'] == '1' && !$isCustomOrder) {
////            $contentsArr = [];
////            if (!empty($contents)) {
////                $contentsArr = array_column($contents, 'contents', 'id');
////            }
////
////            if (!empty($contents) && array_key_exists($order['id'], $contentsArr)) {
////                $contentsCount = count(explode("\n", $contentsArr[$order['id']])) - 1;
////            } else {
////                $mergeItems = [];
////                $newOrder = $order->toArray();
////                foreach ($newOrder['order_item'] as $item) {
////                    $mergeItems[] = $item;
////                }
////                if ($newOrder['mergeOrders']) {
////                    foreach ($newOrder['mergeOrders'] as $val) {
////                        $val = $val->toArray();
////                        foreach ($val['order_item'] as $v) {
////                            $mergeItems[] = $v;
////                        }
////                    }
////                }
////                if ($shopPrintContents['goodsMerge']) {
////                    $contentsCount = 0;
////                    //按照商品名称分组,获取分组后的商品数量
////                    $newMergeItems = collect($mergeItems)->groupBy(function ($item) {
////                        return self::skuGroupBy($item);
////                    })->toArray();
////                    foreach ($newMergeItems as $item) {
////                        foreach ($item as $v) {
////                            $contentsCount++;
////                        }
////                    }
////                } else {
////                    $contentsCount = count($mergeItems);
////                }
////            }
////            \Log::info("contentsCount", ["contentsCount" => $contentsCount, "shopPrintContents" => $shopPrintContents['pageCountNum']]);
////
//////            \Log::info("contentsCount", ["contentsCount" => $contentsCount, "shopPrintContents" => $shopPrintContents['pageCountNum']]);
////            //比较商品数量和配置的分页数量
////            if ($contentsCount > intval($shopPrintContents['pageCountNum'])) {
////                $hasNetPage = true;
////                $printNextItemBeans = [];
////                //修改提示内容以及坐标
////                $printNextItemBeans[0] = [
////                    'itemType' => 'goodsinfo',
////                    'locationY' => 0,
////                    'locationX' => 0,
////                    'width' => 160,
////                    'height' => 178,
////                    'itemName' => "商品详情见下一页。"
////                ];
////                $nextPageConfig = $customConfig;
////                foreach ($nextPageConfig as $key => $item) {
////                    if ($item->id == 17) {
////                        $nextPageConfig[$key]->top = 30;
////                        $nextPageConfig[$key]->left = 10;
////                    }
////                }
////                $newPrintNextItemBeans = self::assemPrintItems($templateShopId,$nextPageConfig, $order,
////                    $isCustomOrder, $orderItemOId, $index, $packageNum);
////                //修改区域大小 占第二页满屏
////                foreach ($newPrintNextItemBeans as $k => $item) {
////                    if ($item['itemType'] == 'goodsinfo') {
////                        $newPrintNextItemBeans[$k]['height'] = 365;
////                    }
////                }
////            }
////        }
//        $data = [
//            "template" => [
//                "width" => $template['width'],
//                "height" => $template['height'],
//            ],
//            "printNextItemBeans" => $printNextItemBeans,
//        ];
//         return WxspPrintTemplate::getPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $sender, $data, $newPrintNextItemBeans);
//    }

    /**
     * 切了自定义字符串
     * @param string $str
     * @return bool|string
     */
    public static function getCustomKey(string $str)
    {
        $str = substr($str, stripos($str, '.') + 1);
        $key = substr($str, 0, stripos($str, '%'));

        return $key;
    }

    /**
     * 获取合并打印模板
     * @param string $wpCode
     * @return mixed|string
     */
    public static function getMergeXml(string $wpCode)
    {
        $specialXml = [
            'BESTQJT' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/BESTQJTDEBANGWULIU_merge.xml',
            'DEBANGWULIU' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/BESTQJTDEBANGWULIU_merge.xml',
            'DB' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/DB_merge.xml',
            'HT' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/HT_merge.xml',
            'JD' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/JD_merge.xml',
            'SF' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/SF_merge.xml',
            'YTO' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/YTO_merge.xml',
            'YUNDA' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/YUNDASTO_merge.xml',
            'YZXB' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/YZXB_merge.xml',
            'ZJS' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/ZJS_merge.xml',
            'ZTO' => 'https://renren-kd.oss-cn-zhangjiakou.aliyuncs.com/merge_xml/ZTO_merge.xml',
        ];

        return isset($specialXml[$wpCode]) ? $specialXml[$wpCode] : self::MERGE_TEMPLATE;
    }

    /**
     * 组装菜鸟打印数据
     * @param $hasNetPage
     * @param $template
     * @param $waybillHistory
     * @param string $orderIdStr
     * @param $sender
     * @param array $data
     * @param array $newPrintNextItemBeans
     * @return array
     */
    private static function getCNPrintData($hasNetPage, $template, $waybillHistory, string $orderIdStr, $sender, array $data, array $newPrintNextItemBeans)
    {
        if (!$hasNetPage) {
            return [
                'express_code' => array_get($template, 'wp_code', ''),
                'express_no' => array_get($waybillHistory, 'waybill_code', ''),
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'encryptedData' => array_get($waybillHistory, 'print_data', ''),
                        'ver' => 'waybill_print_secret_version_1',
                        'templateURL' => $template['template_url'],
                        'addData' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ],
                        'signature' => '',
                    ],
                    [
                        'data' => $data,
                        'templateURL' => TemplateURLConst::HTTPS_PRINT_STATIC_RESOURCES_OSS_CN_ZHANGJIAKOU_ALIYUNCS_COM_TEMPLATE_CN_EXTRA_XML
                    ]
                ],
            ];
        } else {
            $result[] = [
                'express_code' => array_get($template, 'wp_code', ''),
                'express_no' => array_get($waybillHistory, 'waybill_code', ''),
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'encryptedData' => array_get($waybillHistory, 'print_data', ''),
                        'ver' => 'waybill_print_secret_version_1',
                        'templateURL' => $template['template_url'],
                        'addData' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ],
                        'signature' => '',
                    ],
                    [
                        'data' => $data,
                        'templateURL' => TemplateURLConst::HTTPS_PRINT_STATIC_RESOURCES_OSS_CN_ZHANGJIAKOU_ALIYUNCS_COM_TEMPLATE_CN_EXTRA_XML
                    ]
                ],
            ];

            unset($data['isNextPage']);
            preg_match('/模板(\d+)\*(\d+)mm/', $template['waybill_type'], $ch);
            $result[] = [
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'data' => [
                            "template" => [
                                "width" => $ch[1],
                                "height" => $ch[2],
                            ],
                            "printNextItemBeans" => $newPrintNextItemBeans,
                        ],
                        'templateURL' => 'https://printer-static.oss-cn-zhangjiakou.aliyuncs.com/template/cn_new_extra.xml'
                    ]
                ],
            ];

            return $result;
        }
    }

    /**
     * 组装拼多多打印数据
     * @param $hasNetPage
     * @param $template
     * @param $waybillHistory
     * @param string $orderIdStr
     * @param $sender
     * @param array $data
     * @param array $newPrintNextItemBeans
     * @return array
     */
    private static function getPDDPrintData($hasNetPage, $template, $waybillHistory, string $orderIdStr, $sender, array $data, array $newPrintNextItemBeans)
    {
        if (!$hasNetPage) {
            return [
                'express_code' => array_get($template, 'wp_code', ''),
                'express_no' => array_get($waybillHistory, 'waybill_code', ''),
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'encryptedData' => array_get($waybillHistory, 'print_data', ''),
                        'ver' => '3',
                        'templateUrl' => $template['template_url'],
                        'addData' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ],
                        'userid' => '1',
                    ],
                    [
                        'data' => $data,
                        'templateUrl' => '' . TemplateURLConst::HTTPS_PRINT_STATIC_RESOURCES_OSS_CN_ZHANGJIAKOU_ALIYUNCS_COM_TEMPLATE_PDD_EXTRA_XML . ''
                    ]
                ],
            ];
        } else {
            $result[] = [
                'express_code' => array_get($template, 'wp_code', ''),
                'express_no' => array_get($waybillHistory, 'waybill_code', ''),
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'encryptedData' => array_get($waybillHistory, 'print_data', ''),
                        'ver' => '3',
                        'templateUrl' => $template['template_url'],
                        'addData' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ],
                        'userid' => '1',
                    ],
                    [
                        'data' => $data,
                        'templateUrl' => TemplateURLConst::HTTPS_PRINT_STATIC_RESOURCES_OSS_CN_ZHANGJIAKOU_ALIYUNCS_COM_TEMPLATE_PDD_EXTRA_XML
                    ]
                ],
            ];

            preg_match('/模板(\d+)\*(\d+)mm/', $template['waybill_type'], $ch);
            $result[] = [
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        'data' => [
                            "template" => [
                                "width" => $ch[1],
                                "height" => $ch[2],
                            ],
                            "printNextItemBeans" => $newPrintNextItemBeans,
                        ],
                        'templateURL' => 'https://printer-static.oss-cn-zhangjiakou.aliyuncs.com/template/pdd_new_extra.xml'
                    ]
                ],
            ];

            return $result;
        }
    }

    public static function factoryTemplateData($shopId, SenderAddressBo $senderAddressBo, Bo\PrintDataPackBo $printDataPackBo,
                                               array $template, int $packageCurrent, int $packageTotal, array $contents)
    {
        $orderIdStr = $printDataPackBo->getOrderIdStr();
        $customConfig = json_decode($template['custom_config']);
        $hasNetPage = false;
        $newPrintNextItemBeans = [];

        //店铺打印内容配置
        $shopExtra = ShopExtra::query()->where('shop_id', $shopId)->first();
        $shopPrintConfig = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);

        if (!empty($shopExtra->print_contents)) {
            $shopPrintConfig = json_decode($shopExtra->print_contents, true);
        }

        $printNextItemBeans = self::getPrintItems($customConfig, $printDataPackBo, $contents, $shopPrintConfig, $packageCurrent, $packageTotal);
        if ($shopPrintConfig['goodsPaging'] == '1') {
            $hasNetPage = true;
            $printNextItemBeans = [];
            $printNextItemBeans[] = [
                'itemType' => 'goodsinfo',
                'locationY' => 0,
                'locationX' => 0,
                'width' => 160,
                'height' => 178,
                'itemName' => "商品详情见下一页。"
            ];
            $nextPageConfig = $customConfig;
            foreach ($nextPageConfig as $key => $item) {
                if ($item->id == 17) {
                    $nextPageConfig[$key]->top = 30;
                    $nextPageConfig[$key]->left = 10;
                }
            }
            $newPrintNextItemBeans = self::getPrintItems($customConfig, $printDataPackBo, $contents, $shopPrintConfig, $packageCurrent, $packageTotal);
            //修改区域大小 占第二页满屏
            foreach ($newPrintNextItemBeans as $k => $item) {
                if ($item['itemType'] == 'goodsinfo') {
                    $newPrintNextItemBeans[$k]['height'] = 365;
                }
            }
        }
        $waybillHistory = [
            'waybill_code' => $printDataPackBo->waybill_code,
            'print_data' => $printDataPackBo->getWaybillsPrintData()->encrypted_data,
            'sign' => $printDataPackBo->getWaybillsPrintData()->sign,
        ];
        $data = [
            "template" => [
                "width" => $template['width'],
                "height" => $template['height'],
            ],
            "printNextItemBeans" => $printNextItemBeans,
        ];
        $order = null;
        if ($template['auth_source'] == Waybill::AUTH_SOURCE_PDD_WB || $template['auth_source'] == Waybill::AUTH_SOURCE_PDD) {
            return self::getPDDPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $senderAddressBo, $data, $newPrintNextItemBeans);
        } else if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
            return Printing\Stage\DyPrintTemplate::getPrintData($order, $hasNetPage, $template, $waybillHistory, $orderIdStr, $senderAddressBo, $data, $newPrintNextItemBeans);
        } else {
            return self::getCNPrintData($hasNetPage, $template, $waybillHistory, $orderIdStr, $senderAddressBo, $data, $newPrintNextItemBeans);
        }
    }

    public static function getItemNameByFactoryOrder(string $itemKey, PrintOrderBo $printOrderBo)
    {
        $itemName = '';
        //给自定义模板字段赋值
        switch ($itemKey) {
            case 'confirmTime':
                $itemName = $printOrderBo->orderTime;
                break;
            case 'orderNum':
                $itemName = $printOrderBo->orderNo;
                break;
            case 'ownerName':
            case 'mallName':
                $itemName = $printOrderBo->getShopName();
                break;
            case 'buyerMemo':
                $itemName = $printOrderBo->buyerMemo;
                break;
//            case 'count':
//                //return '共计：' . $order['num'] . '件';
//                return '共计：' . array_sum(array_column($order['order_item'], 'goods_num')) . '件';
//                break;
//            case 'watermark':
//                //return $order['num'] . '件';
//                return array_sum(array_column($order['order_item'], 'goods_num')) . '件';
//                break;
            case 'goodsId':
                $itemName = $printOrderBo->goodsId;
                break;
            case 'skuId':
                $itemName = $printOrderBo->skuId;
                break;
            case 'productName':
                $itemName = $printOrderBo->goodsName;
                break;
            case 'skuNum':
                $itemName = '✖' . $printOrderBo->goodsNum;
                break;
            case 'outerSkuId':
                $itemName = $printOrderBo->outerSkuId;
                break;
            case 'outerId':
                $itemName = $printOrderBo->outerId;
                break;
            case 'productStandard':
                $itemName = $printOrderBo->skuValue;
                empty($itemName) && $itemName = '【无】';
//                $desc = $orderItem['custom_order_sku_value'] ?? ($orderItem['custom_sku_value'] ?? (empty($orderItem['sku_desc']) ? '【无】' : $orderItem['sku_desc']));
                break;
            case 'money':
                $itemName = $printOrderBo->payment . '元';
                break;
            case 'totalPrice':
                $itemName = '总价: ' . $printOrderBo->totalPrice . '元';
                break;
            case 'payAmount':
                $itemName = '总支付金额:' . $printOrderBo->payment . '元';
                break;
            case 'remark':
                $itemName = $printOrderBo->sellerMemo;
                break;
            case 'standardAndSkuNum':
                $itemName = "{$printOrderBo->skuValue},✖{$printOrderBo->goodsNum}";
                break;
            case 'outerIdAndSkuNum':
                $itemName = "{$printOrderBo->outerSkuId},✖{$printOrderBo->goodsNum}";
                break;
            case 'allProductDetail':
                $itemName = "{$printOrderBo->customTitle},{$printOrderBo->skuValue},✖{$printOrderBo->goodsNum}";
                break;
            default:
                break;
        }
        return $itemName;
    }

    /**
     * @param string $customKey
     * @param PrintOrderBo[] $printOrderBoArr
     * @param array $contentsArr
     * @param array $shopPrintConfig
     * @param int $packageCurrent
     * @param int $packageTotal
     * @return mixed|string
     * <AUTHOR>
     */
    private static function getItemNameByPrintOrderBoArr(string $customKey, array $printOrderBoArr, array $contentsArr,
                                                         array  $shopPrintConfig, int $packageCurrent, int $packageTotal)
    {
        $itemName = '';
        switch ($customKey) {
            case 'count':
                $num = collect($printOrderBoArr)->pluck('goodsNum')->sum();
                $itemName = '共计：' . $num . '件';
                break;
            case 'watermark':
                $num = collect($printOrderBoArr)->pluck('goodsNum')->sum();
                $itemName = $num . '件';
                break;
            case 'numOfPackage':
                $itemName = $packageCurrent . '/' . $packageTotal . '包裹';
                break;
            case 'contents':
                //如果前端有传过来 用传过来的；没有就现场生成
                if (array_key_exists($printOrderBoArr[0]->orderId, $contentsArr)) {
                    $itemName = $contentsArr[$printOrderBoArr[0]->orderId];
                } else {
                    //是否合并宝贝
                    $goodsMerge = $shopPrintConfig['goodsMerge'] == '1';

                    $goodsArr = [];
                    foreach ($printOrderBoArr as $index => $printOrderBo) {
                        $goodTitle = $outerId = $goodsId = $skuDesc = $skuId = $outerSkuId = $payment = $skuNum = '';
                        if ($shopPrintConfig['goodsTitle'] == '1') {
                            $goodTitle = $printOrderBo->customTitle . ' ';
                        }
                        if ($shopPrintConfig['outerIid'] == '1') {
                            $outerId = $printOrderBo->outerSkuId . ' ';
                        }
                        if ($shopPrintConfig['numIid'] == '1') {
                            $goodsId = $printOrderBo->goodsId . ' ';
                        }
                        if ($shopPrintConfig['skuDesc'] == '1') {
                            $skuDesc = $printOrderBo->skuValue . ' ';
                        }
                        if ($shopPrintConfig['skuId'] == '1') {
                            $skuId = $printOrderBo->skuId . ' ';
                        }
                        if ($shopPrintConfig['outerSkuIid'] == '1') {
                            $outerSkuId = $printOrderBo->outerSkuId . ' ';
                        }
                        if ($shopPrintConfig['payment'] == '1') {
                            $payment = $printOrderBo->payment . ' ';
                        }
                        if ($shopPrintConfig['goodsNum'] == '1') {
                            $skuNum = $printOrderBo->goodsNum;
                        }
                        if ($goodsMerge) {
                            if (isset($goodsArr[$skuId])) {
                                $goodsArr[$skuId]['skuNum'] += $skuNum;
                            } else {
                                $goodsArr[$skuId] = compact('goodTitle', 'outerId', 'goodsId', 'skuDesc', 'skuId', 'outerSkuId', 'payment', 'skuNum');
                            }
                        } else {
                            $goodsArr[] = compact('goodTitle', 'outerId', 'goodsId', 'skuDesc', 'skuId', 'outerSkuId', 'payment', 'skuNum');
                        }
                    }
                    foreach ($goodsArr as $index => $item) {
                        $skuNum = sprintf($shopPrintConfig['goodsNumStyle'], $item['skuNum']) . $shopPrintConfig['goodsNumCompany'];
                        $itemName .= $item['goodTitle'] . $item['outerId'] . $item['goodsId'] . $item['skuDesc'] . $item['skuId']
                            . $item['outerSkuId'] . $item['payment'] . $skuNum . ($shopPrintConfig['goodsLineFeed'] ? "\n" : "");
                    }
                }
                break;
        }
        return $itemName;
    }

    /**
     * @param $customKey
     * @param $value
     * @param int $orderCounter
     * @param $locationY
     * @return string
     * <AUTHOR>
     */
    public static function getItemTypeByCustomKey($customKey, $value, int &$orderCounter, &$locationY)
    {
        switch ($customKey) {
            case 'watermark':
                $itemType = 'watermark';
                break;
            case 'contents':
            case 'remark':
            case 'buyerMemo':
                $itemType = 'goodsinfo';
                break;
            default:
                $locationY = $value->top + ($orderCounter - 1) * $value->height;
                $itemType = 'goodsinfo';
                $orderCounter++;
                break;
        }
        return $itemType;
    }

    private static function getPrintOrderBoByFactoryOrder($orderInfo): PrintOrderBo
    {
        if (isJson($orderInfo['seller_memo'])) {
            $seller_memo = implode(',', json_decode($orderInfo['seller_memo']));
        } else {
            $seller_memo = $orderInfo['seller_memo'];
        }
        $printOrderBo = new PrintOrderBo();
        $printOrderBo->orderId = $orderInfo['id'];
        $printOrderBo->orderTime = $orderInfo['distr_at'];
        $printOrderBo->orderNo = $orderInfo['distr_oid'];
        $printOrderBo->sellerMemo = $seller_memo;
        $printOrderBo->buyerMemo = '';
        $printOrderBo->setShopName($orderInfo['distr_shop_name']);
        $printOrderBo->goodsName = $orderInfo['goods_title'];
        $printOrderBo->goodsNum = $orderInfo['goods_num'];
        $printOrderBo->goodsId = $orderInfo['goods_id'];
        $printOrderBo->outerId = $orderInfo['outer_goods_id'];
        $printOrderBo->skuId = $orderInfo['sku_id'];
        $printOrderBo->skuValue = $orderInfo['sku_value'];
        $printOrderBo->outerSkuId = $orderInfo['outer_sku_id'];
        $printOrderBo->payment = $orderInfo['goods_total_price'];
        $printOrderBo->totalPrice = $orderInfo['goods_total_price'];
        return $printOrderBo;
    }

    /**
     * 获取打印数据
     * @param $customConfig
     * @param Bo\PrintDataPackBo $printDataPackBo
     * @param array $contents
     * @param array $shopPrintConfig
     * @param int $packageCurrent
     * @param int $packageTotal
     * @return array
     * <AUTHOR>
     */
    public static function getPrintItems($customConfig, Bo\PrintDataPackBo $printDataPackBo, array $contents,
                                         array $shopPrintConfig, int $packageCurrent, int $packageTotal): array
    {
        $printItems = [];
        $orderCounter = 1;
        $printOrderBoArr = [];
        // 统计的配置
        $totalCustomKeyArr = [
            'count',
            'watermark',
            'numOfPackage',
            'contents',
        ];
        $totalCustomConfigArr = [];
        foreach ($customConfig as $value) {
            $fontWeight = $value->fontWeight ?? 'bold';
            $fontFamily = $value->fontFamily ?? 'simsun';
            $itemStyle = sprintf('{"fontSize":"%s","fontWeight":"%s","fontFamily":"%s"}', $value->fontSize, $fontWeight, $fontFamily);
            $customKey = self::getCustomKey($value->value);
            if (in_array($customKey, $totalCustomKeyArr)) {
                $totalCustomConfigArr[] = $value;
                continue;
            }
            foreach ($printDataPackBo->order_infos as $orderInfo) {
                $locationY = $value->top;
                if ($value->id == 18) {
                    $itemName = $value->value;
                    $itemType = 'goodsinfo';
                    $tip = '自定义';
                } else {
                    $printOrderBo = self::getPrintOrderBoByFactoryOrder($orderInfo);
                    $printOrderBoArr[] = $printOrderBo;
                    $itemName = self::getItemNameByFactoryOrder($customKey, $printOrderBo);
                    $itemType = self::getItemTypeByCustomKey($customKey, $value, $orderCounter, $locationY);
                    $tip = $customKey;
                }
                $printItems[] = [
                    'locationX' => $value->left,
                    'locationY' => $locationY,
                    'width' => $value->width,
                    'height' => $value->height,
                    'itemStyle' => $itemStyle,
                    'itemType' => $itemType,
                    'itemName' => $itemName,
                    'tip' => $tip,
                ];
            }
        }
        foreach ($totalCustomConfigArr as $index => $value) {
            $fontWeight = $value->fontWeight ?? 'bold';
            $fontFamily = $value->fontFamily ?? 'simsun';
            $itemStyle = sprintf('{"fontSize":"%s","fontWeight":"%s","fontFamily":"%s"}', $value->fontSize, $fontWeight, $fontFamily);
            $customKey = self::getCustomKey($value->value);
            $tip = $customKey;
            $itemName = self::getItemNameByPrintOrderBoArr($customKey, $printOrderBoArr, $contents, $shopPrintConfig, $packageCurrent, $packageTotal);
            $itemType = self::getItemTypeByCustomKey($customKey, $value, $orderCounter, $locationY);
            $printItems[] = [
                'locationX' => $value->left,
                'locationY' => $value->top,
                'width' => $value->width,
                'height' => $value->height,
                'itemStyle' => $itemStyle,
                'itemType' => $itemType,
                'itemName' => $itemName,
                'tip' => $tip,
            ];
        }
        return $printItems;
    }


    /**
     * 组装京东打印数据
     * @param $order
     * @param $hasNetPage
     * @param $template
     * @param $waybillHistory
     * @param string $orderIdStr
     * @param $sender
     * @param array $data
     * @param array $newPrintNextItemBeans
     * @return array
     */
    private static function getJDPrintData($order, $hasNetPage, $template, $waybillHistory, string $orderIdStr, $sender, array $data, array $newPrintNextItemBeans)
    {
        $customData = [];
        foreach ($data['printNextItemBeans'] as $item) {
            $customData[$item['tip']] = $item['itemName'];
        }
        $province = isset($sender['province']) ? $sender['province'] : $template['sender_province'];
        $city = isset($sender['city']) ? $sender['city'] : $template['sender_city'];
        $district = isset($sender['district']) ? $sender['district'] : $template['sender_district'];
        $detail = isset($sender['address']) ? $sender['address'] : $template['sender_address'];
        return [
            'is_jd' => 1,
            'express_code' => $template['wp_code'],
            'express_no' => array_get($waybillHistory, 'waybill_code', ''),
            'customData' => [$customData],
            'documentID' => handleOrderIdStr($order),
            'customTempUrl' => 'http://jd.kuaidixia.net/api/jd/customer?template_id=' . $template['id'],
            'tempUrl' => $template['template_url'],
            'printData' => [array_get($waybillHistory, 'print_data', '')],
            'addData' => [
                'sender' => [
                    'address' => $province . $city . $district . $detail,
                    'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                    'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                    'phone' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                ]
            ],
        ];
    }

    private static function getKSPrintData($order, $hasNetPage, $template, $waybillHistory, string $orderIdStr, $sender, array $data, array $newPrintNextItemBeans)
    {
        $printData = isset($waybillHistory['id']) ? json_decode($waybillHistory['print_data'], true) : $waybillHistory;
        $newData = [];
        foreach ($data['printNextItemBeans'] as $item) {
            if ($item['tip'] == '自定义') {
                $newData['custom'] = $item['itemName'];
            } else {
                $newData[$item['tip']] = $item['itemName'];
            }
        }

        //商品详情一张打不下，就不打了 抖音组件对模板有要求
        //if (!$hasNetPage) {
        return [
            'documentID' => $orderIdStr,
            'waybillCode' => $printData['waybill_code'],
            'ksOrderFlag' => true,
            'contents' => [
                [
                    "templateURL" => $template['template_url'],
                    "signature" => $printData['signature'],
                    "encryptedData" => $printData['print_data'],
                    'key' => $printData['key'],
                    'ver' => $printData['version'],
                    'addData' => [
                        'senderInfo' => [
                            'address' => [
                                'provinceName' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                'cityName' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                'districtName' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                'detailAddress' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                'town' => '',
                                'countryCode' => 'CHN'
                            ],
                            'contact' => [
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ]
                    ]
                ],
                [
                    'data' => array_merge($newData, ["printNextItemBeans" => $data['printNextItemBeans']]),
                    'customData' => $newData,
                    'templateURL' => env('APP_DOMAIN') . '/api/ks/customer?template_id=' . $template['id'].'&md5='.md5(json_encode($template)),
                ]
            ]
        ];
//        } else {
//            $result[] = [
//                'documentID' => $orderIdStr,
//                'waybillCode' => $printData['waybill_code'],
//                'ksOrderFlag' => true,
//                'contents' => [
//                    [
//                        "templateURL" => $template['template_url'],
//                        "signature" => $printData['signature'],
//                        "encryptedData" => $printData['print_data'],
//                        'key' => $printData['key'],
//                        'ver' => $printData['version'],
//                        'addData' => [
//                            'senderInfo' => [
//                                'address' => [
//                                    'provinceName' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
//                                    'cityName' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
//                                    'districtName' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
//                                    'detailAddress' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
//                                    'town' => '',
//                                    'countryCode' => 'CHN'
//                                ],
//                                'contact' => [
//                                    'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
//                                    'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
//                                    'phone' => '',
//                                ]
//                            ]
//                        ]
//                    ],
//                    [
//                        'data' => array_merge($newData, ["printNextItemBeans" => $data['printNextItemBeans']]),
//                        'customData' => $newData,
//                        'templateURL' => env('APP_DOMAIN').'/api/ks/customer?template_id='.$template['id']
//                    ]
//                ]
//            ];
//
//            unset($data['isNextPage']);
//            preg_match('/模板(\d+)\*(\d+)mm/', $template['waybill_type'], $ch);
//            $result[] = [
//                'documentID' => $orderIdStr,
//                'contents' => [
//                    [
//                        'data' => array_merge($newData, ["printNextItemBeans" => $data['printNextItemBeans']]),
//                        'customData' => $newData,
//                        'templateURL' => env('APP_DOMAIN').'/api/ks/customer?template_id='.$template['id']
//                    ]
//                ],
//            ];
//
//            return $result;
//        }
    }

    /**
     * 对SKU进行分组,
     * @param $item
     * @return string
     */
    static function skuGroupBy($item): string
    {
        return $item['num_iid'] . $item['sku_id'] . ($item['custom_sku_value'] ?? '') . ($item['outer_sku_iid'] ?? '');
    }

    /**
     * 把按SKU分组礼品的子订单放到最后
     * @return void
     */
    static function sortMergeOrderItemGroupGiftLast($mergeGoods): array
    {
        $sortedMergeGoods = [];
        foreach ($mergeGoods as $key => $v) {
            //如果是赠品就放到最后，反之放到最前
            //因为是按sku_id进行分组的，所以只要判断第一个商品的类型就可以了
            if ($v[0]['goods_type'] == 2) {
                $sortedMergeGoods[] = $v;
            } else {
                array_unshift($sortedMergeGoods, $v);
            }
        }
        return $sortedMergeGoods;
    }

    /**
     * 把子订单中的赠品放到最后
     * @param $mergeOrders
     * @return array
     */
    static function sortOrderItemGiftLast($mergeOrders): array
    {
        $mergeOrderItems = [];
        foreach ($mergeOrders as $key => $mergeOrder) {
            foreach ($mergeOrder['order_item'] as $k => $item) {
                $mergeOrderItems[] = $item;
            }
        }
        $sortedMergeOrderItems = [];
        foreach ($mergeOrderItems as $item) {
            if ($item['goods_type'] == 2) {
                $sortedMergeOrderItems[] = $item;
            } else {
                array_unshift($sortedMergeOrderItems, $item);
            }
        }
        \Log::info("子订单中的赠品放到最后的结果", $sortedMergeOrderItems);
        return $sortedMergeOrderItems;
    }

    /**
     * @param array $mergeOrders
     * @return array
     */
    public static function extractAndSortGoods(array $mergeOrders): array
    {
        $mergeGoods = [];
        foreach ($mergeOrders as $key => $mergeOrder) {
            foreach ($mergeOrder['order_item'] as $k => $item) {
                $mergeGoods[] = $item;
            }
        }
        //把商品按照sku分组
        return collect($mergeGoods)->groupBy(function ($item) {
            return self::skuGroupBy($item);
        })->toArray();
    }

    /**
     * 参考合并商品的顺序对订单进行排序
     * @param array $mergeOrders
     * @param array $newMergeGoods
     * @param $order
     * @return array
     */
    public static function sortMergeOrdersReferMergeGoods(array $mergeOrders, array $newMergeGoods, $order): array
    {
        $mergeOrdersKeyById = array_column($mergeOrders, null, 'id');
        $sortedMergeOrders = [];
        //按照宝贝的顺序把订单重新排列
        foreach ($newMergeGoods as $mergeGood) {
            foreach ($mergeGood as $item) {
                $orderId = $item['order_id'];
                $order = $mergeOrdersKeyById[$orderId] ?? null;
                if (isset($order)) {
                    $sortedMergeOrders[] = $order;
                    //把已经排列过的订单从待排列的订单中删除
                    unset($mergeOrdersKeyById[$orderId]);
                }

            }
        }
        return $sortedMergeOrders;
    }

    /**
     * 获取打印内容
     * @param $shopId
     * @return mixed
     */
    public static function getPrintContents($shopId)
    {
        $shopExtra = ShopExtra::query()->where('shop_id', $shopId)->first();
        if (empty($shopExtra->print_contents)) {
            $shopPrintContents = json_decode('{"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1","goodsLineFeed":"1","goodsPaging":"0","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"5","goodsMerge":"1"}', true);
        } else {
            $shopPrintContents = json_decode($shopExtra->print_contents, true);
        }
        return $shopPrintContents;
    }

    /**
     * 把自定义打印内容分成三组
     * @param array $customConfig
     * @return array
     */
    public static function splitItems(array $customConfig): array
    {
        $mergeItems = [];
        $noMergeItems = [];
        $customItems = [];
        foreach ($customConfig as $value) {
            $sample = collect(config('custom_area_contents'))
                ->where('value', $value->value)
                ->first();
            if ($sample['isMerge']) {
                $mergeItems[] = $value;
            } else if ($sample['id'] != 18) {
                $noMergeItems[] = $value;
            } else {
                $customItems[] = $value;
            }
        }
        return array($mergeItems, $noMergeItems, $customItems);
    }

    /**
     * 获取模板的自定义配置
     * @param $template
     * @param bool $isCustomOrder
     * @param $order
     * @return mixed
     */
    public static function getTemplateCustomConfig($template, bool $isCustomOrder, $order)
    {
        return json_decode($template['merge_template_url']);
//获取模板的配置项
//        //custom_config 和
//        $customConfig = json_decode($template['custom_config']);
//        //使用合单配置
//        if ((!$isCustomOrder && isset($order->mergeOrders) && count($order->mergeOrders) > 0) || (isset($order->orderItem) && count($order->orderItem) > 1)) {
//            if(!empty($template['merge_template_url'])) {
//                $customConfig = json_decode($template['merge_template_url']);
//            }else{
//                $customConfig = [];
//            }
//        }
//        $result = $customConfig ?? json_decode($template['custom_config']);
//        \Log::info("获取模板的自定义配置", [$template, $result]);
//        return $result;
    }

    /**
     * @param $order
     * @param array $mergeOrders
     * @return array
     */
    public static function extractMergeOrders($order, array $mergeOrders): array
    {
        $temp = $order->toArray();
        unset($temp['mergeOrders']);
        $mergeOrders[] = $temp;
        foreach ($order['mergeOrders'] as $item) {
            $mergeOrders[] = collect($item)->toArray();
        }
        return $mergeOrders;
    }
}
