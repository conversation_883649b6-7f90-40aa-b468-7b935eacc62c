<?php

namespace App\Services\Payment;

class PayOrderRequest
{

    public $mchid;

    public $openId;


    public $userId;

    public $userType;

    public $paySettingId;

    public $tradeType;

    public $description;

    public $orderAmount;

    public $actualAmount;


    public $fee;

    public $payClientIp;


    /**
     * @var string 业务订单号
     */
    public $businessOrderNo;
    /**
     * @var int 业务订单ID
     */
    public $businessOrderId;

    /**
     * @var string 平台支付单号，唯一标识一个支付请求
     */
    public $platformOrderNo;

    public $appId;

    /**
     * @var string 业务类型，例如：充值、提现等
     */
    public $businessType;


    public $orderStatus;

    public $refundStatus;

    /**
     * @var string 支付时间
     */
    public $payAt;

}
