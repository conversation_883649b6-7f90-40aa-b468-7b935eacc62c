<?php

namespace App\Services\PlatformOrder\Impl;

use App\Constants\PlatformConst;
use App\Models\Shop;
use App\Services\PlatformOrder\AbstractPlatformOrderService;

class WxspPlatformOrderImpl extends AbstractPlatformOrderService
{
    protected $platformType = PlatformConst::PLATFORM_TYPE_WX;

    public function formatToOrder(array $order): array
    {
        $shop = $this->getShop();
        foreach ($order as $key => $item) {
            // 计算订购天数 多次订购计算天数特殊(取上一次订购的过期时间)
            if ($key == 0) {
                $duration = floor((strtotime($item['expire_time']) - strtotime($item['create_time'])) / 86400);
            } else {
                $duration = floor((strtotime($item['expire_time']) - strtotime($startTime)) / 86400);
            }
            $data[] = [
                'order_id' => $item['service_order_id'],
                'order_no' => $item['service_order_id'],
                'status' => $item['status'],
                'service_id' => $item['service_id'],
                'service_name' => $item['service_name'],
                'fee' => $item['total_price'],
                'pay_fee' => $item['total_price'],
                'pay_at' => $item['create_time'],
                'order_created_at' => $item['create_time'],
                'cycle_start_at' => $item['create_time'],
                'cycle_end_at' => $item['expire_time'],
                'sku_title' => '专业版' . $duration . '天',
                'sku_spec' => '专业版',
                'duration' => $duration,
                'platform_type' => PlatformConst::PLATFORM_TYPE_WX,
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
                'auth_user_id' => $shop->auth_user_id,
            ];
            $startTime = $item['expire_time'];
        }

        return $data;
    }

    public function getVersionDesc(): array
    {

    }
}
