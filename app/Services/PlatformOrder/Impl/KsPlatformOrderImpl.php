<?php

namespace App\Services\PlatformOrder\Impl;

use App\Constants\PlatformConst;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\PlatformOrder\AbstractPlatformOrderService;

class KsPlatformOrderImpl extends AbstractPlatformOrderService
{
    protected $platformType = PlatformConst::PLATFORM_TYPE_KS;

    protected $orderStatusMap
        = [
            0 => PlatformOrder::STATUS_UNKNOWN,
            1 => PlatformOrder::STATUS_PADDING,
            4 => PlatformOrder::STATUS_CLOSE,
            5 => PlatformOrder::STATUS_SUCCESS,
        ];

    protected $versionMap= [
        UserExtra::VERSION_FREE         => '试用版',
        UserExtra::VERSION_STANDARD     => '标准版',
        UserExtra::VERSION_PROFESSIONAL => '专业版',
        UserExtra::VERSION_SENIOR       => '高级版',
    ];


    public function formatToOrder(array $order): array
	{
        switch ($order['skuInfo']['period']){
            case 1:
                $duration = 7;
                $durationUnit = 0;
                $endTime = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)).' + 7 day'));
                break;
            case 2:
                $duration = 1;
                $durationUnit = 1;
                $endTime = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)).' + 30 day'));
                break;
            case 3:
                $duration = 3;
                $durationUnit = 1;
                $endTime = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)).' + 90 day'));
                break;
            case 4:
                $duration = 6;
                $durationUnit = 1;
                $endTime = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)).' + 180 day'));
                break;
            case 5:
                $duration = 1;
                $durationUnit = 2;
                $endTime = date('Y-m-d H:i:s', strtotime(date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)).' + 360 day'));
                break;
            default:
                $duration = 0;
                $durationUnit = 0;
                $endTime = "";
                break;
        }
        //根据buyOpenId查询以前是否有订购 拿到店铺信息
        $shop = Shop::query()->where('auth_user_id', $order['buyerOpenId'])->first();
        $data = [
            'auth_user_id' => $order['buyerOpenId'],
            'order_id' => $order['oid'],
            'order_no' => $order['oid'],
            'status' => $order['status'],
            'service_id' => $order['serviceId'],
            'service_name' => $order['serviceName'],
            'fee' => $order['orderTotalFee'],
            'pay_fee' => $order['paymentFee'],
            'pay_at' => date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)),
            'order_created_at' => date('Y-m-d H:i:s', substr($order['submitTime'], 0, 10)),
            'order_cycle' => $order['skuInfo']['period'],
            'cycle_start_at' => date('Y-m-d H:i:s', substr($order['payTime'], 0, 10)),
            'cycle_end_at' => $endTime,
            'sku_id' => $order['skuId'],
            'sku_title' => $order['skuInfo']['packageName'],
            'sku_spec' => $order['skuInfo']['periodDesc'],
            'duration' => $duration,
            'duration_unit' => $durationUnit,
            'platform_type' => PlatformConst::PLATFORM_TYPE_KS,
            'user_id' => !empty($shop) ? $shop->user_id : 0,
            'shop_id' => !empty($shop) ? $shop->id : 0,
            'identifier' => !empty($shop) ? $shop->identifier : 0,
        ];

        return $data;
	}

    public function getVersionDesc()
    : array
    {
        $shop  = $this->getShop();
        $order = PlatformOrder::calcuMaxLevel($shop);

        return array_merge(collect($order)->toArray(), [
            'version' => array_search($order['sku_spec'], $this->versionMap)
        ]);
    }
}
