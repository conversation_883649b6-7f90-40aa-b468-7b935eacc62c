<?php

namespace App\Services\Warehouse;


use App\Models\Address;

/**
 * 云仓汇总报表
 */
class WarehouseSummaryReport extends UsageCommon
{

    /**
     * 店铺日统计项
     * @var WarehouseSummaryShopDailyItem[] $items
     */
    public $items = [];

    public $goodsNames = [];
    /**
     * 云仓默认的wpCode
     * @var array
     */
    public $defaultWpCodes = [];


    /**
     * @var int $mergeOrderNum 合并
     */
    public $mergeOrderNum = 0;

    /**
     * 仓库名称
     * @var string $warehouseName
     */
    public $warehouseName;

    /**
     * @var string 产品名称
     */
    public $productName;

    /**
     * 加收快递的省份地址
     * @var Address[] $waybillExtraProvinceCityAddress
     */
    public $waybillExtraProvinceCityAddress = [];


    public $waybillExtraProvinceCityCodes = [];

    /**
     *  新疆西藏省份地址
     * @var Address[] $xinJiangXiZangProvinceAddress
     */
    public $xinJiangXiZangProvinceAddress;

    /**
     * @var string 店铺名称
     */

    public $shopName;

    /**
     * @var int 合计货品数
     */
    public $goodsAmount;


    /**
     * @var int 合计包裹数
     */
    public $packageNum;


    /**
     * 加收区域的列名
     * @var array
     */
    public $waybillExtraProvinceColumns = [];

    /**
     * 新疆西藏的列名
     * @var array
     */
    public $xinJiangXiZangProvinceColumns = [];

    /**
     * 别外快递的列名
     * @var array
     */
    public $wpCodeExtraColumns = [];


    /**
     * 包裹数量合计
     * @return int
     */

    public function sumPackageNum(): int
    {
        $sum = 0;
        foreach ($this->items as $item) {
            $sum += $item->packageNum;
        }
        return $sum;
    }

    /**
     * 货品数量合计
     * @return int
     */
    public function sumGoodsAmount(): int
    {
        $sum = 0;
        foreach ($this->items as $item) {
            $sum += $item->goodsAmount;
        }
        return $sum;
    }

    /**
     * 获取店铺日统计项
     * @param int $shopId
     * @param string $date
     * @param string $shopName
     * @return void
     */
    public function getShopDailyItemInstance(int $shopId, string $date, string $shopName): WarehouseSummaryShopDailyItem
    {
        //比较现有的店铺日统计项，如果没有则创建，如果有则返回
        foreach ($this->items as $item) {
            if ($item->shopId == $shopId && $item->date == $date) {
                return $item;
            }
        }
        $waybillHistorySummaryShopDailyItem = new WarehouseSummaryShopDailyItem();
        $waybillHistorySummaryShopDailyItem->shopId = $shopId;
        $waybillHistorySummaryShopDailyItem->date = $date;
        $waybillHistorySummaryShopDailyItem->shopName = $shopName;
        $this->items[] = $waybillHistorySummaryShopDailyItem;

        return $waybillHistorySummaryShopDailyItem;
    }

    /**
     * 获取店铺商品统计项
     * @param string $goodsName
     * @param int $goodsNum
     * @param string $date
     * @param int $shopId
     * @param string $shopName
     * @return WarehouseSummaryShopGoodItem
     */

    public function getWarehouseSummaryShopGoodItemInstance(string $goodsName, int $goodsNum, string $date, int $shopId, string $shopName): WarehouseSummaryShopGoodItem
    {
        return $this->getShopDailyItemInstance($shopId, $date, $shopName)->getWarehouseSummaryShopGoodItemInstance($goodsName, $goodsNum);
    }

    /**
     * 判断是不是加收区域,包括了普通加收和新疆西藏加收
     * @param int $provinceCode
     * @return ?Address
     */
    public function matchWaybillExtraProvinceAddress(int $provinceCode): ?Address
    {
        foreach ($this->waybillExtraProvinceCityAddress as $address) {
            if ($address->code == $provinceCode) {
                return $address;
            }
        }
        foreach ($this->xinJiangXiZangProvinceAddress as $address) {
            if ($address->code == $provinceCode) {
                return $address;
            }
        }

        return null;
    }

    /**
     * 生成数据
     * @return void
     */
    public function build()
    {
        /**
         * 把每个店铺
         */
        foreach ($this->items as $item) {
            $item->build($this);
            //统计加收区域的使用量
            foreach ($item->waybillExtraProvinceUsage as $provinceCode => $num) {
                if (isset($this->waybillExtraProvinceUsage[$provinceCode])) {
                    $this->waybillExtraProvinceUsage[$provinceCode] += $num;
                } else {
                    $this->waybillExtraProvinceUsage[$provinceCode] = $num;
                }
                $this->sumWaybillExtraProvinceUsage += $num;
            }

            //统计别外快递的使用量
            foreach ($item->wpCodeExtraUsage as $wpCode => $num) {

                if (isset($this->wpCodeExtraUsage[$wpCode])) {
                    $this->wpCodeExtraUsage[$wpCode] += $num;
                } else {
                    $this->wpCodeExtraUsage[$wpCode] = $num;
                }
                if (in_array($wpCode, $this->defaultWpCodes)) {
                    continue;
                }
                $this->sumWpCodeExtraUsage += $num;
            }
            //统计新疆西藏的使用量
            foreach ($item->xinJiangXiZangProvinceUsage as $provinceCode => $num) {
                if (isset($this->xinJiangXiZangProvinceUsage[$provinceCode])) {
                    $this->xinJiangXiZangProvinceUsage[$provinceCode] += $num;

                } else {
                    $this->xinJiangXiZangProvinceUsage[$provinceCode] = $num;
                }
                $this->sumXinJiangXiZangProvinceUsage += $num;
            }

        }
        $this->packageNum = $this->sumPackageNum();
        $this->goodsAmount = $this->sumGoodsAmount();
        $this->wpCodeExtraColumns = array_keys($this->wpCodeExtraUsage);


    }

    /**
     * 导出文件名
     * 仓库
     * @return string
     */
    public function getExportFileName(): string
    {
        \Log::info("生成文件名", [$this]);
        $fileName = $this->warehouseName . '+';
        $fileName = $fileName . $this->packageNum . '单';
        if ($this->sumWaybillExtraProvinceUsage > 0) {
            $fileName = $fileName . '+加收' . $this->sumWaybillExtraProvinceUsage . '单';
        }
        if ($this->sumXinJiangXiZangProvinceUsage > 0) {
            $fileName = $fileName . '+新疆西藏' . $this->sumXinJiangXiZangProvinceUsage . '单';
        }
        if (!empty($this->wpCodeExtraUsage)) {
            foreach ($this->wpCodeExtraUsage as $wpName => $num) {
                $fileName = $fileName . '+' . $wpName . $num . '单';
            }
        }
        if ($this->mergeOrderNum > 0) {
            $fileName = $fileName . '+合单';
        }
        $fileName = $fileName . '+' . implode("+", $this->goodsNames);
        return $fileName . '+' . $this->shopName;
    }

    /**
     * 添加产品名称
     * @param $productName
     * @return void
     */
    public function addProductName($productName)
    {
        $this->goodsNames[] = $productName;
    }
}
