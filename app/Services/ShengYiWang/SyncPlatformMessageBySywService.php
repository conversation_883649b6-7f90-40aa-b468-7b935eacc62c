<?php

namespace App\Services\ShengYiWang;

use App\Jobs\DoudianMsg\DyMsgServiceManagerJob;
use App\Jobs\JdMsg\JdSaveOrUpdateOrderMessageJob;
use App\Jobs\KsMsg\KsMsgServiceManagerJob;
use App\Jobs\PddMsg\PddMsgServiceManagerJob;
use App\Jobs\TbMsg\TbMsgServiceManagerJob;
use App\Utils\ArrayUtil;
use Illuminate\Support\Facades\Log;

/**
 * 同步平台信息
 */
class SyncPlatformMessageBySywService
{
    /**
     * @throws \Exception
     * ，*/
    public function sync(?string $start, ?string $end, ?int $status = null, ?int $page = null, int $pageSize = 1000)
    {
        $client = new ShengYiWangClient();
        $params = [
            'pageSize' => $pageSize
        ];
        if ($start) {
            $params['start'] = $start;
        }
        if ($end) {
            $params['end'] = $end;
        }
        if (isset($status)) {
            $params['state'] = $status;
        }

        $fetchAll = true;
        if ($page) {
            $params['page'] = $page;
            $fetchAll = false;
        } else {
            $page = 1;
        }

        while (true) {
            $markProcessed = false;
            $response = $client->execute('POST', '/third/print/open/message/get', $params);
            $client->handleResponse($response);
            $records = $response['data']['records'];
            Log::info('获取生意网推送的消息', ["param"=>$params,"totalCount"=>array_get($response, 'data.totalCount', 0)]);
            $pageCount = sizeof($records);
            $recordsGroupByChannelType = ArrayUtil::array_group_by($records, 'channelType');
            //打印每个渠道的消息数量,channel_type
            $successProcessedIds = [];
            foreach ($recordsGroupByChannelType as $channelType => $channelRecords) {
                Log::info("渠道类型:".$channelType."数量:".sizeof($channelRecords));
                if ($channelType == 'TIKTOK') {
                    $channelProcessedIds = $this->syncDyMessage($channelRecords);
                    $successProcessedIds = array_merge($successProcessedIds, $channelProcessedIds);
                }
                if ($channelType == 'JD') {
                    $channelProcessedIds = $this->syncJdMessage($channelRecords);
                    $successProcessedIds = array_merge($successProcessedIds, $channelProcessedIds);
                }
                if ($channelType == 'TAOBAO') {
                    $channelProcessedIds=$this->syncTaobaoMessage($channelRecords);
                    $successProcessedIds = array_merge($successProcessedIds, $channelProcessedIds);

                }
                if ($channelType == 'KUAISHOU') {
                    $channelProcessedIds=$this->syncKsMessage($channelRecords);
                    $successProcessedIds = array_merge($successProcessedIds, $channelProcessedIds);
                }
                if ($channelType == 'PDD') {
                    $channelProcessedIds=$this->syncPddMessage($channelRecords);
                    $successProcessedIds = array_merge($successProcessedIds, $channelProcessedIds);
                }
            }
            if(!empty($successProcessedIds)){
                Log::info("处理完成",$successProcessedIds);
                $this->markProcessed($successProcessedIds);
                $markProcessed=true;
            }

            //如果有分页参数，则设置分页参数，表示取特定的一行，如果没有分页参数，就表示取所有行，就是要for循环到分页结束

//            if (!$fetchAll) {
////                $continue = false;
//            } else {
            if ($pageCount < $pageSize) {
                //取光了就sleep
//                $continue = false;\
                Log::info("没有待处理的任务");
                sleep(5);
            }
        }


    }

    private function syncDyMessage($channelRecords): array
    {
        $successDealedIds = [];
        foreach ($channelRecords as $channelRecord) {
            try {
                $messageContent = json_decode($channelRecord['messageContent']);
                $message = [];
                $message['tag'] = intval($channelRecord['messageType']);
                $message['data'] = $messageContent;
                $dyMsgServiceManagerJob = new DyMsgServiceManagerJob($message);
                $dyMsgServiceManagerJob->handle();
                $successDealedIds[] = $channelRecord['id'];
            } catch (\Throwable $ex) {
                Log::error("同步抖音消息异常", ["trace" => $ex->getTraceAsString(), "msg" => $channelRecord]);
            }

        }
        return $successDealedIds;

    }

    private function syncJdMessage($channelRecords): array
    {
        $successDealedIds = [];
        foreach ($channelRecords as $channelRecord) {
            try {
                $messageBody = json_decode($channelRecord['messageContent'], true);
                $data = [
                    "serviceId" => array_get($messageBody, "venderId"), "tid" => array_get($messageBody, "orderId")
                ];
                $jdMsgServiceManagerJob = new JdSaveOrUpdateOrderMessageJob($data);
                $jdMsgServiceManagerJob->handle();
                $successDealedIds[] = $channelRecord['id'];
            } catch (\Throwable $ex) {
                Log::error("同步京东消息异常",
                    ["msg" => $ex->getMessage(), "trace" => $ex->getTraceAsString(), "record" => $channelRecord]);
            }
        }
        return $successDealedIds;
    }

    private function syncTaobaoMessage($channelRecords): array
    {
        $successDealedIds = [];
        foreach ($channelRecords as $channelRecord) {
            try {
                $messageType = $channelRecord['messageType'];
                $messageBody = json_decode($channelRecord['messageContent'], true);
                $messageBody['event'] = $messageType;
                $messageBody['identifier'] = $channelRecord['shopId'];
                $tbMsgServiceManagerJob = new TbMsgServiceManagerJob($messageBody);
                $tbMsgServiceManagerJob->handle();

                $successDealedIds[] = $channelRecord['id'];
            } catch (\Throwable $ex) {
                Log::error("同步淘宝消息异常",
                    ["msg" => $ex->getMessage(), "trace" => $ex->getTraceAsString(), "record" => $channelRecord]);
            }
        }
        return $successDealedIds;
    }

    private function syncKsMessage($channelRecords): array
    {
        $successDealedIds = [];
        foreach ($channelRecords as $channelRecord) {
            try {
                $messageBody = json_decode($channelRecord['messageContent'], true);
//                $data = ["serviceId" => array_get($messageBody, "venderId"), "tid" => array_get($messageBody, "orderId")];
                $ksMsgServiceManagerJob = new KsMsgServiceManagerJob($messageBody);
                $ksMsgServiceManagerJob->handle();
                $successDealedIds[] = $channelRecord['id'];
            } catch (\Throwable $ex) {
                Log::error("同步KS消息异常",
                    ["msg" => $ex->getMessage(), "trace" => $ex->getTraceAsString(), "record" => $channelRecord]);
            }
        }
        return $successDealedIds;
    }

    private function syncPddMessage($channelRecords): array
    {
        $successDealedIds = [];
        foreach ($channelRecords as $channelRecord) {
            try {
                $messageType = $channelRecord['messageType'];
                $messageBody = json_decode($channelRecord['messageContent'], true);
                $messageBody['event'] = $messageType;
                $pddMsgServiceManagerJob = new PddMsgServiceManagerJob($messageBody);
                $pddMsgServiceManagerJob->handle();
                $successDealedIds[] = $channelRecord['id'];

            } catch (\Throwable $ex) {
                Log::error("同步拼多多消息异常",
                    ["msg" => $ex->getMessage(), "trace" => $ex->getTraceAsString(), "record" => $channelRecord]);
            }
        }
        return $successDealedIds;

    }

    /**
     * 标识消息已经处理
     * @param  array  $ids
     * @return void
     */
    public function markProcessed(array $ids)
    {
        try {
            $client = new ShengYiWangClient();
            $client->execute("POST", '/third/print/open/message/close', ['ids' => $ids]);
            Log::info("标识消息已经处理成功", ["ids" => $ids]);
        } catch (\Throwable $ex) {
            Log::error("标识消息已经处理异常",
                ["msg" => $ex->getMessage(), "trace" => $ex->getTraceAsString(), "ids" => $ids]);
        }
    }

}
