<?php

namespace App\Services\Shop;

use App\Models\ShopBind;

/**
 * 店铺绑定的服务
 */
class ShopBindService
{



    /**
     * 从厂家这边绑定关系
     * 先从缓存里面找，如果没有表示没有建立关系，
     * @param int $factoryId
     * @param int $shopId
     * @return mixe
     */
    public function bindAgentPrintFromFactory2Shop(int $factoryId ,int $shopId ){

        $redis = redis('cache');
        $key = $this->getFactoryAgentShopKey($factoryId);
        $value = $redis->hget($key, $shopId);
        if($value){
            \Log::info("缓存了厂家和商家关系");
            return ;
        }
        ShopBind::bindAgentPrintFromFactory2Shop($factoryId,$shopId);
        $redis->hset($key, $shopId,1);

    }

    /**
     * 获取厂家代理的商家的店铺缓存的KEY
     * @param int $factoryId
     * @return string
     */
    protected function getFactoryAgentShopKey(int $factoryId): string
    {
        $key = "factory-shop-agent-print-" . $factoryId;
        return $key;
    }
}
