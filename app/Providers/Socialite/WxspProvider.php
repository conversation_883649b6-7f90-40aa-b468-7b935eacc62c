<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:43
 */

namespace App\Providers\Socialite;


use App\Services\Auth\Impl\WxAuthImpl;
use App\Services\Client\WxClient;
use App\Utils\OauthUtil;
use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\ProviderInterface;
use Overtrue\Socialite\Providers\AbstractProvider;
use Overtrue\Socialite\User;

/**
 * Class KsProvider
 * @package App\Providers\Socialite
 * @see https://docs.qq.com/doc/DQ3hyU3F4b0p6TENX
 * @see https://docs.qq.com/doc/DUklHZ05lUG12aURq
 */
class WxspProvider extends WxProvider
{
    /**
     * @inheritDoc
     */
    protected function getAuthUrl($state)
    {
        return 'https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2';
    }
}
