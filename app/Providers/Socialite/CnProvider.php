<?php

/*
 * This file is part of the overtrue/socialite.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace App\Providers\Socialite;

use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\User;

/**
 * Class TaobaoProvider.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see    https://open.taobao.com/doc.htm?docId=102635&docType=1&source=search [Taobao - OAuth 2.0 授权登录]
 */
class CnProvider extends  TaobaoProvider
{
    protected $stateless = true;

    public  function getAuthUrl($state): string
    {
        return parent::getAuthUrl($state); // TODO: Change the autogenerated stub
    }




//    /**
//     * Get the access token for the given code.
//     *
//     * @param string $code
//     *
//     * @return array
//     */
//    public function getAccessToken($code)
//    {
//        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
//            'query' => $this->getTokenFields($code),
//        ]);
//
//        $res = json_decode($response->getBody(), true);
//
//        if (empty($res['access_token'])) {
//            throw new AuthorizeFailedException('Authorize Failed: ' . json_encode($res, JSON_UNESCAPED_UNICODE), $res);
//        }
//        return $res;
//    }


//    /**
//     * Map the raw user array to a Socialite User instance.
//     *
//     * @param array $user
//     *
//     * @return User
//     */
//    protected function mapUserToObject(array $user)
//    {
//        $name = $this->arrayItem($user, 'taobao_user_nick');
//        $name = urldecode($name);
//
//        return new User([
//            'id' => $this->arrayItem($user, 'taobao_user_id'),
//            'nickname' => $name,
//            'username' => $name,
//            'name' => $name,
//            'avatar' => '',
//        ]);
//    }





    public  function getCodeFields($state = null): array
    {
        $fields = [
            'client_id' => $this->getConfig()->get('client_id'),
            'redirect_uri' => $this->redirectUrl,
            'view' => $this->view,
            'response_type' => 'code',
        ];

        if ($this->state) {
            $fields['state'] = $this->state;
        }
        return $fields;
    }

    protected function getTokenUrl(): string
    {
        return "https://oauth.taobao.com/token";
    }

    /**
     * @param AccessTokenInterface $token
     * @return array
     */
    protected function getUserByToken(AccessTokenInterface $token): array
    {
        return [];
    }

    protected function mapUserToObject(array $user)
    {
        return new User([
            'id' => $user['shop_id'] ?? '',
            'nickname' => $user['shop_name'] ?? '',
            'username' => $user['name'] ?? '',
            'name' => $user['shop_name'] ?? '',
            'avatar' => '',
        ]);
    }

    public function getAccessToken($code)
    {
        $tokenFields = $this->getTokenFields($code)+ ['grant_type' => 'authorization_code', 'view' => $this->view];
//        var_dump($tokenFields);
        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'query' => $tokenFields
        ]);

        return $this->parseAccessToken($response->getBody());
    }




//    protected function getTokenFields(string $code): array
//    {
//        $fields = ['client_id' => $this->getConfig()->get('client_id'),
//            'client_secret' => $this->getConfig()->get('client_secret'),
////            'code' => $code,
//            'redirect_uri' => $this->redirectUrl,
//            'grant_type' => 'authorization_code', 'view' => "web"];
//        var_dump($fields);
//        return $fields;
//    }


}
