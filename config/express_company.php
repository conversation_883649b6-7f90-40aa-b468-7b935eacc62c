<?php

/**
 * pdd + cnLnk 支持的快递公司
 *
 * type 0-公用  1-pdd  2-tb 3-dy
 *
 */

use App\Constants\LogisticsConst;

return [
    [
        "name" => "顺丰快递",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ],
    [
        "name" => "邮政EMS",
        "wpCode" => "EMS",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "天地华宇",
        "wpCode" => "HOAU",
        "unionWpCode" => LogisticsConst::HOAU,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-11-22/fd2e1b82-62d0-4f3d-a50c-889f69a8264a.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "韵达快递",
        "type" => \App\Models\Company::TYPE_PDD,
        "wpCode" => "YUNDA",
        "unionWpCode" => LogisticsConst::YUNDA,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "百世快运",
        "wpCode" => "BESTQJT",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-08-19/d8953929-5916-4fb1-b17c-c98245ad2c23.png"
    ],
    [
        "name" => "宅急送快递",
        "wpCode" => "ZJS",
        "unionWpCode" => LogisticsConst::ZJS,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-04-04/359c0517-7958-43d8-b922-4ddec7d910d4.jpg"
    ],
    [
        "name" => "百世快递",
        "wpCode" => "HT",
        "unionWpCode" => LogisticsConst::BESTKD,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/7ef6ea0e08037a6dd287111bb496a710.png"
    ],

    [
        "name" => "天天快递",
        "wpCode" => "TT",
        "unionWpCode" => LogisticsConst::TT,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/e5366dca534d278c1568e587c613b054.png"
    ],
    [
        "name" => "亚风速递",
        "wpCode" => "AIR",
        "unionWpCode" => LogisticsConst::AIR,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-18/f07a8899-cbca-48a3-8112-bb5c232f521b.jpeg"
    ],
    [
        "name" => "优速快递",
        "wpCode" => "YS",
        "unionWpCode" => LogisticsConst::UC,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-04-15/b0c167b31b9036a63bf6e466e5d3ca8f.png"
    ],
    [
        "name" => "日日顺物流",
        "wpCode" => "RRS",
        "unionWpCode" => LogisticsConst::RRS,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-06-05/187265fcc9dce854c713ea7ced048205.png"
    ],
    [
        "name" => "韵达快运",
        "wpCode" => "YDKY",
        "unionWpCode" => LogisticsConst::YDKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-06-26/e8b9f6f1f26b3c820693428e6dbc6f0e.png"
    ],
    [
        "name" => "邮政快递包裹",
        "wpCode" => "YZXB",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ],
    [
        "name" => "跨越速运",
        "wpCode" => "KYE",
        "unionWpCode" => LogisticsConst::KYSY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-03-11/a1c127c684310a99db41c7dc1b419640.png"
    ],
    [
        "name" => "D速物流",
        "wpCode" => "SDSD",
        "unionWpCode" => LogisticsConst::SDSD,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-20/d215bf9c-acab-428f-8aa1-5c5e1531af79.jpeg"
    ],
    [
        "name" => "中通快运",
        "wpCode" => "ZTOKY",
        "unionWpCode" => LogisticsConst::ZTOKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-26/21ec9b18-7e53-4909-9197-f13e8710c21a.jpg"
    ],
    [
        "name" => "德邦快递",
        "wpCode" => "DB",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-20/f229de71-2ea4-44fd-bff2-deff56bdb2b2.jpeg"
    ],
    [
        "name" => "安能快运",
        "wpCode" => "ANKY",
        "unionWpCode" => LogisticsConst::ANKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "http://img.alicdn.com/tfscom/TB1SwUQHNWYBuNjy1zkXXXGGpXa.jpg_200x200xz"
    ],
    [
        "name" => "承诺达特快",
        "wpCode" => "OTP",
        "unionWpCode" => LogisticsConst::OTP,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-04-12/2426ae7e9774eff18b5f0a0dc75a3b28.png"
    ],
    [
        "name" => "安迅物流",
        "wpCode" => "AXWL",
        "unionWpCode" => LogisticsConst::AXWL,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-04-17/d49709f1b15a4adc827470d413e45dd0.png"
    ],
    [
        "name" => "京广速递",
        "wpCode" => "SZKKE",
        "unionWpCode" => LogisticsConst::SZKKE,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-05-07/dbb58a82dbef8faecacd8400f6e7d6d6.png"
    ],
    [
        "name" => "顺心捷达",
        "wpCode" => "SXJD",
        "unionWpCode" => LogisticsConst::SXJD,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-05-23/b48fc63421d8efcea7c1dbc49f49dec1.png"
    ],
    [
        "name" => "京东配送",
        "wpCode" => "JD",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-10-08/e7c2b6df-10f4-495d-b2ab-6781b6347343.png"
    ],
    [
        "name" => "德邦物流",
        "wpCode" => "DEBANGWULIU",
        "unionWpCode" => LogisticsConst::DBKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-03-16/538bb3ef-166d-4929-9a2b-e4f5c414b230.png"
    ],
    [
        "name" => "顺丰快运",
        "wpCode" => "SFKY",
        "unionWpCode" => LogisticsConst::SFKY,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-10-16/ce6f0035-ca91-451c-9965-9deff0578e12.png"
    ],
    [
        "name" => "中通国际",
        "wpCode" => "ZTOINTER",
        "unionWpCode" => LogisticsConst::ZTOGJ,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-12-08/c005f925-61cd-4fe2-a551-7572b808375c.png"
    ],
    [
        "name" => "韵达国际",
        "wpCode" => "YDGJ",
        "unionWpCode" => LogisticsConst::YDGJ,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-12-08/13f7b598-6352-4d5c-a16d-56f34baec682.png"
    ],
    [
        "name" => "申通国际",
        "wpCode" => "STOINTER",
        "unionWpCode" => LogisticsConst::STOGJ,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-26/dbea9320-0d6e-4187-97ba-15f4988a390d.png"
    ],
    [
        "name" => "九曳供应链",
        "wpCode" => "JIUYE",
        "unionWpCode" => LogisticsConst::JIUYE,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-12-29/3684c82a-c7d9-4960-b247-dc696d1e1755.jpg"
    ],
    [
        "name" => "高捷物流",
        "wpCode" => "GJ",
        "unionWpCode" => LogisticsConst::GJWL,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-03-15/d9c1f187-698a-469c-b00c-409674dba406.jpg"
    ],
    [
        "name" => "极兔速递",
        "wpCode" => "JTSD",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ],
    [
        "name" => "丰网速运",
        "wpCode" => "FENGWANG",
        "unionWpCode" => LogisticsConst::FENGWANG,
        "type" => \App\Models\Company::TYPE_PDD,
        "logo" => "https://funimg.pddpic.com/2021-01-13/dade87f1-f36d-4afc-b932-019892283317.png"
    ],
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    [
        "name" => "顺丰快递",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ],
    [
        "name" => "邮政电商标快",
        "wpCode" => "EYB",
        "unionWpCode" => LogisticsConst::EYB,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.pddpic.com/a/package-advertising/2bfa06d7-1b1f-4df2-b1be-4b887cb56bdb.jpg"
    ],
    [
        "name" => "邮政EMS",
        "wpCode" => "EMS",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "天地华宇",
        "wpCode" => "HOAU",
        "unionWpCode" => LogisticsConst::HOAU,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-11-22/fd2e1b82-62d0-4f3d-a50c-889f69a8264a.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "韵达快递",
        "type" => \App\Models\Company::TYPE_TB,
        "wpCode" => "YUNDA",
        "unionWpCode" => LogisticsConst::YUNDA,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "百世快运",
        "wpCode" => "BESTQJT",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-08-19/d8953929-5916-4fb1-b17c-c98245ad2c23.png"
    ],
    [
        "name" => "宅急送快递",
        "wpCode" => "ZJS",
        "unionWpCode" => LogisticsConst::ZJS,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-04-04/359c0517-7958-43d8-b922-4ddec7d910d4.jpg"
    ],
    [
        "name" => "韵达快运",
        "wpCode" => "CN7000001021040",
        "unionWpCode" => LogisticsConst::YDKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-06-26/e8b9f6f1f26b3c820693428e6dbc6f0e.png"
    ],
    [
        "name" => "邮政快递包裹",
        "wpCode" => "POSTB",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ],
    [
        "name" => "跨越速运",
        "wpCode" => "CN7000001003751",
        "unionWpCode" => LogisticsConst::KYSY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-03-11/a1c127c684310a99db41c7dc1b419640.png"
    ],
    [
        "name" => "递速物流",
        "wpCode" => "*********",
        "unionWpCode" => LogisticsConst::DSWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/tfscom/T1XO_1XhRjXXb1upjX.jpg"
    ],
    [
        "name" => "速尔快递",
        "wpCode" => "SURE",
        "unionWpCode" => LogisticsConst::SURE,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i3/1065192055/T2m711Xi8XXXXXXXXX_!!1065192055.jpg"
    ],
    [
        "name" => "丹鸟",
        "wpCode" => "CP570969",
        "unionWpCode" => LogisticsConst::DANNIAO,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1daEjXAxz61VjSZFtXXaDSVXa.png_200x200xz"
    ],
    [
        "name" => "安能快递",
        "wpCode" => "2608021499_235",
        "unionWpCode" => LogisticsConst::ANKD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1FqUJHH9YBuNjy0FgXXcxcXXa.jpg_200x200xz"
    ],
    [
        "name" => "圆通承诺达",
        "wpCode" => "CP468398",
        "unionWpCode" => LogisticsConst::YTOCND,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tps/i3/T1Z.V2FS8bXXc_d6ve-160-80.jpg"
    ],
    [
        "name" => "快捷快递",
        "wpCode" => "FAST",
        "unionWpCode" => LogisticsConst::FAST,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tps/i3/T1Z.V2FS8bXXc_d6ve-160-80.jpg"
    ],
    [
        "name" => "中通快运",
        "wpCode" => "3108002701_1011",
        "unionWpCode" => LogisticsConst::ZTOKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-26/21ec9b18-7e53-4909-9197-f13e8710c21a.jpg"
    ],
    [
        "name" => "德邦快递",
        "wpCode" => "DBKD",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-03-20/f229de71-2ea4-44fd-bff2-deff56bdb2b2.jpeg"
    ],
    [
        "name" => "安能快运",
        "wpCode" => "CN7000001000869",
        "unionWpCode" => LogisticsConst::ANKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1SwUQHNWYBuNjy1zkXXXGGpXa.jpg_200x200xz"
    ],
    [
        "name" => "顺心捷达",
        "wpCode" => "CP471906",
        "unionWpCode" => LogisticsConst::SXJD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-05-23/b48fc63421d8efcea7c1dbc49f49dec1.png"
    ],
    [
        "name" => "国通快递",
        "wpCode" => "GTO",
        "unionWpCode" => LogisticsConst::GTO,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "//img.alicdn.com/tps/i3/T1ZQxYFT4cXXc_d6ve-160-80.jpg"
    ],
    [
        "name" => "全峰快递",
        "wpCode" => "QFKD",
        "unionWpCode" => LogisticsConst::QFKD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/tps/i1/T1VC47FFBaXXc_d6ve-160-80.jpg"
    ],
    [
        "name" => "EMS经济快递",
        "wpCode" => "EYB",
        "unionWpCode" => LogisticsConst::EMSJJ,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tps/i2/T1q1d6FKlaXXc_d6ve-160-80.jpg"
    ],
    [
        "name" => "卡行天下",
        "wpCode" => "CP457538",
        "unionWpCode" => LogisticsConst::KXTX,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1G8h9QQvoK1RjSZFDXXXY3pXa.png_200x200xz"
    ],
    [
        "name" => "苏宁快递",
        "wpCode" => "SNWL",
        "unionWpCode" => LogisticsConst::SNWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://gtms02.alicdn.com/tps/i2/TB1J8o2KVXXXXcqXFXXqxFJVFXX-100-100.png"
    ],
    [
        "name" => "申通快运",
        "wpCode" => "CN7000001017817",
        "unionWpCode" => LogisticsConst::STOKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://gtms01.alicdn.com/tps/i1/TB15wjcKVXXXXX9aXXXqxFJVFXX-100-100.png"
    ],
    [
        "name" => "如风达",
        "wpCode" => "*********",
        "unionWpCode" => LogisticsConst::RFD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1RK3sKVXXXXa9apXXSutbFXXX"
    ],
    [
        "name" => "百世云配",
        "wpCode" => "CP443514",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1.yIvxhTpK1RjSZFMXXbG_VXa.png_200x200xz"
    ],
    [
        "name" => "联邦快递",
        "wpCode" => "FEDEX",
        "unionWpCode" => LogisticsConst::FEDEX,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://gtms01.alicdn.com/tps/i1/TB1r96BKVXXXXbaXXXXqxFJVFXX-100-100.png"
    ],
    [
        "name" => "邮政标准快递", // 淘宝没找到这个？
        "wpCode" => "5000000007756",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "http://img.alicdn.com/tfscom/TB1OU9XNVXXXXb2aXXXwu0bFXXX_200x200xz"
    ],
    [
        "name" => "极兔速递",
        "wpCode" => "HTKY",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/7ef6ea0e08037a6dd287111bb496a710.png"
    ],
    [
        "name" => "天天快递",
        "wpCode" => "TTKDEX",
        "unionWpCode" => LogisticsConst::TT,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/e5366dca534d278c1568e587c613b054.png"
    ],
    [
        "name" => "优速快递",
        "wpCode" => "UC",
        "unionWpCode" => LogisticsConst::UC,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-04-15/b0c167b31b9036a63bf6e466e5d3ca8f.png"
    ],
    [
        "name" => "丰网速运",
        "wpCode" => "LE09252050",
        "unionWpCode" => LogisticsConst::FENGWANG,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://funimg.pddpic.com/2021-01-13/dade87f1-f36d-4afc-b932-019892283317.png"
    ],
    [
        "name" => "中铁快运",
        "wpCode" => "ZTKY",
        "unionWpCode" => LogisticsConst::ZTKY,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i1/6000000004542/O1CN01RY2GaP1jQHZbLGcAt_!!6000000004542-2-urc.png"
    ],
    [
        "name" => "1688货运",
        "wpCode" => "LE14322600",
        "unionWpCode" => LogisticsConst::HY1688,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://image.baidu.com/search/detail?ct=*********&z=undefined&tn=baiduimagedetail&ipn=d&word=1688%E8%B4%A7%E8%BF%90logo&step_word=&ie=utf-8&in=&cl=2&lm=-1&st=undefined&hd=undefined&latest=undefined&copyright=undefined&cs=1367784823,2035009375&os=1423574211,3403759495&simid=1367784823,2035009375&pn=0&rn=1&di=7117150749552803841&ln=1841&fr=&fmq=1661407557730_R&fm=&ic=undefined&s=undefined&se=&sme=&tab=0&width=undefined&height=undefined&face=undefined&is=0,0&istype=0&ist=&jit=&bdtype=0&spn=0&pi=0&gsm=0&objurl=https%3A%2F%2Fgimg2.baidu.com%2Fimage_search%2Fsrc%3Dhttp%253A%252F%252Fpic.51yuansu.com%252Fpic3%252Fcover%252F02%252F41%252F41%252F59e4d7337c3b3_610.jpg%26refer%3Dhttp%253A%252F%252Fpic.51yuansu.com%26app%3D2002%26size%3Df9999%2C10000%26q%3Da80%26n%3D0%26g%3D0n%26fmt%3Dauto%3Fsec%3D1663999557%26t%3D5172caaf848132f5318dfb78afc3b082&rpstart=0&rpnum=0&adpicid=0&nojc=undefined&dyTabStr=MCwzLDEsNiw0LDUsNyw4LDIsOQ%3D%3D"
    ],
    [
        "name" => "日日顺",
        "wpCode" => "*********",
        "unionWpCode" => LogisticsConst::RRS,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://t10.baidu.com/it/u=*********,1474463797&fm=58"
    ],
    [
        "name" => "快弟来了",
        "wpCode" => "**********",
        "unionWpCode" => LogisticsConst::KDLL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i2/O1CN01vDaxAi1W4fzSvj9LJ_!!6000000002735-2-tps-1710-606.png"
    ],
    [
        "name" => "韵达同城",
        "wpCode" => "**********",
        "unionWpCode" => LogisticsConst::YDTC,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i1/O1CN01aInEYz27DxjIlgnIw_!!6000000007764-2-tps-1765-607.png"
    ],
    [
        "name" => "壹米滴答",
        "wpCode" => "2744832184_543",
        "unionWpCode" => LogisticsConst::YMDD,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i4/O1CN01tqtpWo1UHNkx5TnZ6_!!6000000002492-2-tps-778-305.png"
    ],
    [
        "name" => "安迅物流",
        "wpCode" => "21000026002",
        "unionWpCode" => LogisticsConst::AXWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i2/O1CN01CECq8V1qKVz41GlCa_!!6000000005477-2-tps-2896-636.png"
    ],
    [
        "name" => "京广速递",
        "wpCode" => "CP449455",
        "unionWpCode" => LogisticsConst::SZKKE,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i1/6000000004068/O1CN01gWAqAq1fvBq9xT39j_!!6000000004068-0-urc.jpg"
    ],
    [
        "name" => "远成快运",
        "wpCode" => "2460304407_385",
        "unionWpCode" => LogisticsConst::SZKKE,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i1/6000000004068/O1CN01gWAqAq1fvBq9xT39j_!!6000000004068-0-urc.jpg"
    ],
    [
        "name" => "九曳",
        "wpCode" => "2383545689_32",
        "unionWpCode" => LogisticsConst::JIUYE,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i4/O1CN01onywBH1QIWHMmwLIp_!!6000000001953-2-tps-640-224.png"
    ],
    [
        "name" => "加运美快运",
        "wpCode" => "CP446169",
        "unionWpCode" => LogisticsConst::JIUYE,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i3/O1CN0104ba3Z1c5UHUyNecO_!!6000000003549-2-tps-593-466.png"
    ],
    [
        "name" => "速腾物流",
        "wpCode" => "CN7000001028572",
        "unionWpCode" => LogisticsConst::STWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i2/O1CN01kOZ2f41YDxCZ2T1dV_!!6000000003026-2-tps-1365-469.png"
    ],
    [
        "name" => "中通冷链",
        "wpCode" => "LE14066700",
        "unionWpCode" => LogisticsConst::STWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i2/O1CN01OXx9zj1XRPH2oHoy2_!!6000000002920-2-tps-2084-648.png"
    ],
    [
        "name" => "中铁智慧物流",
        "wpCode" => "LE32538030",
        "unionWpCode" => LogisticsConst::ZTWL,
        "type" => \App\Models\Company::TYPE_TB,
        "logo" => "https://img.alicdn.com/imgextra/i4/O1CN01Tph4Oe24JVwha63h4_!!6000000007370-2-tps-856-400.png"
    ],

    //抖音
    [
        "name" => "中铁智慧物流",
        "wpCode" => "ZTZHWL",
        "unionWpCode" => LogisticsConst::ZTWL,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://img.alicdn.com/imgextra/i4/O1CN01Tph4Oe24JVwha63h4_!!6000000007370-2-tps-856-400.png"
    ],
    [
        "name" => "邮政电商标快",
        "wpCode" => "yzdsbk",
        "unionWpCode" => LogisticsConst::EYB,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://img.pddpic.com/a/package-advertising/2bfa06d7-1b1f-4df2-b1be-4b887cb56bdb.jpg"
    ],
    [
        "name" => "安能物流",
        "wpCode" => "annengwuliu",
        "unionWpCode" => LogisticsConst::ANKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf3-cm.ecombdstatic.com/obj/ecom-logistics-track/11ddf6c18a6e708cd511b18a298a2bb9",
        "parentPart" => 1,
    ],
    [
        "name" => "丹鸟",
        "wpCode" => "danniao",
        "unionWpCode" => LogisticsConst::DANNIAO,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "http://img.alicdn.com/tfscom/TB1daEjXAxz61VjSZFtXXaDSVXa.png_200x200xz",
        "parentPart" => 1,
    ],
    [
        "name" => "韵达快运",
        "wpCode" => "yundakuaiyun",
        "unionWpCode" => LogisticsConst::YDKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/print_template/2019-06-26/e8b9f6f1f26b3c820693428e6dbc6f0e.png",
        "parentPart" => 1,
    ],
    [
        "name" => "中通快递",
        "wpCode" => "zhongtong",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "韵达快递",
        "wpCode" => "yunda",
        "unionWpCode" => LogisticsConst::YUNDA,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "极兔快递",
        "wpCode" => "jtexpress",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "yuantong",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "邮政快递包裹",
        "wpCode" => "youzhengguonei",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ],
    [
        "name" => "EMS",
        "wpCode" => "ems",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "百世快递",
        "wpCode" => "huitongkuaidi",
        "unionWpCode" => LogisticsConst::BESTKD,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/7ef6ea0e08037a6dd287111bb496a710.png"
    ],
    [
        "name" => "京东快运",
        "wpCode" => "jingdongkuaiyun",
        "unionWpCode" => LogisticsConst::JDKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf26-cm.ecombdstatic.com/obj/ecom-logistics-track/fa80cda98aa9fd09d9dd12cd82faafd4",
        "parentPart" => 1,
    ],
    [
        "name" => "京东快递",
        "wpCode" => "jd",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/d782008eae0f2873c0de2ca67e9e9fe3",
        "parentPart" => 1,
    ],
    [
        "name" => "申通快递",
        "wpCode" => "shentong",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "顺丰快递",
        "wpCode" => "shunfeng",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png",
        "parentPart" => 1,
    ],
    [
        "name" => "众邮快递",
        "wpCode" => "zhongyouex",
        "unionWpCode" => LogisticsConst::ZYKD,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://sf3-ecomcdn-tos.pstatp.com/obj/power/Lark20200515-113833.png"
    ],
    [
        "name" => "丰网速运",
        "wpCode" => "fengwang",
        "unionWpCode" => LogisticsConst::FENGWANG,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/7db92d1e790d9fb8081b0e3ff551fc3e"
    ],
    [
        "name" => "德邦快递",
        "wpCode" => "debangwuliu",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/deppon_logo.jpeg",
        "parentPart" => 1,
    ],
    [
        "name" => "德邦快运",
        "wpCode" => "debangkuaiyun",
        "unionWpCode" => LogisticsConst::DBKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/2f969ff09871a2ba7c9cb45da8e455df",
        "parentPart" => 1,
    ],
    [
        "name" => "优速物流",
        "wpCode" => "youshuwuliu",
        "unionWpCode" => LogisticsConst::UC,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://sf3-ecomcdn-tos.pstatp.com/obj/temai/FnKTtGDvC25bB7jf1tflqB5x-Mgm.png",
        "parentPart" => 1,
    ],
    [
        "name" => "顺丰快运",
        "wpCode" => "shunfengkuaiyun",
        "unionWpCode" => LogisticsConst::SFKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png",
        "parentPart" => 1,
    ],
    [
        "name" => "中通快运",
        "wpCode" => "zhongtongkuaiyun",
        "unionWpCode" => LogisticsConst::ZTOKY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf26-cm.ecombdstatic.com/obj/ecom-logistics-track/Fi8yT_yCAO9kbTWfG1Kn67DMZiEt.png",
        "parentPart" => 1,
    ],
    [
        "name" => "九曳供应链",
        "wpCode" => "jiuyescm",
        "unionWpCode" => LogisticsConst::JIUYE,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf3-cm.ecombdstatic.com/obj/ecom-logistics-track/e0ac5b6c3377b8ee1a640f3bff19bbd3"
    ],
    [
        "name" => "苏宁物流",
        "wpCode" => "suning",
        "unionWpCode" => LogisticsConst::SNWL,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf3-cm.ecombdstatic.com/obj/ecom-logistics-track/e0ac5b6c3377b8ee1a640f3bff19bbd3",
        "parentPart" => 1,
    ],
    [
        "name" => "D速物流",
        "wpCode" => "dsukuaidi",
        "unionWpCode" => LogisticsConst::SDSD,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf26-cm.ecombdstatic.com/obj/ecom-logistics-track/cfa6bf200489c5083146f93372e464e0",
        "parentPart" => 1,
    ],
    [
        "name" => "快弟来了",
        "wpCode" => "xlair",
        "unionWpCode" => LogisticsConst::KDLL,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf3-cm.ecombdstatic.com/obj/ecom-logistics-track/eddfa18aa828b70701c81d7d3a2e1d6c",
        "parentPart" => 1,
    ],
    [
        "name" => "哪吒速运",
        "wpCode" => "NZSY",
        "unionWpCode" => LogisticsConst::NZSY,
        "type" => \App\Models\Company::TYPE_DY,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/31340b8034dafefce9f52393d53bc604",
        "parentPart" => 1,
    ],

    //京东
//    [
//        "name" => "平安达腾飞快递",
//        "wpCode" => "PADTFKD",
//        "unionWpCode" => LogisticsConst::PADTFKD,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2181069.jpg"
//    ], [
//        "name" => "中铁智慧物流",
//        "wpCode" => "ZTFBKY",
//        "unionWpCode" => LogisticsConst::ZTFBKY,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2852539.jpg"
//    ],
//    [
//        "name" => "汇森速运",
//        "wpCode" => "HSSY",
//        "unionWpCode" => LogisticsConst::HSSY,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2134895.jpg"
//    ],
    [
        "name" => "京广速递",
        "wpCode" => "JGSD",
        "unionWpCode" => LogisticsConst::SZKKE,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/2133515.jpg"
    ],
//    [
//        "name" => "中通国际",
//        "wpCode" => "ZTOINT",
//        "unionWpCode" => LogisticsConst::ZTOINT,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2133093.jpg"
//    ],
//    [
//        "name" => "速腾快递",
//        "wpCode" => "STKD",
//        "unionWpCode" => LogisticsConst::STKD,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2100813.jpg"
//    ],
//    [
//        "name" => "加运美",
//        "wpCode" => "JYM",
//        "unionWpCode" => LogisticsConst::JYM,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2100547.jpg"
//    ],
    [
        "name" => "壹米滴答",
        "wpCode" => "YMDD",
        "unionWpCode" => LogisticsConst::YMDD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1325880.jpg"
    ], [
        "name" => "极兔速递",
        "wpCode" => "JTSD",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1255654.jpg"
    ], [
        "name" => "众邮快递",
        "wpCode" => "ZYKD",
        "unionWpCode" => LogisticsConst::ZYKD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/881232.jpg"
    ], [
        "name" => "顺心捷达",
        "wpCode" => "SXJD",
        "unionWpCode" => LogisticsConst::SXJD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/832230.jpg"
    ], [
        "name" => "韵达快运",
        "wpCode" => "YDKY",
        "unionWpCode" => LogisticsConst::YDKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/731302.jpg"
    ], [
        "name" => "中通快运",
        "wpCode" => "ZTO56",
        "unionWpCode" => LogisticsConst::ZTKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/680414.jpg"
    ], [
        "name" => "亚风快运",
        "wpCode" => "AF",
        "unionWpCode" => LogisticsConst::AIR,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/323141.jpg"
    ], [
        "name" => "安能物流",
        "wpCode" => "ANE",
        "unionWpCode" => LogisticsConst::ANKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/4832.jpg"
    ], [
        "name" => "百世快运",
        "wpCode" => "BESTJD",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1748.jpg"
    ], [
        "name" => "优速",
        "wpCode" => "UC",
        "unionWpCode" => LogisticsConst::UC,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1747.jpg"
    ], [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1499.jpg"
    ], [
        "name" => "韵达快递",
        "wpCode" => "YUNDA",
        "unionWpCode" => LogisticsConst::YUNDA,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1434698.jpg"
    ], [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/470.jpg"
    ], [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/463.jpg"
    ],
//    [
//        "name" => "红背心",
//        "wpCode" => "HBX",
//        "unionWpCode" => LogisticsConst::HBX,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/2134079.jpg"
//    ],
    [
        "name" => "邮政电商标快",
        "wpCode" => "DSBK",
        "unionWpCode" => LogisticsConst::EYB,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/2124945.jpg"
    ], [
        "name" => "德邦快运",
        "wpCode" => "DDKY",
        "unionWpCode" => LogisticsConst::DBKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/2119699.jpg"
    ], [
        "name" => "达达秒送",
        "wpCode" => "DDTCKS",
        "unionWpCode" => LogisticsConst::DDTCKS,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1999327.jpg"
    ],
//    [
//        "name" => "京东医药",
//        "wpCode" => "JDYY",
//        "unionWpCode" => LogisticsConst::JDYY,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/1613410.jpg"
//    ],
//    [
//        "name" => "海信物流",
//        "wpCode" => "SAVOR",
//        "unionWpCode" => LogisticsConst::SAVOR,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/835370.jpg"
//    ],
    [
        "name" => "京东快运",
        "wpCode" => "JDKY",
        "unionWpCode" => LogisticsConst::JDKY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/773574.jpg"
    ], [
        "name" => "跨越速运",
        "wpCode" => "KYE",
        "unionWpCode" => LogisticsConst::KYSY,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/599866.jpg"
    ],
//    [
//        "name" => "京东大件物流",
//        "wpCode" => "JDDJ",
//        "unionWpCode" => LogisticsConst::JDDJ,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/336878.jpg"
//    ],
//    [
//        "name" => "安得物流",
//        "wpCode" => "ANNTO",
//        "unionWpCode" => LogisticsConst::ANNTO,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/247899.jpg"
//    ],
//    [
//        "name" => "贝业新兄弟",
//        "wpCode" => "BYL",
//        "unionWpCode" => LogisticsConst::BYL,
//        "type" => \App\Models\Company::TYPE_JD,
//        "logo" => "https://wl.jd.com/static/providerLogoImage/222693.jpg"
//    ],
    [
        "name" => "EMS",
        "wpCode" => "EMSBZ",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/3668.jpg"
    ], [
        "name" => "德邦快递",
        "wpCode" => "DBKD",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/3046.jpg"
    ], [
        "name" => "邮政快递包裹",
        "wpCode" => "ZGYZZHDD",
        "unionWpCode" => LogisticsConst::ZGYZZHDD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/2170.jpg"
    ], [
        "name" => "京东快递",
        "wpCode" => "JD",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/2087.jpg"
    ], [
        "name" => "宅急送",
        "wpCode" => "ZJS",
        "unionWpCode" => LogisticsConst::ZJS,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/1409.jpg"
    ], [
        "name" => "顺丰快递",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_JD,
        "logo" => "https://wl.jd.com/static/providerLogoImage/467.jpg"
    ],
    //微信
    [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "韵达快递",
        "wpCode" => "YD",
        "unionWpCode" => LogisticsConst::YUNDA,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "极兔快递",
        "wpCode" => "JTSD",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "邮政快递包裹",
        "wpCode" => "YZPY",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ],
    [
        "name" => "EMS",
        "wpCode" => "EMS",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "百世快递",
        "wpCode" => "HTKY",
        "unionWpCode" => LogisticsConst::BESTKD,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-03/7ef6ea0e08037a6dd287111bb496a710.png"
    ],
    [
        "name" => "京东快递",
        "wpCode" => "JD",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/d782008eae0f2873c0de2ca67e9e9fe3"
    ],
    [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "顺丰快递",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ],
    [
        "name" => "丰网速运",
        "wpCode" => "FWX",
        "unionWpCode" => LogisticsConst::FENGWANG,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/7db92d1e790d9fb8081b0e3ff551fc3e"
    ],
    [
        "name" => "德邦快递",
        "wpCode" => "DBL",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/deppon_logo.jpeg"
    ],
    [
        "name" => "优速物流",
        "wpCode" => "UC",
        "unionWpCode" => LogisticsConst::UC,
        "type" => \App\Models\Company::TYPE_WX,
        "logo" => "https://sf3-ecomcdn-tos.pstatp.com/obj/temai/FnKTtGDvC25bB7jf1tflqB5x-Mgm.png"
    ],
    //微信视频
    [
        "name" => "韵达快递",
        "wpCode" => "YUNDA",
        "unionWpCode" => LogisticsConst::YUNDA,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "极兔快递",
        "wpCode" => "JTSD",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ],
    [
        "name" => "顺丰速运",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ],
    [
        "name" => "京东",
        "wpCode" => "JD",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/d782008eae0f2873c0de2ca67e9e9fe3"
    ],
    [
        "name" => "中国邮政",
        "wpCode" => "EMS",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "菜鸟速递(丹鸟)",
        "wpCode" => "CNSD",
        "unionWpCode" => LogisticsConst::DANNIAO,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://res.wx.qq.com/shop/public/2023-11-20/a5dfb46c-b699-4e72-9e7d-9dba84e57060.png"
    ],
    [
        "name" => "德邦快递",
        "wpCode" => "DBKD",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_WXSP,
        "logo" => "https://res.wx.qq.com/shop/public/2023-12-07/10de0414-b805-4a92-a8ae-dad2cd26381f.png"
    ],

    //快手
    [
        "name" => "邮政电商标快",
        "wpCode" => "POST_DSBK",
        "unionWpCode" => LogisticsConst::EYB,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://img.pddpic.com/a/package-advertising/2bfa06d7-1b1f-4df2-b1be-4b887cb56bdb.jpg"
    ],
    [
        "name" => "百世快运",
        "wpCode" => "BEST800_LOGISTICS",
        "unionWpCode" => LogisticsConst::BESTKY,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://u2-401.ecukwai.com/kos/nlav11586/ecompany/BEST800.jpg?x-oss-process=image/resize,m_mfit,w_210,h_90"
    ],
    [
        "name" => "中通快递",
        "wpCode" => "ZTO",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ],
    [
        "name" => "韵达快递",
        "type" => \App\Models\Company::TYPE_KS,
        "wpCode" => "YUNDA",
        "unionWpCode" => LogisticsConst::YUNDA,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ],
    [
        "name" => "圆通快递",
        "wpCode" => "YTO",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ],
    [
        "name" => "申通快递",
        "wpCode" => "STO",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ],
    [
        "name" => "极兔快递",
        "wpCode" => "JT",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ],
    [
        "name" => "邮政快递包裹",
        "wpCode" => "POSTB",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ],
    [
        "name" => "顺丰",
        "wpCode" => "SF",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ],
    [
        "name" => "EMS",
        "wpCode" => "EMS",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ],
    [
        "name" => "京东",
        "wpCode" => "JD",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_KS,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/d782008eae0f2873c0de2ca67e9e9fe3"
    ],
    [
        "name" => "顺丰速运",
        "wpCode" => "shunfeng",
        "unionWpCode" => LogisticsConst::SF,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://testimg.yangkeduo.com/express/2018-07-19/c0215420d8466459115fa5fd30035df5.png"
    ], [
        "name" => "中通速递",
        "wpCode" => "zto",
        "unionWpCode" => LogisticsConst::ZTO,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/c3d5eebec471a5c5536e3cad7dc02a21.png"
    ], [
        "name" => "圆通速递",
        "wpCode" => "yuantong",
        "unionWpCode" => LogisticsConst::YTO,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-16/a5015c87a6a4b51ad01df2f73502e317.png"
    ], [
        "name" => "韵达速递",
        "wpCode" => "yunda",
        "unionWpCode" => LogisticsConst::YUNDA,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/546c001d958ffedef1ad7fc53ea3d4f2.png"
    ], [
        "name" => "极兔速递",
        "wpCode" => "jtexpress",
        "unionWpCode" => LogisticsConst::JT,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2020-02-03/dc6822ad-9415-4164-9f11-d5a48c341aac.png"
    ], [
        "name" => "申通速递",
        "wpCode" => "shentong",
        "unionWpCode" => LogisticsConst::STO,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-01-17/172779e0c4587a929d9bc9495c9cdd66.png"
    ], [
        "name" => "邮政快递",
        "wpCode" => "youzhengguonei",
        "unionWpCode" => LogisticsConst::POSTB,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://pinduoduoimg.yangkeduo.com/msfe/2019-02-24/c6eb4dbf541ac75e8c7497319dd09a1c.png"
    ], [
        "name" => "邮政电商标快",
        "wpCode" => "youzhengbiaokuai",
        "unionWpCode" => LogisticsConst::EYB,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://img.pddpic.com/a/package-advertising/2bfa06d7-1b1f-4df2-b1be-4b887cb56bdb.jpg"
    ], [
        "name" => "EMS",
        "wpCode" => "ems",
        "unionWpCode" => LogisticsConst::EMS,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://t16img.yangkeduo.com/mms_static/2019-09-06/518a616b-a34a-413b-89d4-cce6e7334183.png"
    ], [
        "name" => "德邦快递",
        "wpCode" => "debangwuliu",
        "unionWpCode" => LogisticsConst::DBKD,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/deppon_logo.jpeg"
    ], [
        "name" => "京东物流",
        "wpCode" => "jd",
        "unionWpCode" => LogisticsConst::JD,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://lf6-cm.ecombdstatic.com/obj/ecom-logistics-track/d782008eae0f2873c0de2ca67e9e9fe3"
    ], [
        "name" => "菜鸟速递",
        "wpCode" => "danniao",
        "unionWpCode" => LogisticsConst::DANNIAO,
        "type" => \App\Models\Company::TYPE_XHS,
        "logo" => "https://img.pddpic.com/a/package-advertising/2bfa06d7-1b1f-4df2-b1be-4b887cb56bdb.jpg"
    ],
];
