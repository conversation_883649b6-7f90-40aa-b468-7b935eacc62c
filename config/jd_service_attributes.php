<?php
/**
 * 京东的增值服务
 */

use App\Constants\WaybillConst;

return [
    //中通
    'ZTO' => [
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'COD',
        ],
        [
            'required' => false,
            'service_desc' => '中通COD',
            'service_name' => '中通COD',
            'service_code' => 'T1COD',
        ],
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":1,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '到付',
            'service_name' => '到付',
            'service_code' => 'DFService',
            'service_attributes' => [
                [
                    'attribute_name' => '到付',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value',
                    'type_desc' => '{"EMPTY_STRING":"固定值"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '中通好快 ',
            'service_name' => '中通好快 ',
            'service_code' => 'BKService',
            'service_attributes' => [
                [
                    'attribute_name' => '中通好快',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"EMPTY_STRING":"固定值"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '中通飞快 ',
            'service_name' => '中通飞快 ',
            'service_code' => 'TKService',
            'service_attributes' => [
                [
                    'attribute_name' => '中通飞快',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"EMPTY_STRING":"固定值"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '中通标快 ',
            'service_name' => '中通标快 ',
            'service_code' => 'SVC-VIP',
            'service_attributes' => [
                [
                    'attribute_name' => '中通标快',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value',
                    'type_desc' => '{"EMPTY_STRING":"固定值"}'
                ]
            ]
        ],
    ],
    //顺丰
    "SF" => [
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' =>  preg_replace('/\s/','',  WaybillConst::SF_PRODUCT_TYPE)

                ]
            ]
        ],
    ]

];
